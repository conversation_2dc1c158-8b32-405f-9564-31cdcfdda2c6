/**
 * Single-Pass File Operations
 * 
 * Provides file operation schema and utilities for single-pass mode
 * Supports atomic file operations with validation and rollback
 */

import { z } from 'zod';
import { writeFileSync, readFileSync, unlinkSync, renameSync, existsSync } from 'fs';
import { dirname, resolve, join } from 'path';
import { mkdirSync } from 'fs';
import { logInfo, logError } from '../logger/log.js';

/**
 * File operation schema for validation
 */
export const FileOperationSchema = z.object({
  path: z.string(),
  updated_full_content: z.string().nullable().optional(),
  delete: z.boolean().nullable().optional(),
  move_to: z.string().nullable().optional(),
});

export type FileOperation = z.infer<typeof FileOperationSchema>;

export interface FileOperationResult {
  success: boolean;
  path: string;
  operation: 'create' | 'update' | 'delete' | 'move';
  error?: string;
  backup?: string;
}

export interface FileOperationContext {
  rootPath: string;
  dryRun?: boolean;
  createBackups?: boolean;
  backupDir?: string;
}

/**
 * Execute a batch of file operations atomically
 */
export async function executeFileOperations(
  operations: FileOperation[],
  context: FileOperationContext
): Promise<{
  success: boolean;
  results: FileOperationResult[];
  error?: string;
}> {
  const results: FileOperationResult[] = [];
  const backups: Array<{ original: string; backup: string }> = [];

  try {
    // Validate all operations first
    for (const operation of operations) {
      const validation = FileOperationSchema.safeParse(operation);
      if (!validation.success) {
        throw new Error(`Invalid operation for ${operation.path}: ${validation.error.message}`);
      }
    }

    // Execute operations
    for (const operation of operations) {
      const result = await executeFileOperation(operation, context);
      results.push(result);

      if (!result.success) {
        throw new Error(`Operation failed for ${operation.path}: ${result.error}`);
      }

      if (result.backup) {
        backups.push({ original: operation.path, backup: result.backup });
      }
    }

    logInfo('File operations completed successfully', {
      operationCount: operations.length,
      backupCount: backups.length
    });

    return { success: true, results };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logError('File operations failed, rolling back', new Error(errorMessage));

    // Rollback operations
    await rollbackOperations(results, backups);

    return {
      success: false,
      results,
      error: errorMessage
    };
  }
}

/**
 * Execute a single file operation
 */
async function executeFileOperation(
  operation: FileOperation,
  context: FileOperationContext
): Promise<FileOperationResult> {
  const fullPath = resolve(context.rootPath, operation.path);
  
  try {
    // Determine operation type
    if (operation.delete) {
      return await deleteFile(fullPath, context);
    } else if (operation.move_to) {
      return await moveFile(fullPath, resolve(context.rootPath, operation.move_to), context);
    } else if (operation.updated_full_content !== undefined) {
      const exists = existsSync(fullPath);
      return await writeFile(
        fullPath, 
        operation.updated_full_content || '', 
        context,
        exists ? 'update' : 'create'
      );
    } else {
      throw new Error('No valid operation specified');
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return {
      success: false,
      path: operation.path,
      operation: 'update',
      error: errorMessage
    };
  }
}

/**
 * Write file with backup
 */
async function writeFile(
  filePath: string,
  content: string,
  context: FileOperationContext,
  operation: 'create' | 'update'
): Promise<FileOperationResult> {
  let backup: string | undefined;

  try {
    // Create backup if file exists and backups are enabled
    if (context.createBackups && existsSync(filePath)) {
      backup = await createBackup(filePath, context);
    }

    // Ensure directory exists
    const dir = dirname(filePath);
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
    }

    // Write file (dry run check)
    if (!context.dryRun) {
      writeFileSync(filePath, content, 'utf-8');
    }

    logInfo(`File ${operation}d`, { path: filePath, backup });

    return {
      success: true,
      path: filePath,
      operation,
      backup
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return {
      success: false,
      path: filePath,
      operation,
      error: errorMessage,
      backup
    };
  }
}

/**
 * Delete file with backup
 */
async function deleteFile(
  filePath: string,
  context: FileOperationContext
): Promise<FileOperationResult> {
  let backup: string | undefined;

  try {
    if (!existsSync(filePath)) {
      return {
        success: true,
        path: filePath,
        operation: 'delete'
      };
    }

    // Create backup if enabled
    if (context.createBackups) {
      backup = await createBackup(filePath, context);
    }

    // Delete file (dry run check)
    if (!context.dryRun) {
      unlinkSync(filePath);
    }

    logInfo('File deleted', { path: filePath, backup });

    return {
      success: true,
      path: filePath,
      operation: 'delete',
      backup
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return {
      success: false,
      path: filePath,
      operation: 'delete',
      error: errorMessage,
      backup
    };
  }
}

/**
 * Move file with backup
 */
async function moveFile(
  fromPath: string,
  toPath: string,
  context: FileOperationContext
): Promise<FileOperationResult> {
  let backup: string | undefined;

  try {
    if (!existsSync(fromPath)) {
      throw new Error('Source file does not exist');
    }

    // Create backup of destination if it exists
    if (context.createBackups && existsSync(toPath)) {
      backup = await createBackup(toPath, context);
    }

    // Ensure destination directory exists
    const dir = dirname(toPath);
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
    }

    // Move file (dry run check)
    if (!context.dryRun) {
      renameSync(fromPath, toPath);
    }

    logInfo('File moved', { from: fromPath, to: toPath, backup });

    return {
      success: true,
      path: fromPath,
      operation: 'move',
      backup
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return {
      success: false,
      path: fromPath,
      operation: 'move',
      error: errorMessage,
      backup
    };
  }
}

/**
 * Create backup of a file
 */
async function createBackup(
  filePath: string,
  context: FileOperationContext
): Promise<string> {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = context.backupDir || join(context.rootPath, '.kritrima-backups');
  const backupName = `${timestamp}-${filePath.replace(/[/\\]/g, '_')}`;
  const backupPath = join(backupDir, backupName);

  // Ensure backup directory exists
  if (!existsSync(backupDir)) {
    mkdirSync(backupDir, { recursive: true });
  }

  // Copy file to backup location
  const content = readFileSync(filePath);
  writeFileSync(backupPath, content);

  return backupPath;
}

/**
 * Rollback operations using backups
 */
async function rollbackOperations(
  results: FileOperationResult[],
  backups: Array<{ original: string; backup: string }>
): Promise<void> {
  try {
    // Restore from backups
    for (const { original, backup } of backups) {
      if (existsSync(backup)) {
        const content = readFileSync(backup);
        writeFileSync(original, content);
        unlinkSync(backup);
      }
    }

    // Remove created files
    for (const result of results) {
      if (result.success && result.operation === 'create' && existsSync(result.path)) {
        unlinkSync(result.path);
      }
    }

    logInfo('Operations rolled back successfully');

  } catch (error) {
    logError('Failed to rollback operations', error instanceof Error ? error : new Error(String(error)));
  }
}
