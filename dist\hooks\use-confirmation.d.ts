import React from 'react';
export interface ConfirmationResult {
    confirmed: boolean;
    value?: string;
    action?: string;
}
export declare function useConfirmation(): {
    submitConfirmation: (result: ConfirmationResult) => void;
    requestConfirmation: (prompt: React.ReactElement, explanation?: string) => Promise<ConfirmationResult>;
    confirmationPrompt: React.ReactElement | null;
    explanation?: string;
    isConfirmationPending: boolean;
};
//# sourceMappingURL=use-confirmation.d.ts.map