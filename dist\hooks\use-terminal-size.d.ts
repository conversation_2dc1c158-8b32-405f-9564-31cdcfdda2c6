interface TerminalSize {
    columns: number;
    rows: number;
    width: number;
    height: number;
}
export declare function useTerminalSize(): TerminalSize;
export declare function getTerminalSize(): TerminalSize;
export declare function isTerminalSizeAdequate(minColumns?: number, minRows?: number): boolean;
export declare function getResponsiveLayout(): {
    isSmall: boolean;
    isMedium: boolean;
    isLarge: boolean;
    layout: 'compact' | 'normal' | 'expanded';
};
export {};
//# sourceMappingURL=use-terminal-size.d.ts.map