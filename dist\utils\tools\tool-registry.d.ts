export interface Tool {
    id: string;
    name: string;
    description: string;
    category: 'file' | 'git' | 'system' | 'network' | 'analysis' | 'development';
    version: string;
    parameters: ToolParameter[];
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    requiresApproval: boolean;
    execute: (params: Record<string, any>, context: ToolContext) => Promise<ToolResult>;
}
export interface ToolParameter {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'array' | 'object';
    description: string;
    required: boolean;
    default?: any;
    validation?: (value: any) => boolean;
}
export interface ToolContext {
    workingDirectory: string;
    userId?: string;
    sessionId?: string;
    environment: Record<string, string>;
    capabilities: string[];
}
export interface ToolResult {
    success: boolean;
    data?: any;
    output?: string;
    error?: string;
    metadata?: Record<string, any>;
    executionTime: number;
}
export declare class ToolRegistry {
    private tools;
    private executionHistory;
    constructor();
    registerTool(tool: Tool): void;
    getTool(id: string): Tool | undefined;
    getAllTools(): Tool[];
    getToolsByCategory(category: Tool['category']): Tool[];
    executeTool(toolId: string, parameters: Record<string, any>, context: ToolContext): Promise<ToolResult>;
    private validateParameters;
    private validateParameterType;
    private registerBuiltinTools;
    getExecutionHistory(): ToolExecution[];
    clearExecutionHistory(): void;
    getUsageStatistics(): Record<string, number>;
}
interface ToolExecution {
    toolId: string;
    parameters: Record<string, any>;
    result: ToolResult;
    context: ToolContext;
    timestamp: string;
    executionTime: number;
}
export declare const toolRegistry: ToolRegistry;
export declare function executeTool(toolId: string, parameters?: Record<string, any>, context?: Partial<ToolContext>): Promise<ToolResult>;
export declare function getAvailableTools(): Tool[];
export {};
//# sourceMappingURL=tool-registry.d.ts.map