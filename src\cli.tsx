#!/usr/bin/env node

/**
 * Kritrima AI CLI - Main Entry Point
 * 
 * Handles argument parsing, configuration loading, and application initialization
 * Provides multiple modes: interactive chat, single-pass, and configuration
 */

import { Command } from 'commander';
import chalk from 'chalk';
import { CLI_VERSION } from './version.js';
import { loadConfig, getApiKey } from './utils/config.js';
import { getAvailableProviders } from './utils/providers.js';
import { checkForUpdates } from './utils/check-updates.js';
import { App } from './app.js';
import type { ApprovalPolicy, ProviderName } from './types/index.js';

// Create commander program
const program = new Command();

program
  .name('kritrima-ai')
  .description('Sophisticated AI-powered CLI assistant with multi-provider support')
  .version(CLI_VERSION)
  .option('-m, --model <model>', 'AI model to use')
  .option('-p, --provider <provider>', 'AI provider to use')
  .option('-a, --approval <mode>', 'Approval mode: suggest, auto-edit, full-auto')
  .option('-c, --config <path>', 'Configuration file path')
  .option('-d, --debug', 'Enable debug logging')
  .option('-v, --verbose', 'Verbose output')
  .option('--no-notifications', 'Disable desktop notifications')
  .option('--no-updates', 'Skip update check')
  .option('--full-context', 'Enable experimental full-context mode')
  .option('--single-pass', 'Run in single-pass mode')
  .option('--list-providers', 'List available providers')
  .option('--list-models', 'List available models for provider')
  .option('--test-connection', 'Test connection to AI provider')
  .argument('[prompt]', 'Initial prompt for single-pass mode');

/**
 * Main CLI function
 */
async function main() {
  try {
    // Parse command line arguments
    program.parse();
    const options = program.opts();
    const args = program.args;

    // Handle special commands first (no config needed)
    if (options.listProviders) {
      await handleListProviders();
      return;
    }

    if (options.listModels) {
      await handleListModels(options.provider);
      return;
    }

    if (options.testConnection) {
      await handleTestConnection(options.provider);
      return;
    }

    // Load configuration
    const config = loadConfig();

    // Apply command line overrides
    if (options.model) {
      config.model = options.model;
    }
    if (options.provider) {
      config.provider = options.provider as ProviderName;
    }
    if (options.approval) {
      config.approvalMode = options.approval as ApprovalPolicy;
    }
    if (options.debug) {
      config.enableLogging = true;
    }
    if (options.noNotifications) {
      config.enableNotifications = false;
    }

    // Only validate configuration for modes that need it
    const needsValidation = options.singlePass || args.length > 0 || (!options.singlePass && args.length === 0);
    if (needsValidation) {
      await validateConfiguration(config);
    }

    // Check for updates (unless disabled)
    if (!options.noUpdates) {
      await checkForUpdates().catch(() => {
        // Silently fail update check
      });
    }

    // Determine mode
    if (options.singlePass || args.length > 0) {
      // Single-pass mode
      const prompt = args.join(' ');
      if (!prompt) {
        console.error(chalk.red('Error: Prompt required for single-pass mode'));
        process.exit(1);
      }
      
      await runSinglePass(prompt, config, options);
    } else {
      // Interactive mode
      await runInteractive(config, options);
    }

  } catch (error) {
    console.error(chalk.red('Error:'), error instanceof Error ? error.message : 'Unknown error');

    const opts = program.opts();
    if (opts.verbose || opts.debug) {
      console.error(chalk.gray('Stack trace:'));
      console.error(error instanceof Error ? error.stack : error);
    }
    
    process.exit(1);
  }
}

/**
 * Handle --list-providers command
 */
async function handleListProviders() {
  console.log(chalk.blue('Available AI Providers:'));
  console.log();

  const providers = getAvailableProviders();
  
  for (const provider of providers) {
    const hasApiKey = !!getApiKey(provider);
    const status = hasApiKey ? chalk.green('✓') : chalk.red('✗');
    
    console.log(`  ${status} ${provider.padEnd(12)} - ${hasApiKey ? 'API key configured' : 'API key missing'}`);
  }
  
  console.log();
  console.log(chalk.gray('Set API keys using environment variables (e.g., OPENAI_API_KEY)'));
}

/**
 * Handle --list-models command
 */
async function handleListModels(provider?: string) {
  const config = loadConfig();
  const targetProvider = provider || config.provider;
  
  console.log(chalk.blue(`Available models for ${targetProvider}:`));
  console.log();

  try {
    const { fetchModels } = await import('./utils/model-utils.js');
    const models = await fetchModels(targetProvider);
    
    for (const model of models) {
      console.log(`  • ${model}`);
    }
    
    if (models.length === 0) {
      console.log(chalk.yellow('  No models found or provider not accessible'));
    }
    
  } catch (error) {
    console.error(chalk.red('Error fetching models:'), error instanceof Error ? error.message : 'Unknown error');
    process.exit(1);
  }
}

/**
 * Handle --test-connection command
 */
async function handleTestConnection(provider?: string) {
  const config = loadConfig();
  const targetProvider = provider || config.provider;
  
  console.log(chalk.blue(`Testing connection to ${targetProvider}...`));

  try {
    const { testClientConnection } = await import('./utils/openai-client.js');
    const result = await testClientConnection(targetProvider);
    
    if (result.success) {
      console.log(chalk.green('✓ Connection successful'));
    } else {
      console.log(chalk.red('✗ Connection failed:'), result.error);
      process.exit(1);
    }
    
  } catch (error) {
    console.error(chalk.red('✗ Connection failed:'), error instanceof Error ? error.message : 'Unknown error');
    process.exit(1);
  }
}

/**
 * Validate configuration
 */
async function validateConfiguration(config: any) {
  // Check API key
  const apiKey = getApiKey(config.provider);
  if (!apiKey) {
    console.error(chalk.red(`Error: No API key found for provider '${config.provider}'`));
    console.error(chalk.gray(`Please set the ${config.provider.toUpperCase()}_API_KEY environment variable`));
    process.exit(1);
  }

  // Validate approval mode
  const validApprovalModes = ['suggest', 'auto-edit', 'full-auto'];
  if (!validApprovalModes.includes(config.approvalMode)) {
    console.error(chalk.red(`Error: Invalid approval mode '${config.approvalMode}'`));
    console.error(chalk.gray(`Valid modes: ${validApprovalModes.join(', ')}`));
    process.exit(1);
  }
}

/**
 * Run in single-pass mode
 */
async function runSinglePass(prompt: string, config: any, options: any) {
  console.log(chalk.blue('Running in single-pass mode...'));
  console.log(chalk.gray(`Prompt: ${prompt}`));
  console.log();

  try {
    // Import single-pass module
    const { runSinglePass: executeSinglePass } = await import('./cli-singlepass.js');
    
    await executeSinglePass({
      originalPrompt: prompt,
      config,
      rootPath: process.cwd(),
      fullContext: options.fullContext
    });
    
  } catch (error) {
    console.error(chalk.red('Single-pass execution failed:'), error instanceof Error ? error.message : 'Unknown error');
    process.exit(1);
  }
}

/**
 * Run in interactive mode
 */
async function runInteractive(config: any, options: any) {
  // Check if we're in a git repository
  const { checkInGit } = await import('./utils/check-in-git.js');
  const inGit = checkInGit(process.cwd());
  
  if (!inGit) {
    console.log(chalk.yellow('Warning: Not in a git repository. Some features may be limited.'));
    console.log();
  }

  // Show startup information
  if (options.verbose) {
    console.log(chalk.blue('Kritrima AI CLI'), chalk.gray(`v${CLI_VERSION}`));
    console.log(chalk.gray(`Provider: ${config.provider}`));
    console.log(chalk.gray(`Model: ${config.model}`));
    console.log(chalk.gray(`Approval: ${config.approvalMode}`));
    console.log();
  }

  // Start the main application
  const app = new App(config);
  await app.start();
}

/**
 * Handle uncaught errors
 */
process.on('uncaughtException', (error) => {
  console.error(chalk.red('Uncaught Exception:'), error.message);
  if (process.env.DEBUG) {
    console.error(error.stack);
  }
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error(chalk.red('Unhandled Rejection:'), reason);
  process.exit(1);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log(chalk.yellow('\nGracefully shutting down...'));
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log(chalk.yellow('\nGracefully shutting down...'));
  process.exit(0);
});

// Run the CLI
main().catch((error) => {
  console.error(chalk.red('Fatal error:'), error.message);
  process.exit(1);
});
