import { spawn } from 'child_process';
import { resolve } from 'path';
import { existsSync, writeFileSync, unlinkSync } from 'fs';
import { tmpdir } from 'os';
import { join } from 'path';
import { logInfo, logError } from '../../logger/log.js';
export async function execWithLandlock(input, config, additionalWritableRoots = []) {
    const startTime = Date.now();
    logInfo('Executing command with Landlock sandbox', {
        command: input.command,
        workdir: input.workdir,
        timeout: input.timeout
    });
    try {
        if (!isLandlockAvailable()) {
            throw new Error('Landlock is not available on this system');
        }
        const sandboxConfig = prepareSandboxConfig(input, config, additionalWritableRoots);
        const scriptPath = createSandboxScript(sandboxConfig);
        try {
            const result = await executeInSandbox(scriptPath, input.timeout || 30000);
            const duration = Date.now() - startTime;
            logInfo('Sandboxed command execution completed', {
                command: input.command,
                exitCode: result.exitCode,
                duration,
                success: result.exitCode === 0
            });
            return {
                success: result.exitCode === 0,
                output: result.stdout + (result.stderr ? `\nSTDERR:\n${result.stderr}` : ''),
                error: result.stderr || undefined,
                exitCode: result.exitCode,
                duration,
                command: input.command,
                workdir: input.workdir || process.cwd()
            };
        }
        finally {
            if (existsSync(scriptPath)) {
                unlinkSync(scriptPath);
            }
        }
    }
    catch (error) {
        const duration = Date.now() - startTime;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logError('Sandboxed command execution failed', error instanceof Error ? error : new Error(errorMessage));
        return {
            success: false,
            output: '',
            error: errorMessage,
            exitCode: -1,
            duration,
            command: input.command,
            workdir: input.workdir || process.cwd()
        };
    }
}
function isLandlockAvailable() {
    try {
        if (process.platform !== 'linux') {
            return false;
        }
        const { execSync } = require('child_process');
        const kernelVersion = execSync('uname -r', { encoding: 'utf8' }).trim();
        const versionMatch = kernelVersion.match(/^(\d+)\.(\d+)/);
        if (versionMatch) {
            const major = parseInt(versionMatch[1]);
            const minor = parseInt(versionMatch[2]);
            if (major > 5 || (major === 5 && minor >= 13)) {
                return true;
            }
        }
        return false;
    }
    catch (error) {
        return false;
    }
}
function prepareSandboxConfig(input, config, additionalWritableRoots) {
    const workdir = input.workdir ? resolve(input.workdir) : process.cwd();
    const readablePaths = [
        '/usr',
        '/lib',
        '/lib64',
        '/bin',
        '/sbin',
        '/etc/ld.so.cache',
        '/etc/passwd',
        '/etc/group',
        '/proc/self',
        '/dev/null',
        '/dev/zero',
        '/dev/urandom',
        workdir
    ];
    const writablePaths = [
        workdir,
        tmpdir(),
        ...additionalWritableRoots.map(path => resolve(path))
    ];
    const executablePaths = [
        '/usr/bin',
        '/bin',
        '/usr/local/bin'
    ];
    return {
        command: input.command,
        workdir,
        readablePaths,
        writablePaths,
        executablePaths
    };
}
function createSandboxScript(config) {
    const scriptPath = join(tmpdir(), `landlock-${Date.now()}-${Math.random().toString(36).substring(7)}.sh`);
    const script = `#!/bin/bash
set -e

# Landlock sandbox script
# This script uses landlock_restrict to apply filesystem restrictions

# Check if landlock_restrict is available
if ! command -v landlock_restrict &> /dev/null; then
    echo "Error: landlock_restrict not found. Please install landlock tools." >&2
    exit 1
fi

# Prepare landlock arguments
LANDLOCK_ARGS=""

# Add readable paths
${config.readablePaths.map(path => `LANDLOCK_ARGS="$LANDLOCK_ARGS --ro '${path}'"`).join('\n')}

# Add writable paths
${config.writablePaths.map(path => `LANDLOCK_ARGS="$LANDLOCK_ARGS --rw '${path}'"`).join('\n')}

# Add executable paths
${config.executablePaths.map(path => `LANDLOCK_ARGS="$LANDLOCK_ARGS --exec '${path}'"`).join('\n')}

# Change to working directory
cd "${config.workdir}"

# Execute command with landlock restrictions
exec landlock_restrict $LANDLOCK_ARGS -- ${config.command.map(arg => `'${arg.replace(/'/g, "'\"'\"'")}'`).join(' ')}
`;
    writeFileSync(scriptPath, script, { mode: 0o755 });
    return scriptPath;
}
function executeInSandbox(scriptPath, timeout) {
    return new Promise((resolve, reject) => {
        const child = spawn('/bin/bash', [scriptPath], {
            stdio: ['pipe', 'pipe', 'pipe'],
            timeout
        });
        let stdout = '';
        let stderr = '';
        child.stdout?.on('data', (data) => {
            stdout += data.toString();
        });
        child.stderr?.on('data', (data) => {
            stderr += data.toString();
        });
        child.on('close', (code) => {
            resolve({
                stdout: stdout.trim(),
                stderr: stderr.trim(),
                exitCode: code || 0
            });
        });
        child.on('error', (error) => {
            reject(new Error(`Sandbox process error: ${error.message}`));
        });
        setTimeout(() => {
            if (!child.killed) {
                child.kill('SIGTERM');
                setTimeout(() => {
                    if (!child.killed) {
                        child.kill('SIGKILL');
                    }
                }, 5000);
                reject(new Error(`Sandboxed command timed out after ${timeout}ms`));
            }
        }, timeout);
    });
}
export async function testLandlock() {
    const capabilities = [];
    const limitations = [];
    if (!isLandlockAvailable()) {
        return {
            available: false,
            capabilities: [],
            limitations: ['Landlock not available (requires Linux 5.13+)']
        };
    }
    try {
        const { execSync } = require('child_process');
        try {
            const version = execSync('landlock_restrict --version 2>/dev/null || echo "unknown"', { encoding: 'utf8' }).trim();
            capabilities.push('Landlock tools available');
            capabilities.push('Filesystem access control');
            capabilities.push('Path-based restrictions');
            return {
                available: true,
                version,
                capabilities,
                limitations: [
                    'Requires landlock_restrict tool',
                    'No network isolation',
                    'No resource limits'
                ]
            };
        }
        catch (error) {
            limitations.push('landlock_restrict tool not found');
        }
    }
    catch (error) {
        limitations.push('Failed to test Landlock capabilities');
    }
    return {
        available: false,
        capabilities,
        limitations
    };
}
export function getLandlockEnvironment() {
    return {
        platform: 'linux',
        sandboxing: true,
        restrictions: [
            'Filesystem access control via Landlock LSM',
            'Read-only access to system directories',
            'Write access limited to working directory and temp',
            'Executable access limited to standard paths',
            'No network isolation (requires additional tools)'
        ]
    };
}
