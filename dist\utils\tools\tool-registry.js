import { sandboxExecutor } from '../sandbox-execution.js';
import { requestCommandApproval } from '../security/approval-system.js';
import { logInfo, logError } from '../logger/log.js';
export class ToolRegistry {
    tools = new Map();
    executionHistory = [];
    constructor() {
        this.registerBuiltinTools();
    }
    registerTool(tool) {
        this.tools.set(tool.id, tool);
        logInfo(`Registered tool: ${tool.name} (${tool.id})`);
    }
    getTool(id) {
        return this.tools.get(id);
    }
    getAllTools() {
        return Array.from(this.tools.values());
    }
    getToolsByCategory(category) {
        return Array.from(this.tools.values()).filter(tool => tool.category === category);
    }
    async executeTool(toolId, parameters, context) {
        const startTime = Date.now();
        try {
            const tool = this.getTool(toolId);
            if (!tool) {
                throw new Error(`Tool ${toolId} not found`);
            }
            const validationResult = this.validateParameters(tool, parameters);
            if (!validationResult.valid) {
                throw new Error(`Parameter validation failed: ${validationResult.errors.join(', ')}`);
            }
            if (tool.requiresApproval) {
                const approval = await requestCommandApproval(`tool:${tool.id}`, Object.keys(parameters), `Execute tool: ${tool.name}`);
                if (approval.status !== 'approved') {
                    throw new Error(`Tool execution not approved: ${approval.status}`);
                }
            }
            logInfo(`Executing tool: ${tool.name} with parameters:`, parameters);
            const result = await tool.execute(parameters, context);
            const executionTime = Date.now() - startTime;
            const execution = {
                toolId: tool.id,
                parameters,
                result,
                context,
                timestamp: new Date().toISOString(),
                executionTime
            };
            this.executionHistory.push(execution);
            logInfo(`Tool ${tool.name} executed successfully in ${executionTime}ms`);
            return { ...result, executionTime };
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : String(error);
            logError(`Tool execution failed: ${errorMessage}`);
            return {
                success: false,
                error: errorMessage,
                executionTime
            };
        }
    }
    validateParameters(tool, parameters) {
        const errors = [];
        for (const param of tool.parameters) {
            if (param.required && !(param.name in parameters)) {
                errors.push(`Missing required parameter: ${param.name}`);
                continue;
            }
            const value = parameters[param.name];
            if (value === undefined || value === null) {
                if (param.required) {
                    errors.push(`Parameter ${param.name} cannot be null or undefined`);
                }
                continue;
            }
            if (!this.validateParameterType(value, param.type)) {
                errors.push(`Parameter ${param.name} must be of type ${param.type}`);
                continue;
            }
            if (param.validation && !param.validation(value)) {
                errors.push(`Parameter ${param.name} failed custom validation`);
            }
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
    validateParameterType(value, type) {
        switch (type) {
            case 'string':
                return typeof value === 'string';
            case 'number':
                return typeof value === 'number' && !isNaN(value);
            case 'boolean':
                return typeof value === 'boolean';
            case 'array':
                return Array.isArray(value);
            case 'object':
                return typeof value === 'object' && value !== null && !Array.isArray(value);
            default:
                return false;
        }
    }
    registerBuiltinTools() {
        this.registerTool({
            id: 'file_read',
            name: 'Read File',
            description: 'Read contents of a file',
            category: 'file',
            version: '1.0.0',
            riskLevel: 'low',
            requiresApproval: false,
            parameters: [
                {
                    name: 'path',
                    type: 'string',
                    description: 'Path to the file to read',
                    required: true
                }
            ],
            execute: async (params) => {
                const fs = await import('fs/promises');
                try {
                    const content = await fs.readFile(params.path, 'utf-8');
                    return {
                        success: true,
                        data: content,
                        output: `File read successfully: ${params.path}`,
                        executionTime: 0
                    };
                }
                catch (error) {
                    throw new Error(`Failed to read file: ${error}`);
                }
            }
        });
        this.registerTool({
            id: 'file_write',
            name: 'Write File',
            description: 'Write content to a file',
            category: 'file',
            version: '1.0.0',
            riskLevel: 'medium',
            requiresApproval: true,
            parameters: [
                {
                    name: 'path',
                    type: 'string',
                    description: 'Path to the file to write',
                    required: true
                },
                {
                    name: 'content',
                    type: 'string',
                    description: 'Content to write to the file',
                    required: true
                }
            ],
            execute: async (params) => {
                const fs = await import('fs/promises');
                try {
                    await fs.writeFile(params.path, params.content, 'utf-8');
                    return {
                        success: true,
                        output: `File written successfully: ${params.path}`,
                        executionTime: 0
                    };
                }
                catch (error) {
                    throw new Error(`Failed to write file: ${error}`);
                }
            }
        });
        this.registerTool({
            id: 'execute_command',
            name: 'Execute Command',
            description: 'Execute a shell command',
            category: 'system',
            version: '1.0.0',
            riskLevel: 'high',
            requiresApproval: true,
            parameters: [
                {
                    name: 'command',
                    type: 'string',
                    description: 'Command to execute',
                    required: true
                },
                {
                    name: 'args',
                    type: 'array',
                    description: 'Command arguments',
                    required: false,
                    default: []
                },
                {
                    name: 'timeout',
                    type: 'number',
                    description: 'Timeout in milliseconds',
                    required: false,
                    default: 30000
                }
            ],
            execute: async (params, context) => {
                const result = await sandboxExecutor.executeCommand(params.command, params.args || [], {
                    timeout: params.timeout || 30000,
                    workingDirectory: context.workingDirectory
                });
                return {
                    success: result.success,
                    data: {
                        stdout: result.stdout,
                        stderr: result.stderr,
                        exitCode: result.exitCode
                    },
                    output: result.stdout,
                    error: result.success ? undefined : result.stderr,
                    executionTime: 0
                };
            }
        });
        this.registerTool({
            id: 'git_status',
            name: 'Git Status',
            description: 'Get git repository status',
            category: 'git',
            version: '1.0.0',
            riskLevel: 'low',
            requiresApproval: false,
            parameters: [],
            execute: async (_params, context) => {
                const { getGitStatus } = await import('../check-in-git.js');
                const status = getGitStatus();
                return {
                    success: true,
                    data: status,
                    output: `Git status retrieved for ${context.workingDirectory}`,
                    executionTime: 0
                };
            }
        });
        this.registerTool({
            id: 'analyze_project',
            name: 'Analyze Project',
            description: 'Perform full project context analysis',
            category: 'analysis',
            version: '1.0.0',
            riskLevel: 'low',
            requiresApproval: false,
            parameters: [
                {
                    name: 'includeContent',
                    type: 'boolean',
                    description: 'Include file contents in analysis',
                    required: false,
                    default: false
                }
            ],
            execute: async (_params, context) => {
                const { getProjectContext } = await import('../full-context.js');
                const projectContext = await getProjectContext(context.workingDirectory);
                return {
                    success: true,
                    data: projectContext,
                    output: `Project analysis complete: ${projectContext.files.length} files analyzed`,
                    executionTime: 0
                };
            }
        });
        logInfo(`Registered ${this.tools.size} built-in tools`);
    }
    getExecutionHistory() {
        return [...this.executionHistory];
    }
    clearExecutionHistory() {
        this.executionHistory = [];
    }
    getUsageStatistics() {
        const stats = {};
        for (const execution of this.executionHistory) {
            stats[execution.toolId] = (stats[execution.toolId] || 0) + 1;
        }
        return stats;
    }
}
export const toolRegistry = new ToolRegistry();
export async function executeTool(toolId, parameters = {}, context) {
    const defaultContext = {
        workingDirectory: process.cwd(),
        environment: process.env,
        capabilities: ['file_read', 'file_write', 'command_execute'],
        ...context
    };
    return toolRegistry.executeTool(toolId, parameters, defaultContext);
}
export function getAvailableTools() {
    return toolRegistry.getAllTools();
}
