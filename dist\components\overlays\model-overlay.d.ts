import type { ProviderName } from '../../types/index.js';
interface ModelOverlayProps {
    currentProvider: string;
    currentModel: string;
    onProviderChange: (provider: ProviderName) => void;
    onModelChange: (model: string) => void;
    onClose: () => void;
    visible: boolean;
}
export declare function ModelOverlay({ currentProvider, currentModel, onProviderChange, onModelChange, onClose, visible }: ModelOverlayProps): import("react/jsx-runtime").JSX.Element | null;
export {};
//# sourceMappingURL=model-overlay.d.ts.map