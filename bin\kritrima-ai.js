#!/usr/bin/env node

/**
 * Kritrima AI CLI - Binary Entry Point
 * 
 * This is the main entry point for the Kritrima AI CLI application.
 * It handles the initial setup and delegates to the main CLI module.
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Determine if we're running from source or built
const srcPath = join(__dirname, '..', 'src', 'cli.tsx');
const distPath = join(__dirname, '..', 'dist', 'cli.js');

let entryPoint;

if (existsSync(distPath)) {
  // Running from built version
  entryPoint = distPath;
} else if (existsSync(srcPath)) {
  // Running from source with tsx
  entryPoint = srcPath;
} else {
  console.error('Error: Could not find CLI entry point');
  process.exit(1);
}

// Dynamic import to handle both .js and .tsx files
try {
  if (entryPoint.endsWith('.tsx')) {
    // Use tsx for TypeScript files
    const { spawn } = await import('child_process');
    const child = spawn('npx', ['tsx', entryPoint, ...process.argv.slice(2)], {
      stdio: 'inherit',
      cwd: join(__dirname, '..')
    });

    child.on('exit', (code) => {
      process.exit(code || 0);
    });
  } else {
    // Direct import for compiled JavaScript
    // Convert paths to file:// URLs for ESM compatibility across all platforms
    const entryUrl = new URL('file://' + (process.platform === 'win32'
      ? '/' + entryPoint.replace(/\\/g, '/')
      : entryPoint)).href;
    await import(entryUrl);
  }
} catch (error) {
  console.error('Error starting Kritrima AI CLI:', error.message);
  process.exit(1);
}
