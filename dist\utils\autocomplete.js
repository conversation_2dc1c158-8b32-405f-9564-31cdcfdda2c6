import { readdirSync, statSync, existsSync } from 'fs';
import { join, dirname, basename, extname } from 'path';
import { getSlashCommandNames } from './slash-commands.js';
import { getHistory } from './storage/command-history.js';
export function getCompletions(input, cursorPosition = input.length, options = {}) {
    const maxSuggestions = options.maxSuggestions || 10;
    const includeHidden = options.includeHidden || false;
    const fuzzyMatch = options.fuzzyMatch || true;
    const { word, prefix, type } = extractWordAtCursor(input, cursorPosition);
    let suggestions = [];
    switch (type) {
        case 'slash':
            suggestions = getSlashCommandCompletions(word, fuzzyMatch);
            break;
        case 'file':
            suggestions = getFileCompletions(word, includeHidden);
            break;
        case 'command':
            suggestions = getCommandCompletions(word, fuzzyMatch);
            break;
        default:
            suggestions = [
                ...getSlashCommandCompletions(word, fuzzyMatch),
                ...getFileCompletions(word, includeHidden),
                ...getCommandCompletions(word, fuzzyMatch)
            ];
    }
    suggestions = [...new Set(suggestions)];
    if (fuzzyMatch) {
        suggestions = fuzzyFilter(suggestions, word);
    }
    const hasMore = suggestions.length > maxSuggestions;
    suggestions = suggestions.slice(0, maxSuggestions);
    return {
        suggestions,
        prefix,
        hasMore,
        type: type || 'mixed'
    };
}
function extractWordAtCursor(input, cursorPosition) {
    let start = cursorPosition;
    let end = cursorPosition;
    while (start > 0 && !/\s/.test(input[start - 1])) {
        start--;
    }
    while (end < input.length && !/\s/.test(input[end])) {
        end++;
    }
    const word = input.slice(start, end);
    const prefix = input.slice(0, start);
    let type = null;
    if (word.startsWith('/')) {
        type = 'slash';
    }
    else if (word.startsWith('@') || word.includes('/') || word.includes('.')) {
        type = 'file';
    }
    else if (prefix.trim() === '' || prefix.trim().split(/\s+/).length === 1) {
        type = 'command';
    }
    return { word, prefix, type };
}
function getSlashCommandCompletions(input, fuzzyMatch = true) {
    const commands = getSlashCommandNames();
    const query = input.startsWith('/') ? input : `/${input}`;
    if (!fuzzyMatch) {
        return commands.filter(cmd => cmd.toLowerCase().startsWith(query.toLowerCase()));
    }
    return commands;
}
function getFileCompletions(input, includeHidden = false) {
    try {
        let path = input;
        if (path.startsWith('@')) {
            path = path.slice(1);
        }
        let dir;
        let filename;
        if (path.includes('/') || path.includes('\\')) {
            dir = dirname(path);
            filename = basename(path);
        }
        else {
            dir = '.';
            filename = path;
        }
        const resolvedDir = dir === '.' ? process.cwd() : join(process.cwd(), dir);
        if (!existsSync(resolvedDir)) {
            return [];
        }
        const entries = readdirSync(resolvedDir);
        const suggestions = [];
        for (const entry of entries) {
            if (!includeHidden && entry.startsWith('.')) {
                continue;
            }
            if (filename && !entry.toLowerCase().startsWith(filename.toLowerCase())) {
                continue;
            }
            const fullPath = join(resolvedDir, entry);
            const stats = statSync(fullPath);
            if (stats.isDirectory()) {
                suggestions.push(`${entry}/`);
            }
            else {
                suggestions.push(entry);
            }
        }
        const pathPrefix = dir === '.' ? '' : `${dir}/`;
        return suggestions.map(s => `${pathPrefix}${s}`);
    }
    catch (error) {
        return [];
    }
}
function getCommandCompletions(input, fuzzyMatch = true) {
    try {
        const history = getHistory();
        const commands = history.map(entry => entry.command);
        const uniqueCommands = [...new Set(commands)];
        if (!fuzzyMatch) {
            return uniqueCommands.filter(cmd => cmd.toLowerCase().startsWith(input.toLowerCase()));
        }
        return uniqueCommands;
    }
    catch (error) {
        return [];
    }
}
function fuzzyFilter(suggestions, query) {
    if (!query) {
        return suggestions;
    }
    const queryLower = query.toLowerCase();
    const scored = suggestions.map(suggestion => ({
        suggestion,
        score: fuzzyScore(suggestion.toLowerCase(), queryLower)
    }));
    return scored
        .filter(item => item.score > 0)
        .sort((a, b) => b.score - a.score)
        .map(item => item.suggestion);
}
function fuzzyScore(text, query) {
    if (text === query)
        return 100;
    if (text.startsWith(query))
        return 90;
    if (text.includes(query))
        return 70;
    let score = 0;
    let textIndex = 0;
    let queryIndex = 0;
    while (textIndex < text.length && queryIndex < query.length) {
        if (text[textIndex] === query[queryIndex]) {
            score += 1;
            queryIndex++;
        }
        textIndex++;
    }
    if (queryIndex === query.length) {
        score += 10;
    }
    score -= Math.abs(text.length - query.length) * 0.1;
    return Math.max(0, score);
}
export function getFileTypeCompletions(directory = '.', extensions = []) {
    try {
        const resolvedDir = directory === '.' ? process.cwd() : join(process.cwd(), directory);
        if (!existsSync(resolvedDir)) {
            return [];
        }
        const entries = readdirSync(resolvedDir);
        const suggestions = [];
        for (const entry of entries) {
            const fullPath = join(resolvedDir, entry);
            const stats = statSync(fullPath);
            if (stats.isFile()) {
                const ext = extname(entry).toLowerCase();
                if (extensions.length === 0 || extensions.includes(ext)) {
                    suggestions.push(entry);
                }
            }
        }
        return suggestions.sort();
    }
    catch (error) {
        return [];
    }
}
export function getDirectoryCompletions(basePath = '.', includeHidden = false) {
    try {
        const resolvedDir = basePath === '.' ? process.cwd() : join(process.cwd(), basePath);
        if (!existsSync(resolvedDir)) {
            return [];
        }
        const entries = readdirSync(resolvedDir);
        const suggestions = [];
        for (const entry of entries) {
            if (!includeHidden && entry.startsWith('.')) {
                continue;
            }
            const fullPath = join(resolvedDir, entry);
            const stats = statSync(fullPath);
            if (stats.isDirectory()) {
                suggestions.push(`${entry}/`);
            }
        }
        return suggestions.sort();
    }
    catch (error) {
        return [];
    }
}
export class AutoCompleteManager {
    cache = new Map();
    cacheTimeout = 5000;
    getCompletions(input, cursorPosition, options) {
        const cacheKey = `${input}:${cursorPosition}:${JSON.stringify(options)}`;
        const cached = this.cache.get(cacheKey);
        if (cached) {
            return cached;
        }
        const result = getCompletions(input, cursorPosition, options);
        this.cache.set(cacheKey, result);
        setTimeout(() => {
            this.cache.delete(cacheKey);
        }, this.cacheTimeout);
        return result;
    }
    clearCache() {
        this.cache.clear();
    }
    getCacheSize() {
        return this.cache.size;
    }
}
export const autoCompleteManager = new AutoCompleteManager();
export function getFileSystemSuggestions(query, basePath = process.cwd(), maxResults = 10) {
    try {
        let searchPath = query;
        if (searchPath.startsWith('@')) {
            searchPath = searchPath.slice(1);
        }
        let dir;
        let filename;
        if (searchPath.includes('/') || searchPath.includes('\\')) {
            dir = dirname(searchPath);
            filename = basename(searchPath);
        }
        else {
            dir = '.';
            filename = searchPath;
        }
        const resolvedDir = dir === '.' ? basePath : join(basePath, dir);
        if (!existsSync(resolvedDir)) {
            return [];
        }
        const entries = readdirSync(resolvedDir);
        const suggestions = [];
        for (const entry of entries) {
            if (entry.startsWith('.')) {
                continue;
            }
            if (filename && !entry.toLowerCase().includes(filename.toLowerCase())) {
                continue;
            }
            const fullPath = join(resolvedDir, entry);
            const stats = statSync(fullPath);
            if (stats.isDirectory()) {
                suggestions.push(`${entry}/`);
            }
            else {
                suggestions.push(entry);
            }
            if (suggestions.length >= maxResults) {
                break;
            }
        }
        const pathPrefix = dir === '.' ? '' : `${dir}/`;
        return suggestions.map(s => `${pathPrefix}${s}`);
    }
    catch (error) {
        return [];
    }
}
