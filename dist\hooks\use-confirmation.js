import { useState, useCallback, useRef } from 'react';
export function useConfirmation() {
    const [currentRequest, setCurrentRequest] = useState(null);
    const [_queue, setQueue] = useState([]);
    const requestIdRef = useRef(0);
    const submitConfirmation = useCallback((result) => {
        if (currentRequest) {
            currentRequest.resolve(result);
            setCurrentRequest(null);
            setQueue(prevQueue => {
                const [nextRequest, ...remainingQueue] = prevQueue;
                if (nextRequest) {
                    setCurrentRequest(nextRequest);
                }
                return remainingQueue;
            });
        }
    }, [currentRequest]);
    const requestConfirmation = useCallback((prompt, explanation) => {
        return new Promise((resolve, reject) => {
            const id = `confirmation-${++requestIdRef.current}`;
            const request = {
                id,
                prompt,
                explanation,
                resolve,
                reject,
            };
            if (currentRequest) {
                setQueue(prevQueue => [...prevQueue, request]);
            }
            else {
                setCurrentRequest(request);
            }
        });
    }, [currentRequest]);
    return {
        submitConfirmation,
        requestConfirmation,
        confirmationPrompt: currentRequest?.prompt || null,
        explanation: currentRequest?.explanation,
        isConfirmationPending: currentRequest !== null,
    };
}
