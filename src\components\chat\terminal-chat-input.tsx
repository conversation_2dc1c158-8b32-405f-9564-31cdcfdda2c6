/**
 * Advanced Terminal Chat Input System
 * 
 * Provides sophisticated input handling with multiline editing, file suggestions,
 * slash commands, history navigation, and tab completion
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Box, Text, useInput } from 'ink';
import TextBuffer from '../../text-buffer.js';
import { getFileSystemSuggestions } from '../../utils/autocomplete.js';
import { getCommandHistory } from '../../utils/storage/command-history.js';
import { SLASH_COMMANDS } from '../../utils/slash-commands.js';
// Removed unused HistoryEntry import

interface TerminalChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: (value: string) => void;
  disabled?: boolean;
  placeholder?: string;
  multiline?: boolean;
  showSuggestions?: boolean;
}

interface InputState {
  key: number;
  initialCursorOffset?: number;
}

export function TerminalChatInput({
  value,
  onChange,
  onSubmit,
  disabled = false,
  placeholder = "Type your message...",
  multiline = true,
  showSuggestions = true
}: TerminalChatInputProps) {
  // Editor state for future enhancements
  const [_editorState, _setEditorState] = useState<InputState>({ key: 0 });
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showingSuggestions, setShowingSuggestions] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState(0);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [originalInput, setOriginalInput] = useState('');
  const textBufferRef = useRef<TextBuffer | null>(null);

  // Initialize text buffer
  useEffect(() => {
    if (!textBufferRef.current) {
      textBufferRef.current = new TextBuffer();
      textBufferRef.current.setText(value);
    }
  }, []);

  // Update text buffer when value changes externally
  useEffect(() => {
    if (textBufferRef.current && textBufferRef.current.getText() !== value) {
      textBufferRef.current.setText(value);
    }
  }, [value]);

  // Handle keyboard input
  useInput((input, key) => {
    if (disabled) {
      return;
    }

    const textBuffer = textBufferRef.current;
    if (!textBuffer) {
      return;
    }

    // Handle special keys
    if (key.return) {
      if (key.shift && multiline) {
        // Shift+Enter for new line in multiline mode
        textBuffer.insertText('\n');
        updateValue();
      } else {
        // Enter to submit
        handleSubmit();
      }
      return;
    }

    if (key.tab) {
      handleTabCompletion();
      return;
    }

    if (key.upArrow) {
      if (showingSuggestions) {
        navigateSuggestions(-1);
      } else {
        navigateHistory(-1);
      }
      return;
    }

    if (key.downArrow) {
      if (showingSuggestions) {
        navigateSuggestions(1);
      } else {
        navigateHistory(1);
      }
      return;
    }

    if (key.escape) {
      if (showingSuggestions) {
        hideSuggestions();
      } else {
        // Clear input
        textBuffer.clear();
        updateValue();
        resetHistory();
      }
      return;
    }

    if (key.backspace || key.delete) {
      if (key.backspace) {
        textBuffer.backspace();
      } else {
        textBuffer.delete();
      }
      updateValue();
      updateSuggestions();
      return;
    }

    // Handle regular character input
    if (input && !key.ctrl && !key.meta) {
      textBuffer.insertText(input);
      updateValue();
      updateSuggestions();
    }

    // Handle Ctrl+A (select all)
    if (key.ctrl && input === 'a') {
      textBuffer.selectAll();
      updateValue();
      return;
    }

    // Handle Ctrl+C (copy)
    if (key.ctrl && input === 'c') {
      // Copy selected text to clipboard (if supported)
      const selectedText = textBuffer.getSelectedText();
      if (selectedText) {
        // Note: Clipboard operations would need platform-specific implementation
        console.log('Copied:', selectedText);
      }
      return;
    }

    // Handle Ctrl+V (paste)
    if (key.ctrl && input === 'v') {
      // Paste from clipboard (if supported)
      // Note: Clipboard operations would need platform-specific implementation
      return;
    }

    // Handle Ctrl+Z (undo)
    if (key.ctrl && input === 'z') {
      textBuffer.undo();
      updateValue();
      return;
    }

    // Handle Ctrl+Y (redo)
    if (key.ctrl && input === 'y') {
      textBuffer.redo();
      updateValue();
      return;
    }
  });

  /**
   * Update the input value from text buffer
   */
  const updateValue = useCallback(() => {
    const textBuffer = textBufferRef.current;
    if (textBuffer) {
      const newValue = textBuffer.getText();
      onChange(newValue);
    }
  }, [onChange]);

  /**
   * Handle form submission
   */
  const handleSubmit = useCallback(() => {
    const currentValue = value.trim();
    if (currentValue) {
      onSubmit(currentValue);
      // Clear input after submission
      textBufferRef.current?.clear();
      updateValue();
      resetHistory();
      hideSuggestions();
    }
  }, [value, onSubmit, updateValue]);

  /**
   * Handle tab completion
   */
  const handleTabCompletion = useCallback(() => {
    if (showingSuggestions && suggestions.length > 0) {
      // Apply selected suggestion
      const suggestion = suggestions[selectedSuggestion];
      applySuggestion(suggestion);
    } else {
      // Trigger suggestions
      updateSuggestions();
    }
  }, [showingSuggestions, suggestions, selectedSuggestion]);

  /**
   * Update suggestions based on current input
   */
  const updateSuggestions = useCallback(() => {
    if (!showSuggestions) {
      return;
    }

    const currentValue = value;
    // Cursor position for future cursor-based suggestions
    const _cursorPosition = textBufferRef.current?.getCursorPosition() || { row: 0, col: 0 };
    
    // Check for file suggestions (@file pattern)
    if (currentValue.includes('@')) {
      const atIndex = currentValue.lastIndexOf('@');
      const query = currentValue.substring(atIndex + 1);
      
      if (query.length > 0) {
        const fileSuggestions = getFileSystemSuggestions(query, process.cwd());
        setSuggestions(fileSuggestions);
        setShowingSuggestions(fileSuggestions.length > 0);
        setSelectedSuggestion(0);
        return;
      }
    }

    // Check for slash command suggestions
    if (currentValue.startsWith('/')) {
      const commandSuggestions = SLASH_COMMANDS
        .filter(cmd => cmd.command.startsWith(currentValue))
        .map(cmd => cmd.command);
      
      setSuggestions(commandSuggestions);
      setShowingSuggestions(commandSuggestions.length > 0);
      setSelectedSuggestion(0);
      return;
    }

    // Hide suggestions if no matches
    hideSuggestions();
  }, [value, showSuggestions]);

  /**
   * Apply a suggestion to the input
   */
  const applySuggestion = useCallback((suggestion: string) => {
    const textBuffer = textBufferRef.current;
    if (!textBuffer) {
      return;
    }

    const currentValue = value;
    
    if (currentValue.includes('@')) {
      // Replace file suggestion
      const atIndex = currentValue.lastIndexOf('@');
      const newValue = currentValue.substring(0, atIndex + 1) + suggestion;
      textBuffer.setText(newValue);
    } else if (currentValue.startsWith('/')) {
      // Replace slash command
      textBuffer.setText(suggestion);
    }
    
    updateValue();
    hideSuggestions();
  }, [value, updateValue]);

  /**
   * Navigate through suggestions
   */
  const navigateSuggestions = useCallback((direction: number) => {
    if (!showingSuggestions || suggestions.length === 0) {
      return;
    }
    
    const newIndex = Math.max(0, Math.min(suggestions.length - 1, selectedSuggestion + direction));
    setSelectedSuggestion(newIndex);
  }, [showingSuggestions, suggestions.length, selectedSuggestion]);

  /**
   * Hide suggestions
   */
  const hideSuggestions = useCallback(() => {
    setShowingSuggestions(false);
    setSuggestions([]);
    setSelectedSuggestion(0);
  }, []);

  /**
   * Navigate through command history
   */
  const navigateHistory = useCallback((direction: number) => {
    const history = getCommandHistory();
    if (history.length === 0) {
      return;
    }

    if (historyIndex === -1 && direction === -1) {
      // First time going up - save current input
      setOriginalInput(value);
      setHistoryIndex(history.length - 1);
      const historyItem = history[history.length - 1];
      textBufferRef.current?.setText(historyItem.command);
      updateValue();
    } else if (historyIndex >= 0) {
      const newIndex = historyIndex + direction;
      
      if (newIndex >= 0 && newIndex < history.length) {
        setHistoryIndex(newIndex);
        const historyItem = history[newIndex];
        textBufferRef.current?.setText(historyItem.command);
        updateValue();
      } else if (newIndex === -1) {
        // Back to original input
        setHistoryIndex(-1);
        textBufferRef.current?.setText(originalInput);
        updateValue();
      }
    }
  }, [historyIndex, value, updateValue]);

  /**
   * Reset history navigation
   */
  const resetHistory = useCallback(() => {
    setHistoryIndex(-1);
    setOriginalInput('');
  }, []);

  // Render the input with cursor
  const renderInput = () => {
    const lines = value.split('\n');
    const textBuffer = textBufferRef.current;
    const cursorPos = textBuffer?.getCursorPosition() || { row: 0, col: 0 };

    return lines.map((line, lineIndex) => {
      if (lineIndex === cursorPos.row) {
        // Line with cursor
        const beforeCursor = line.substring(0, cursorPos.col);
        const afterCursor = line.substring(cursorPos.col);
        
        return (
          <Text key={lineIndex}>
            {beforeCursor}
            <Text color="black">█</Text>
            {afterCursor}
          </Text>
        );
      } else {
        return <Text key={lineIndex}>{line}</Text>;
      }
    });
  };

  return (
    <Box flexDirection="column">
      {/* Input area */}
      <Box borderStyle="single" paddingX={1} paddingY={multiline ? 1 : 0}>
        <Box flexDirection="column" width="100%">
          {value ? renderInput() : <Text color="gray">{placeholder}</Text>}
        </Box>
      </Box>

      {/* Suggestions */}
      {showingSuggestions && suggestions.length > 0 && (
        <Box borderStyle="single" paddingX={1}>
          <Box flexDirection="column" width="100%">
            <Text color="blue" bold>Suggestions:</Text>
            {suggestions.slice(0, 5).map((suggestion, index) => (
              <Text 
                key={index}
                backgroundColor={index === selectedSuggestion ? "blue" : undefined}
                color={index === selectedSuggestion ? "white" : "gray"}
              >
                {suggestion}
              </Text>
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );
}
