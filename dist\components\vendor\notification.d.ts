export type NotificationType = 'info' | 'success' | 'warning' | 'error';
export interface NotificationData {
    id: string;
    type: NotificationType;
    title: string;
    message?: string;
    duration?: number;
    actions?: NotificationAction[];
    timestamp: number;
}
export interface NotificationAction {
    label: string;
    key: string;
    action: () => void;
}
export interface NotificationProps {
    notification: NotificationData;
    onDismiss: (id: string) => void;
    onAction: (id: string, actionKey: string) => void;
}
export interface NotificationManagerProps {
    notifications: NotificationData[];
    maxVisible?: number;
    position?: 'top' | 'bottom';
    onDismiss: (id: string) => void;
    onAction: (id: string, actionKey: string) => void;
}
export declare function Notification({ notification, onDismiss, onAction: _onAction }: NotificationProps): import("react/jsx-runtime").JSX.Element;
export declare function NotificationManager({ notifications, maxVisible, position, onDismiss, onAction }: NotificationManagerProps): import("react/jsx-runtime").JSX.Element | null;
export declare function useNotifications(): {
    notifications: NotificationData[];
    addNotification: (type: NotificationType, title: string, message?: string, options?: {
        duration?: number;
        actions?: NotificationAction[];
        id?: string;
    }) => string;
    dismissNotification: (id: string) => void;
    handleAction: (id: string, actionKey: string) => void;
    clearAll: () => void;
    showInfo: (title: string, message?: string, options?: any) => string;
    showSuccess: (title: string, message?: string, options?: any) => string;
    showWarning: (title: string, message?: string, options?: any) => string;
    showError: (title: string, message?: string, options?: any) => string;
};
export interface ToastProps {
    type: NotificationType;
    message: string;
    visible: boolean;
    onDismiss?: () => void;
}
export declare function Toast({ type, message, visible, onDismiss }: ToastProps): import("react/jsx-runtime").JSX.Element | null;
export interface ProgressNotificationProps {
    title: string;
    progress: number;
    message?: string;
    visible: boolean;
}
export declare function ProgressNotification({ title, progress, message, visible }: ProgressNotificationProps): import("react/jsx-runtime").JSX.Element | null;
//# sourceMappingURL=notification.d.ts.map