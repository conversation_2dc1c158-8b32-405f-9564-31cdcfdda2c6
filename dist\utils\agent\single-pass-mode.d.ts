import { AgentLoop } from './agent-loop.js';
import type { ResponseItem } from '../../types/index.js';
export interface SinglePassOptions {
    useFullContext: boolean;
    maxTokens: number;
    temperature: number;
    includeSystemPrompt: boolean;
    planningPhase: boolean;
    executionPhase: boolean;
    validationPhase: boolean;
}
export interface SinglePassResult {
    success: boolean;
    items: ResponseItem[];
    executionTime: number;
    tokensUsed: number;
    phases: {
        planning?: PhaseResult;
        execution?: PhaseResult;
        validation?: PhaseResult;
    };
    warnings: string[];
    errors: string[];
}
export interface PhaseResult {
    success: boolean;
    duration: number;
    output: string;
    tokensUsed: number;
    error?: string;
}
export declare function executeSinglePass(userInput: string, agentLoop: AgentLoop, workingDir?: string, options?: Partial<SinglePassOptions>): Promise<SinglePassResult>;
export declare function isSinglePassSuitable(userInput: string): {
    suitable: boolean;
    reasons: string[];
    confidence: number;
};
//# sourceMappingURL=single-pass-mode.d.ts.map