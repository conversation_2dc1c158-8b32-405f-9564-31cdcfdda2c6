#!/usr/bin/env node

/**
 * Installation Fix Script for Kritrima AI CLI
 * Handles common installation issues across platforms
 */

import { execSync } from 'child_process';
import { existsSync, unlinkSync, chmodSync } from 'fs';
import { join } from 'path';
import chalk from 'chalk';

const isWindows = process.platform === 'win32';
const isMacOS = process.platform === 'darwin';
const isLinux = process.platform === 'linux';

console.log(chalk.blue('🔧 Kritrima AI CLI - Installation Fix'));
console.log(chalk.gray(`Platform: ${process.platform} (${process.arch})`));
console.log();

// Step 1: Check for existing installations
console.log(chalk.yellow('🔍 Checking for existing installations...'));

try {
  const globalList = execSync('npm list -g kritrima-ai --depth=0', { encoding: 'utf8' });
  if (globalList.includes('kritrima-ai@')) {
    console.log(chalk.yellow('⚠️  Found existing global installation'));
    
    // Try to uninstall cleanly first
    try {
      console.log(chalk.blue('🗑️  Attempting clean uninstall...'));
      execSync('npm uninstall -g kritrima-ai', { stdio: 'inherit' });
      console.log(chalk.green('✅ Clean uninstall successful'));
    } catch (error) {
      console.log(chalk.yellow('⚠️  Clean uninstall failed, will force remove'));
    }
  }
} catch (error) {
  console.log(chalk.green('✅ No existing global installation found'));
}

// Step 2: Check for binary conflicts
console.log(chalk.yellow('🔍 Checking for binary conflicts...'));

const possibleBinPaths = [
  '/usr/bin/kritrima-ai',
  '/usr/local/bin/kritrima-ai',
  '/opt/homebrew/bin/kritrima-ai',
  process.env.HOME ? join(process.env.HOME, '.local/bin/kritrima-ai') : null,
  isWindows ? join(process.env.APPDATA || '', 'npm', 'kritrima-ai') : null,
  isWindows ? join(process.env.APPDATA || '', 'npm', 'kritrima-ai.cmd') : null
].filter(Boolean);

for (const binPath of possibleBinPaths) {
  if (existsSync(binPath)) {
    console.log(chalk.yellow(`⚠️  Found conflicting binary: ${binPath}`));
    
    try {
      if (process.getuid && process.getuid() === 0) {
        // Running as root, can remove directly
        unlinkSync(binPath);
        console.log(chalk.green(`✅ Removed: ${binPath}`));
      } else {
        console.log(chalk.red(`❌ Cannot remove ${binPath} (requires sudo)`));
        console.log(chalk.gray(`   Run: sudo rm "${binPath}"`));
      }
    } catch (error) {
      console.log(chalk.red(`❌ Failed to remove ${binPath}: ${error.message}`));
    }
  }
}

// Step 3: Platform-specific fixes
console.log(chalk.yellow('🔧 Applying platform-specific fixes...'));

if (isLinux || isMacOS) {
  // Check npm global prefix
  try {
    const npmPrefix = execSync('npm config get prefix', { encoding: 'utf8' }).trim();
    const binDir = join(npmPrefix, 'bin');
    const conflictPath = join(binDir, 'kritrima-ai');
    
    if (existsSync(conflictPath)) {
      console.log(chalk.yellow(`⚠️  Found npm binary conflict: ${conflictPath}`));
      
      try {
        if (process.getuid && process.getuid() === 0) {
          unlinkSync(conflictPath);
          console.log(chalk.green(`✅ Removed npm binary: ${conflictPath}`));
        } else {
          console.log(chalk.red(`❌ Cannot remove ${conflictPath} (requires sudo)`));
          console.log(chalk.gray(`   Run: sudo rm "${conflictPath}"`));
        }
      } catch (error) {
        console.log(chalk.red(`❌ Failed to remove ${conflictPath}: ${error.message}`));
      }
    }
  } catch (error) {
    console.log(chalk.yellow('⚠️  Could not check npm prefix'));
  }
}

// Step 4: Clear npm cache
console.log(chalk.yellow('🧹 Clearing npm cache...'));

try {
  execSync('npm cache clean --force', { stdio: 'inherit' });
  console.log(chalk.green('✅ npm cache cleared'));
} catch (error) {
  console.log(chalk.yellow('⚠️  Cache clear failed, continuing...'));
}

// Step 5: Installation instructions
console.log();
console.log(chalk.green('🎉 Fix process complete!'));
console.log();
console.log(chalk.blue('📋 Installation Instructions:'));
console.log();

if (isLinux || isMacOS) {
  console.log(chalk.yellow('For Linux/macOS:'));
  console.log(chalk.gray('# Option 1: Install with sudo (recommended)'));
  console.log(chalk.white('sudo npm install -g kritrima-ai'));
  console.log();
  console.log(chalk.gray('# Option 2: Install without sudo (user-local)'));
  console.log(chalk.white('npm config set prefix ~/.local'));
  console.log(chalk.white('npm install -g kritrima-ai'));
  console.log(chalk.white('export PATH="$HOME/.local/bin:$PATH"'));
  console.log();
  console.log(chalk.gray('# Option 3: Force overwrite (if conflicts remain)'));
  console.log(chalk.white('sudo npm install -g kritrima-ai --force'));
}

if (isWindows) {
  console.log(chalk.yellow('For Windows:'));
  console.log(chalk.gray('# Standard installation'));
  console.log(chalk.white('npm install -g kritrima-ai'));
  console.log();
  console.log(chalk.gray('# If conflicts remain, force overwrite'));
  console.log(chalk.white('npm install -g kritrima-ai --force'));
}

console.log();
console.log(chalk.blue('🧪 Test Installation:'));
console.log(chalk.white('kritrima-ai --version'));
console.log(chalk.white('kritrima-ai --help'));
console.log();

// Step 6: Automatic installation attempt
const shouldAutoInstall = process.argv.includes('--auto-install');

if (shouldAutoInstall) {
  console.log(chalk.yellow('🚀 Attempting automatic installation...'));
  
  try {
    const installCommand = (isLinux || isMacOS) && process.getuid && process.getuid() === 0
      ? 'npm install -g kritrima-ai'
      : isWindows
      ? 'npm install -g kritrima-ai'
      : 'npm install -g kritrima-ai --force';
    
    execSync(installCommand, { stdio: 'inherit' });
    console.log(chalk.green('✅ Installation successful!'));
    
    // Test the installation
    try {
      execSync('kritrima-ai --version', { stdio: 'inherit' });
      console.log(chalk.green('✅ CLI is working correctly!'));
    } catch (error) {
      console.log(chalk.yellow('⚠️  Installation completed but CLI test failed'));
    }
    
  } catch (error) {
    console.log(chalk.red('❌ Automatic installation failed'));
    console.log(chalk.gray('Please try manual installation using the commands above'));
  }
}
