import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput, useApp } from 'ink';
import chalk from 'chalk';
import { AgentLoop } from '../../utils/agent/agent-loop.js';
import { createInputItem } from '../../utils/input-utils.js';
import { executeSlashCommand, isSlashCommand } from '../../utils/slash-commands.js';
import { expandFileTags } from '../../utils/file-tag-utils.js';
import { addToHistory } from '../../utils/storage/command-history.js';
import { saveRollout } from '../../utils/storage/save-rollout.js';
import { logInfo, logError } from '../../utils/logger/log.js';
import { processMultiModalInput } from '../../utils/multimodal-input.js';
import { TerminalChatInput } from './terminal-chat-input.js';
import { ModelOverlay } from '../overlays/model-overlay.js';
import { HistoryOverlay } from '../overlays/history-overlay.js';
import { SessionsOverlay } from '../overlays/sessions-overlay.js';
import { ApprovalOverlay } from '../overlays/approval-overlay.js';
import { HelpOverlay } from '../overlays/help-overlay.js';
import { LoadingSpinner } from '../vendor/spinner.js';
import { useNotifications, NotificationManager } from '../vendor/notification.js';
import { useAgentState } from '../../hooks/use-agent-state.js';
export function TerminalChat({ config, onExit }) {
    useApp();
    const [model, setModel] = useState(config.model);
    const [provider, setProvider] = useState(config.provider);
    const [approvalPolicy, setApprovalPolicy] = useState(config.approvalMode);
    const [items, setItems] = useState([]);
    const [loading, setLoading] = useState(false);
    const [input, setInput] = useState('');
    const [sessionId] = useState(() => generateSessionId());
    const [overlayMode, setOverlayMode] = useState('none');
    useAgentState({
        onStateChange: (state) => {
            setLoading(state !== 'idle');
        },
        onError: (error) => {
            notifications.showError('Agent Error', error.message);
        },
        onComplete: (_result) => {
            notifications.showSuccess('Task Completed', 'Agent operation completed successfully');
        }
    });
    const notifications = useNotifications();
    const [agentLoop] = useState(() => new AgentLoop({
        model,
        provider,
        approvalPolicy,
        additionalWritableRoots: config.additionalWritableRoots || []
    }));
    useEffect(() => {
        agentLoop.updateConfig({
            model,
            provider,
            approvalPolicy
        });
    }, [model, provider, approvalPolicy, agentLoop]);
    useEffect(() => {
        if (items.length > 0) {
            saveRollout(sessionId, items, { model, provider, approvalMode: approvalPolicy });
        }
    }, [items, sessionId, model, provider, approvalPolicy]);
    useInput((input, key) => {
        if (key.ctrl && input === 'c') {
            onExit();
            return;
        }
        if (overlayMode === 'none') {
            if (key.ctrl && input === 'm') {
                setOverlayMode('model');
                return;
            }
            if (key.ctrl && input === 'h') {
                setOverlayMode('history');
                return;
            }
            if (key.ctrl && input === 's') {
                setOverlayMode('sessions');
                return;
            }
            if (key.ctrl && input === 'a') {
                setOverlayMode('approval');
                return;
            }
            if (key.ctrl && input === '?') {
                setOverlayMode('help');
                return;
            }
        }
        if (overlayMode !== 'none' && key.escape) {
            setOverlayMode('none');
            return;
        }
        if (overlayMode !== 'none') {
            return;
        }
        if (key.return) {
            handleSubmit();
            return;
        }
        if (key.backspace || key.delete) {
            setInput(prev => prev.slice(0, -1));
            return;
        }
        if (input && !key.ctrl && !key.meta) {
            setInput(prev => prev + input);
        }
    });
    const handleSubmit = useCallback(async () => {
        if (!input.trim() || loading) {
            return;
        }
        const userInput = input.trim();
        setInput('');
        setLoading(true);
        try {
            logInfo(`Processing user input: ${userInput}`);
            addToHistory(userInput);
            if (isSlashCommand(userInput)) {
                const result = await executeSlashCommand(userInput);
                if (result.command === '/exit' || result.command === '/quit') {
                    onExit();
                    return;
                }
                const commandResult = {
                    role: 'assistant',
                    content: result.result,
                    type: 'output',
                    timestamp: Date.now(),
                    metadata: {
                        model: 'system',
                        provider: 'system'
                    }
                };
                setItems(prev => [...prev, commandResult]);
                setLoading(false);
                return;
            }
            const processedInput = processMultiModalInput(userInput);
            const expandedInput = await expandFileTags(processedInput.metadata.originalInput, process.cwd());
            const inputItem = await createInputItem(expandedInput, processedInput.content);
            const results = await agentLoop.executeLoop(inputItem, {
                onDelta: (delta) => {
                    console.log(chalk.gray(delta));
                },
                onComplete: (content) => {
                    logInfo(`AI response completed - Length: ${content.length}`);
                },
                onError: (error) => {
                    logError('AI response error', new Error(error));
                },
                onToolCall: (toolCall) => {
                    logInfo(`Tool call initiated: ${toolCall.name}`);
                },
                onToolResult: (result) => {
                    logInfo(`Tool execution completed - Success: ${result.success}, Command: ${result.metadata?.command?.join(' ') || 'unknown'}`);
                },
                getCommandConfirmation: async (command, workdir) => {
                    return await handleCommandApproval(command, workdir);
                }
            });
            setItems(prev => [...prev, ...results]);
            addToHistory(userInput, true);
        }
        catch (error) {
            logError('Error processing user input', error instanceof Error ? error : new Error(String(error)));
            const errorItem = {
                role: 'assistant',
                content: `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
                type: 'output',
                timestamp: Date.now(),
                metadata: {
                    model: 'system',
                    provider: 'system'
                }
            };
            setItems(prev => [...prev, errorItem]);
            addToHistory(userInput, false);
        }
        finally {
            setLoading(false);
        }
    }, [input, loading, agentLoop, onExit]);
    const handleCommandApproval = useCallback(async (command, workdir) => {
        if (approvalPolicy === 'full-auto') {
            return true;
        }
        if (approvalPolicy === 'auto-edit') {
            const safeCommands = config.safeCommands || [];
            const commandName = command[0]?.toLowerCase();
            return safeCommands.includes(commandName);
        }
        console.log(chalk.yellow(`\nCommand approval required:`));
        console.log(chalk.gray(`Command: ${command.join(' ')}`));
        console.log(chalk.gray(`Working directory: ${workdir}`));
        console.log(chalk.blue('Auto-approving for demo purposes...'));
        return true;
    }, [approvalPolicy, config.safeCommands]);
    const handleProviderChange = useCallback((newProvider) => {
        setProvider(newProvider);
        agentLoop.updateConfig({ provider: newProvider });
        notifications.showInfo('Provider Changed', `Switched to ${newProvider}`);
    }, [agentLoop, notifications]);
    const handleModelChange = useCallback((newModel) => {
        setModel(newModel);
        agentLoop.updateConfig({ model: newModel });
        notifications.showInfo('Model Changed', `Switched to ${newModel}`);
    }, [agentLoop, notifications]);
    const handleApprovalPolicyChange = useCallback((newPolicy) => {
        setApprovalPolicy(newPolicy);
        agentLoop.updateConfig({ approvalPolicy: newPolicy });
        notifications.showInfo('Approval Mode Changed', `Switched to ${newPolicy} mode`);
    }, [agentLoop, notifications]);
    const handleSelectCommand = useCallback((command) => {
        setInput(command);
        setOverlayMode('none');
    }, []);
    const handleLoadSession = useCallback((sessionData) => {
        setItems(sessionData.items);
        setModel(sessionData.metadata.model);
        setProvider(sessionData.metadata.provider);
        setApprovalPolicy(sessionData.metadata.approvalMode || 'suggest');
        agentLoop.updateConfig({
            model: sessionData.metadata.model,
            provider: sessionData.metadata.provider,
            approvalPolicy: sessionData.metadata.approvalMode || 'suggest'
        });
        notifications.showSuccess('Session Loaded', `Loaded session with ${sessionData.items.length} items`);
        setOverlayMode('none');
    }, [agentLoop, notifications]);
    const renderItems = () => {
        return items.map((item, index) => {
            if (item.type === 'message') {
                return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: "blue", bold: true, children: item.role === 'user' ? '👤 You:' : '🤖 Assistant:' }), item.content.map((content, contentIndex) => (_jsx(Text, { children: content.type === 'input_text' ? content.text : '[Image]' }, contentIndex)))] }, index));
            }
            if (item.type === 'output') {
                return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: "green", bold: true, children: "\uD83E\uDD16 Assistant:" }), _jsx(Text, { children: item.content })] }, index));
            }
            if (item.type === 'function_call') {
                return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: "yellow", bold: true, children: "\uD83D\uDD27 Tool Call:" }), _jsxs(Text, { color: "gray", children: [item.name, "(", item.arguments, ")"] })] }, index));
            }
            if (item.type === 'tool_result') {
                return (_jsxs(Box, { flexDirection: "column", children: [_jsxs(Text, { color: item.success ? "green" : "red", bold: true, children: [item.success ? '✅' : '❌', " Tool Result:"] }), _jsx(Text, { children: item.result })] }, index));
            }
            return null;
        });
    };
    return (_jsxs(Box, { flexDirection: "column", height: "100%", children: [_jsx(Box, { borderStyle: "single", paddingX: 1, children: _jsxs(Text, { color: "blue", bold: true, children: ["Kritrima AI CLI - ", provider, "/", model, " (", approvalPolicy, ")"] }) }), _jsxs(Box, { flexDirection: "column", flexGrow: 1, paddingX: 1, paddingY: 1, children: [renderItems(), loading && (_jsx(Box, { children: _jsx(LoadingSpinner, { message: "Processing your request...", showElapsed: true, startTime: Date.now(), type: "dots", color: "cyan" }) }))] }), overlayMode === 'none' && (_jsx(TerminalChatInput, { value: input, onChange: setInput, onSubmit: handleSubmit, disabled: loading, placeholder: "Type your message... (use @filename for files, /command for slash commands)", multiline: true, showSuggestions: true })), _jsx(Box, { paddingX: 1, children: _jsxs(Text, { color: "gray", dimColor: true, children: ["Press Ctrl+C to exit \u2022 Ctrl+M: Model \u2022 Ctrl+H: History \u2022 Ctrl+S: Sessions \u2022 Ctrl+A: Approval \u2022 Ctrl+?: Help \u2022 ", items.length, " messages"] }) }), _jsx(ModelOverlay, { currentProvider: provider, currentModel: model, onProviderChange: handleProviderChange, onModelChange: handleModelChange, onClose: () => setOverlayMode('none'), visible: overlayMode === 'model' }), _jsx(HistoryOverlay, { onSelectCommand: handleSelectCommand, onClose: () => setOverlayMode('none'), visible: overlayMode === 'history' }), _jsx(SessionsOverlay, { onLoadSession: handleLoadSession, onClose: () => setOverlayMode('none'), visible: overlayMode === 'sessions' }), _jsx(ApprovalOverlay, { currentMode: approvalPolicy, onModeChange: handleApprovalPolicyChange, onClose: () => setOverlayMode('none'), visible: overlayMode === 'approval' }), _jsx(HelpOverlay, { onClose: () => setOverlayMode('none'), visible: overlayMode === 'help' }), _jsx(NotificationManager, { notifications: notifications.notifications, onDismiss: notifications.dismissNotification, onAction: notifications.handleAction })] }));
}
function generateSessionId() {
    return Math.random().toString(36).substring(2, 15) +
        Math.random().toString(36).substring(2, 15);
}
