/**
 * Command History Browser Component
 * 
 * Interactive browser for command history with search and filtering
 * Supports command re-execution and history management
 */

import React, { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
// Removed unused chalk import
import { getHistory, searchHistory, getHistoryStats } from '../../utils/storage/command-history.js';
import type { HistoryEntry } from '../../types/index.js';

interface HistoryBrowserProps {
  onSelect: (command: string) => void;
  onCancel: () => void;
}

export function HistoryBrowser({ onSelect, onCancel }: HistoryBrowserProps) {
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [filteredHistory, setFilteredHistory] = useState<HistoryEntry[]>([]);
  const [selectedIndex, setSelectedIndex] = useState<number>(0);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [mode, setMode] = useState<'browse' | 'search'>('browse');
  const [stats, setStats] = useState<any>(null);

  // Load history on mount
  useEffect(() => {
    const loadHistory = () => {
      const historyData = getHistory();
      const historyStats = getHistoryStats();
      
      // Reverse to show most recent first
      const reversedHistory = [...historyData].reverse();
      
      setHistory(reversedHistory);
      setFilteredHistory(reversedHistory);
      setStats(historyStats);
    };

    loadHistory();
  }, []);

  // Handle search
  useEffect(() => {
    if (searchQuery.trim()) {
      const results = searchHistory(searchQuery);
      setFilteredHistory([...results].reverse());
      setSelectedIndex(0);
    } else {
      setFilteredHistory(history);
      setSelectedIndex(0);
    }
  }, [searchQuery, history]);

  // Handle keyboard input
  useInput((input, key) => {
    if (key.escape) {
      if (mode === 'search' && searchQuery) {
        setSearchQuery('');
        setMode('browse');
      } else {
        onCancel();
      }
      return;
    }

    if (key.return) {
      const selectedEntry = filteredHistory[selectedIndex];
      if (selectedEntry) {
        onSelect(selectedEntry.command);
      }
      return;
    }

    if (key.upArrow) {
      setSelectedIndex(prev => Math.max(0, prev - 1));
      return;
    }

    if (key.downArrow) {
      setSelectedIndex(prev => Math.min(filteredHistory.length - 1, prev + 1));
      return;
    }

    if (key.ctrl && input === 'f') {
      setMode('search');
      return;
    }

    if (key.ctrl && input === 'c') {
      onCancel();
      return;
    }

    if (key.backspace || key.delete) {
      if (mode === 'search') {
        setSearchQuery(prev => prev.slice(0, -1));
      }
      return;
    }

    // Handle search input
    if (mode === 'search' && input && !key.ctrl && !key.meta) {
      setSearchQuery(prev => prev + input);
    }
  });

  const formatTimestamp = (timestamp: number): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return date.toLocaleDateString();
  };

  const getStatusIcon = (entry: HistoryEntry): string => {
    if (entry.success === true) return '✅';
    if (entry.success === false) return '❌';
    return '❓';
  };

  const getStatusColor = (entry: HistoryEntry): string => {
    if (entry.success === true) return 'green';
    if (entry.success === false) return 'red';
    return 'gray';
  };

  return (
    <Box flexDirection="column" padding={1}>
      {/* Header */}
      <Box borderStyle="single" paddingX={1}>
        <Text bold color="blue">Command History Browser</Text>
      </Box>

      {/* Stats */}
      {stats && (
        <Box>
          <Text>
            Total: <Text color="cyan">{stats.totalCommands}</Text> • 
            Successful: <Text color="green">{stats.successfulCommands}</Text> • 
            Failed: <Text color="red">{stats.failedCommands}</Text> • 
            Unique: <Text color="yellow">{stats.uniqueCommands}</Text>
          </Text>
        </Box>
      )}

      {/* Search bar */}
      <Box borderStyle="single" paddingX={1}>
        <Text>
          {mode === 'search' ? '🔍 ' : '📝 '}
          Search: <Text color={mode === 'search' ? 'cyan' : 'gray'}>
            {searchQuery || (mode === 'search' ? '█' : 'Press Ctrl+F to search')}
          </Text>
        </Text>
      </Box>

      {/* History list */}
      <Box flexDirection="column" height={15}>
        {filteredHistory.length === 0 ? (
          <Box justifyContent="center" alignItems="center" height="100%">
            <Text color="gray">
              {searchQuery ? 'No commands found matching your search' : 'No command history available'}
            </Text>
          </Box>
        ) : (
          filteredHistory.slice(0, 15).map((entry, index) => {
            const isSelected = index === selectedIndex;
            const statusIcon = getStatusIcon(entry);
            const statusColor = getStatusColor(entry);
            const timestamp = formatTimestamp(entry.timestamp);

            return (
              <Box key={`${entry.timestamp}-${index}`} flexDirection="column">
                <Box>
                  <Text color={isSelected ? 'cyan' : undefined}>
                    {isSelected ? '▶ ' : '  '}
                    <Text color={statusColor}>{statusIcon}</Text>
                    {' '}
                    <Text bold={isSelected}>{entry.command}</Text>
                  </Text>
                </Box>
                {isSelected && (
                  <Box>
                    <Text color="gray">
                      Executed: {timestamp}
                      {entry.success !== undefined && (
                        <Text color={statusColor}>
                          {' • '}
                          {entry.success ? 'Success' : 'Failed'}
                        </Text>
                      )}
                    </Text>
                  </Box>
                )}
              </Box>
            );
          })
        )}
      </Box>

      {/* Show more indicator */}
      {filteredHistory.length > 15 && (
        <Box justifyContent="center">
          <Text color="gray">
            ... and {filteredHistory.length - 15} more commands
          </Text>
        </Box>
      )}

      {/* Selected command preview */}
      {filteredHistory[selectedIndex] && (
        <Box borderStyle="single" paddingX={1}>
          <Box flexDirection="column">
            <Text bold color="cyan">Selected Command:</Text>
            <Text color="yellow">{filteredHistory[selectedIndex].command}</Text>
          </Box>
        </Box>
      )}

      {/* Instructions */}
      <Box borderStyle="single" paddingX={1}>
        <Box flexDirection="column">
          <Text color="gray">
            ↑/↓ Navigate • Enter Execute • Ctrl+F Search • Esc Cancel
          </Text>
          {mode === 'search' && (
            <Text color="yellow">
              Type to search commands • Esc to clear search
            </Text>
          )}
          {filteredHistory[selectedIndex] && (
            <Text color="cyan">
              Press Enter to execute: {filteredHistory[selectedIndex].command}
            </Text>
          )}
        </Box>
      </Box>
    </Box>
  );
}
