export type AgentName = 'npm' | 'pnpm' | 'bun' | 'yarn' | 'deno';
export declare function detectInstallerByPath(): Promise<AgentName | undefined>;
export declare function getPackageManagerVersion(pm: AgentName): Promise<string | null>;
export declare function getInstallCommand(pm: AgentName, packageName: string, global?: boolean): string;
export declare function getUpdateCommand(pm: AgentName, packageName: string, global?: boolean): string;
export declare function getUninstallCommand(pm: AgentName, packageName: string, global?: boolean): string;
export declare function detectFromLockFiles(projectPath?: string): AgentName | undefined;
export declare function getPackageManagerInfo(pm?: AgentName): Promise<{
    name: AgentName;
    version: string | null;
    available: boolean;
    installCommand: string;
    updateCommand: string;
}>;
export declare function getAllPackageManagers(): Promise<Array<{
    name: AgentName;
    version: string | null;
    available: boolean;
}>>;
export declare function recommendPackageManager(): Promise<{
    recommended: AgentName;
    reason: string;
    alternatives: AgentName[];
}>;
//# sourceMappingURL=package-manager-detector.d.ts.map