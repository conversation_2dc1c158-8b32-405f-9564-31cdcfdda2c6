import type { AppConfig } from './types/index.js';
export declare class App {
    private config;
    private inkInstance;
    constructor(config: AppConfig);
    start(): Promise<void>;
    stop(): Promise<void>;
    private performStartupChecks;
    private validateConfiguration;
    private checkAPIConnectivity;
    private initializeUI;
    private showStartupMessage;
    private setupSignalHandlers;
    private handleExit;
    getConfig(): AppConfig;
    updateConfig(updates: Partial<AppConfig>): void;
    restart(newConfig?: Partial<AppConfig>): Promise<void>;
}
export declare function createApp(config: AppConfig): Promise<App>;
export declare function startApplication(config: AppConfig): Promise<void>;
//# sourceMappingURL=app.d.ts.map