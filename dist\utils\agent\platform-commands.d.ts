export declare function adaptCommandForPlatform(command: string[]): string[];
export declare function isCommandAvailable(command: string): boolean;
export declare function getCommandSuggestions(command: string): string[];
export declare function getCommandHelp(command: string): string;
export declare function normalizePath(path: string): string;
export declare function getShellCommand(): string[];
export declare function escapeArgument(arg: string): string;
//# sourceMappingURL=platform-commands.d.ts.map