import { readFileSync, writeFileSync, existsSync, unlinkSync, mkdirSync, copyFileSync } from 'fs';
import { dirname, join, resolve } from 'path';
import { logInfo, logError } from '../logger/log.js';
export function processPatch(patch, workingDirectory = process.cwd(), createBackups = true) {
    const result = {
        success: true,
        operations: [],
        errors: [],
        backups: []
    };
    try {
        logInfo('Processing patch', { patchLength: patch.length, workingDirectory });
        const diffs = parsePatch(patch);
        if (diffs.length === 0) {
            result.errors.push('No valid diffs found in patch');
            result.success = false;
            return result;
        }
        for (const diff of diffs) {
            try {
                const operation = applyDiff(diff, workingDirectory, createBackups);
                result.operations.push(operation);
                if (operation.backup) {
                    result.backups.push(operation.backup);
                }
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                result.errors.push(`Failed to apply diff for ${diff.newFile}: ${errorMessage}`);
                result.success = false;
            }
        }
        logInfo('Patch processing completed', {
            operations: result.operations.length,
            errors: result.errors.length,
            success: result.success
        });
        return result;
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logError('Patch processing failed', error instanceof Error ? error : new Error(errorMessage));
        result.errors.push(`Patch processing failed: ${errorMessage}`);
        result.success = false;
        return result;
    }
}
export function parsePatch(patch) {
    const lines = patch.split('\n');
    const diffs = [];
    let currentDiff = null;
    let currentHunk = null;
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        if (line.startsWith('--- ')) {
            if (currentDiff && currentHunk) {
                currentDiff.hunks.push(currentHunk);
            }
            if (currentDiff) {
                diffs.push(currentDiff);
            }
            currentDiff = {
                oldFile: line.substring(4).trim(),
                newFile: '',
                hunks: [],
                isNew: false,
                isDeleted: false
            };
            currentHunk = null;
        }
        else if (line.startsWith('+++ ') && currentDiff) {
            currentDiff.newFile = line.substring(4).trim();
            if (currentDiff.oldFile === '/dev/null') {
                currentDiff.isNew = true;
            }
            if (currentDiff.newFile === '/dev/null') {
                currentDiff.isDeleted = true;
            }
        }
        else if (line.startsWith('@@') && currentDiff) {
            if (currentHunk) {
                currentDiff.hunks.push(currentHunk);
            }
            const hunkMatch = line.match(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/);
            if (hunkMatch) {
                currentHunk = {
                    oldStart: parseInt(hunkMatch[1]),
                    oldCount: parseInt(hunkMatch[2] || '1'),
                    newStart: parseInt(hunkMatch[3]),
                    newCount: parseInt(hunkMatch[4] || '1'),
                    lines: []
                };
            }
        }
        else if (currentHunk && (line.startsWith(' ') || line.startsWith('-') || line.startsWith('+'))) {
            currentHunk.lines.push(line);
        }
        else if (line.startsWith('*** [') && line.includes('] File: ')) {
            const match = line.match(/\*\*\* \[(\w+)\] File: (.+)/);
            if (match) {
                const action = match[1].toLowerCase();
                const filePath = match[2].trim();
                if (currentDiff && currentHunk) {
                    currentDiff.hunks.push(currentHunk);
                }
                if (currentDiff) {
                    diffs.push(currentDiff);
                }
                currentDiff = {
                    oldFile: action === 'create' ? '/dev/null' : filePath,
                    newFile: action === 'delete' ? '/dev/null' : filePath,
                    hunks: [],
                    isNew: action === 'create',
                    isDeleted: action === 'delete'
                };
                currentHunk = null;
            }
        }
    }
    if (currentDiff && currentHunk) {
        currentDiff.hunks.push(currentHunk);
    }
    if (currentDiff) {
        diffs.push(currentDiff);
    }
    return diffs;
}
function applyDiff(diff, workingDirectory, createBackups) {
    const filePath = resolve(workingDirectory, diff.newFile);
    if (diff.isDeleted) {
        if (!existsSync(filePath)) {
            throw new Error(`File to delete does not exist: ${filePath}`);
        }
        let backupPath;
        if (createBackups) {
            backupPath = createBackup(filePath);
        }
        unlinkSync(filePath);
        return {
            type: 'delete',
            path: filePath,
            backup: backupPath
        };
    }
    if (diff.isNew) {
        if (existsSync(filePath)) {
            throw new Error(`File already exists: ${filePath}`);
        }
        const dir = dirname(filePath);
        if (!existsSync(dir)) {
            mkdirSync(dir, { recursive: true });
        }
        const content = extractNewFileContent(diff.hunks);
        writeFileSync(filePath, content, 'utf-8');
        return {
            type: 'create',
            path: filePath,
            content
        };
    }
    if (!existsSync(filePath)) {
        throw new Error(`File to edit does not exist: ${filePath}`);
    }
    let backupPath;
    if (createBackups) {
        backupPath = createBackup(filePath);
    }
    const originalContent = readFileSync(filePath, 'utf-8');
    const modifiedContent = applyHunks(originalContent, diff.hunks);
    writeFileSync(filePath, modifiedContent, 'utf-8');
    return {
        type: 'edit',
        path: filePath,
        content: modifiedContent,
        backup: backupPath
    };
}
function extractNewFileContent(hunks) {
    const lines = [];
    for (const hunk of hunks) {
        for (const line of hunk.lines) {
            if (line.startsWith('+')) {
                lines.push(line.substring(1));
            }
            else if (line.startsWith(' ')) {
                lines.push(line.substring(1));
            }
        }
    }
    return lines.join('\n');
}
function applyHunks(originalContent, hunks) {
    const originalLines = originalContent.split('\n');
    let modifiedLines = [...originalLines];
    let offset = 0;
    const sortedHunks = [...hunks].sort((a, b) => a.oldStart - b.oldStart);
    for (const hunk of sortedHunks) {
        const result = applyHunk(modifiedLines, hunk, offset);
        modifiedLines = result.lines;
        offset += result.offset;
    }
    return modifiedLines.join('\n');
}
function applyHunk(lines, hunk, currentOffset) {
    const startLine = hunk.oldStart - 1 + currentOffset;
    const newLines = [];
    let oldLineIndex = 0;
    let newOffset = 0;
    for (const line of hunk.lines) {
        if (line.startsWith(' ')) {
            const expectedLine = line.substring(1);
            const actualLine = lines[startLine + oldLineIndex];
            if (actualLine !== expectedLine) {
                logError('Hunk context mismatch', new Error(`Expected: "${expectedLine}", Got: "${actualLine}"`));
            }
            newLines.push(expectedLine);
            oldLineIndex++;
        }
        else if (line.startsWith('-')) {
            oldLineIndex++;
            newOffset--;
        }
        else if (line.startsWith('+')) {
            newLines.push(line.substring(1));
            newOffset++;
        }
    }
    const result = [
        ...lines.slice(0, startLine),
        ...newLines,
        ...lines.slice(startLine + oldLineIndex)
    ];
    return {
        lines: result,
        offset: currentOffset + newOffset
    };
}
function createBackup(filePath) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${filePath}.backup-${timestamp}`;
    copyFileSync(filePath, backupPath);
    logInfo('Created backup', { original: filePath, backup: backupPath });
    return backupPath;
}
export function restoreFromBackup(backupPath) {
    try {
        if (!existsSync(backupPath)) {
            return false;
        }
        const originalPath = backupPath.replace(/\.backup-[\d-T]+$/, '');
        copyFileSync(backupPath, originalPath);
        logInfo('Restored from backup', { backup: backupPath, original: originalPath });
        return true;
    }
    catch (error) {
        logError('Failed to restore from backup', error instanceof Error ? error : new Error(String(error)));
        return false;
    }
}
export function cleanupBackups(directory, maxAge = 7 * 24 * 60 * 60 * 1000) {
    try {
        const { readdirSync, statSync } = require('fs');
        const files = readdirSync(directory);
        let cleaned = 0;
        const now = Date.now();
        for (const file of files) {
            if (file.includes('.backup-')) {
                const filePath = join(directory, file);
                const stats = statSync(filePath);
                if (now - stats.mtime.getTime() > maxAge) {
                    unlinkSync(filePath);
                    cleaned++;
                }
            }
        }
        logInfo('Cleaned up backup files', { directory, cleaned });
        return cleaned;
    }
    catch (error) {
        logError('Failed to cleanup backups', error instanceof Error ? error : new Error(String(error)));
        return 0;
    }
}
