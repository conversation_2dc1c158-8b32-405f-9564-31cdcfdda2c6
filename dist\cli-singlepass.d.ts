import type { AppConfig } from './types/index.js';
export interface SinglePassConfig {
    originalPrompt: string;
    config: AppConfig;
    rootPath: string;
    fullContext?: boolean;
}
export declare function runSinglePass(options: SinglePassConfig): Promise<void>;
export declare function validateSinglePassConfig(config: SinglePassConfig): void;
export declare function getSinglePassHelp(): string;
//# sourceMappingURL=cli-singlepass.d.ts.map