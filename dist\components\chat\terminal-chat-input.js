import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect, useCallback, useRef } from 'react';
import { Box, Text, useInput } from 'ink';
import TextBuffer from '../../text-buffer.js';
import { getFileSystemSuggestions } from '../../utils/autocomplete.js';
import { getCommandHistory } from '../../utils/storage/command-history.js';
import { SLASH_COMMANDS } from '../../utils/slash-commands.js';
export function TerminalChatInput({ value, onChange, onSubmit, disabled = false, placeholder = "Type your message...", multiline = true, showSuggestions = true }) {
    const [_editorState, _setEditorState] = useState({ key: 0 });
    const [suggestions, setSuggestions] = useState([]);
    const [showingSuggestions, setShowingSuggestions] = useState(false);
    const [selectedSuggestion, setSelectedSuggestion] = useState(0);
    const [historyIndex, setHistoryIndex] = useState(-1);
    const [originalInput, setOriginalInput] = useState('');
    const textBufferRef = useRef(null);
    useEffect(() => {
        if (!textBufferRef.current) {
            textBufferRef.current = new TextBuffer();
            textBufferRef.current.setText(value);
        }
    }, []);
    useEffect(() => {
        if (textBufferRef.current && textBufferRef.current.getText() !== value) {
            textBufferRef.current.setText(value);
        }
    }, [value]);
    useInput((input, key) => {
        if (disabled) {
            return;
        }
        const textBuffer = textBufferRef.current;
        if (!textBuffer) {
            return;
        }
        if (key.return) {
            if (key.shift && multiline) {
                textBuffer.insertText('\n');
                updateValue();
            }
            else {
                handleSubmit();
            }
            return;
        }
        if (key.tab) {
            handleTabCompletion();
            return;
        }
        if (key.upArrow) {
            if (showingSuggestions) {
                navigateSuggestions(-1);
            }
            else {
                navigateHistory(-1);
            }
            return;
        }
        if (key.downArrow) {
            if (showingSuggestions) {
                navigateSuggestions(1);
            }
            else {
                navigateHistory(1);
            }
            return;
        }
        if (key.escape) {
            if (showingSuggestions) {
                hideSuggestions();
            }
            else {
                textBuffer.clear();
                updateValue();
                resetHistory();
            }
            return;
        }
        if (key.backspace || key.delete) {
            if (key.backspace) {
                textBuffer.backspace();
            }
            else {
                textBuffer.delete();
            }
            updateValue();
            updateSuggestions();
            return;
        }
        if (input && !key.ctrl && !key.meta) {
            textBuffer.insertText(input);
            updateValue();
            updateSuggestions();
        }
        if (key.ctrl && input === 'a') {
            textBuffer.selectAll();
            updateValue();
            return;
        }
        if (key.ctrl && input === 'c') {
            const selectedText = textBuffer.getSelectedText();
            if (selectedText) {
                console.log('Copied:', selectedText);
            }
            return;
        }
        if (key.ctrl && input === 'v') {
            return;
        }
        if (key.ctrl && input === 'z') {
            textBuffer.undo();
            updateValue();
            return;
        }
        if (key.ctrl && input === 'y') {
            textBuffer.redo();
            updateValue();
            return;
        }
    });
    const updateValue = useCallback(() => {
        const textBuffer = textBufferRef.current;
        if (textBuffer) {
            const newValue = textBuffer.getText();
            onChange(newValue);
        }
    }, [onChange]);
    const handleSubmit = useCallback(() => {
        const currentValue = value.trim();
        if (currentValue) {
            onSubmit(currentValue);
            textBufferRef.current?.clear();
            updateValue();
            resetHistory();
            hideSuggestions();
        }
    }, [value, onSubmit, updateValue]);
    const handleTabCompletion = useCallback(() => {
        if (showingSuggestions && suggestions.length > 0) {
            const suggestion = suggestions[selectedSuggestion];
            applySuggestion(suggestion);
        }
        else {
            updateSuggestions();
        }
    }, [showingSuggestions, suggestions, selectedSuggestion]);
    const updateSuggestions = useCallback(() => {
        if (!showSuggestions) {
            return;
        }
        const currentValue = value;
        const _cursorPosition = textBufferRef.current?.getCursorPosition() || { row: 0, col: 0 };
        if (currentValue.includes('@')) {
            const atIndex = currentValue.lastIndexOf('@');
            const query = currentValue.substring(atIndex + 1);
            if (query.length > 0) {
                const fileSuggestions = getFileSystemSuggestions(query, process.cwd());
                setSuggestions(fileSuggestions);
                setShowingSuggestions(fileSuggestions.length > 0);
                setSelectedSuggestion(0);
                return;
            }
        }
        if (currentValue.startsWith('/')) {
            const commandSuggestions = SLASH_COMMANDS
                .filter(cmd => cmd.command.startsWith(currentValue))
                .map(cmd => cmd.command);
            setSuggestions(commandSuggestions);
            setShowingSuggestions(commandSuggestions.length > 0);
            setSelectedSuggestion(0);
            return;
        }
        hideSuggestions();
    }, [value, showSuggestions]);
    const applySuggestion = useCallback((suggestion) => {
        const textBuffer = textBufferRef.current;
        if (!textBuffer) {
            return;
        }
        const currentValue = value;
        if (currentValue.includes('@')) {
            const atIndex = currentValue.lastIndexOf('@');
            const newValue = currentValue.substring(0, atIndex + 1) + suggestion;
            textBuffer.setText(newValue);
        }
        else if (currentValue.startsWith('/')) {
            textBuffer.setText(suggestion);
        }
        updateValue();
        hideSuggestions();
    }, [value, updateValue]);
    const navigateSuggestions = useCallback((direction) => {
        if (!showingSuggestions || suggestions.length === 0) {
            return;
        }
        const newIndex = Math.max(0, Math.min(suggestions.length - 1, selectedSuggestion + direction));
        setSelectedSuggestion(newIndex);
    }, [showingSuggestions, suggestions.length, selectedSuggestion]);
    const hideSuggestions = useCallback(() => {
        setShowingSuggestions(false);
        setSuggestions([]);
        setSelectedSuggestion(0);
    }, []);
    const navigateHistory = useCallback((direction) => {
        const history = getCommandHistory();
        if (history.length === 0) {
            return;
        }
        if (historyIndex === -1 && direction === -1) {
            setOriginalInput(value);
            setHistoryIndex(history.length - 1);
            const historyItem = history[history.length - 1];
            textBufferRef.current?.setText(historyItem.command);
            updateValue();
        }
        else if (historyIndex >= 0) {
            const newIndex = historyIndex + direction;
            if (newIndex >= 0 && newIndex < history.length) {
                setHistoryIndex(newIndex);
                const historyItem = history[newIndex];
                textBufferRef.current?.setText(historyItem.command);
                updateValue();
            }
            else if (newIndex === -1) {
                setHistoryIndex(-1);
                textBufferRef.current?.setText(originalInput);
                updateValue();
            }
        }
    }, [historyIndex, value, updateValue]);
    const resetHistory = useCallback(() => {
        setHistoryIndex(-1);
        setOriginalInput('');
    }, []);
    const renderInput = () => {
        const lines = value.split('\n');
        const textBuffer = textBufferRef.current;
        const cursorPos = textBuffer?.getCursorPosition() || { row: 0, col: 0 };
        return lines.map((line, lineIndex) => {
            if (lineIndex === cursorPos.row) {
                const beforeCursor = line.substring(0, cursorPos.col);
                const afterCursor = line.substring(cursorPos.col);
                return (_jsxs(Text, { children: [beforeCursor, _jsx(Text, { color: "black", children: "\u2588" }), afterCursor] }, lineIndex));
            }
            else {
                return _jsx(Text, { children: line }, lineIndex);
            }
        });
    };
    return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Box, { borderStyle: "single", paddingX: 1, paddingY: multiline ? 1 : 0, children: _jsx(Box, { flexDirection: "column", width: "100%", children: value ? renderInput() : _jsx(Text, { color: "gray", children: placeholder }) }) }), showingSuggestions && suggestions.length > 0 && (_jsx(Box, { borderStyle: "single", paddingX: 1, children: _jsxs(Box, { flexDirection: "column", width: "100%", children: [_jsx(Text, { color: "blue", bold: true, children: "Suggestions:" }), suggestions.slice(0, 5).map((suggestion, index) => (_jsx(Text, { backgroundColor: index === selectedSuggestion ? "blue" : undefined, color: index === selectedSuggestion ? "white" : "gray", children: suggestion }, index)))] }) }))] }));
}
