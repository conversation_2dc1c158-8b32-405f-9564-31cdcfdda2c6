# Installation Fix Guide for Kritrima AI CLI

This guide addresses common installation issues, particularly the `EEXIST: file already exists` error.

## 🚨 Quick Fix for Your Error

You encountered this error:
```
npm error code EEXIST
npm error path /usr/bin/kritrima-ai
npm error EEXIST: file already exists
```

### Solution 1: Remove Existing Installation (Recommended)

```bash
# Step 1: Remove the existing binary
sudo rm /usr/bin/kritrima-ai

# Step 2: Clean uninstall any existing npm installation
sudo npm uninstall -g kritrima-ai

# Step 3: Clear npm cache
npm cache clean --force

# Step 4: Install fresh
sudo npm install -g kritrima-ai
```

### Solution 2: Force Overwrite

```bash
# Force installation (overwrites existing files)
sudo npm install -g kritrima-ai --force
```

### Solution 3: Use Our Automated Fix Script

```bash
# Download and run the fix script
curl -o install-fix.js https://raw.githubusercontent.com/kritrima-ai/kritrima-ai-cli/main/scripts/install-fix.js
node install-fix.js --auto-install
```

## 🔧 Comprehensive Installation Guide

### For Linux (Ubuntu/Debian/CentOS)

#### Method 1: Global Installation with sudo (Recommended)
```bash
# Clean any existing installation
sudo npm uninstall -g kritrima-ai 2>/dev/null || true
sudo rm -f /usr/bin/kritrima-ai /usr/local/bin/kritrima-ai

# Install fresh
sudo npm install -g kritrima-ai

# Verify installation
kritrima-ai --version
```

#### Method 2: User-local Installation (No sudo required)
```bash
# Set npm prefix to user directory
npm config set prefix ~/.local

# Install locally
npm install -g kritrima-ai

# Add to PATH (add this to your ~/.bashrc or ~/.zshrc)
export PATH="$HOME/.local/bin:$PATH"

# Reload shell or run:
source ~/.bashrc  # or source ~/.zshrc

# Verify installation
kritrima-ai --version
```

### For macOS

#### Method 1: Standard Installation
```bash
# Clean any existing installation
sudo npm uninstall -g kritrima-ai 2>/dev/null || true
sudo rm -f /usr/local/bin/kritrima-ai /opt/homebrew/bin/kritrima-ai

# Install fresh
sudo npm install -g kritrima-ai

# Verify installation
kritrima-ai --version
```

#### Method 2: Homebrew Node.js
```bash
# If using Homebrew Node.js
npm uninstall -g kritrima-ai 2>/dev/null || true
npm install -g kritrima-ai

# Verify installation
kritrima-ai --version
```

### For Windows

#### Method 1: Standard Installation
```bash
# Clean any existing installation
npm uninstall -g kritrima-ai

# Install fresh
npm install -g kritrima-ai

# Verify installation
kritrima-ai --version
```

#### Method 2: Force Installation
```bash
# If conflicts persist
npm install -g kritrima-ai --force
```

### For Windows WSL

```bash
# Same as Linux instructions
sudo npm uninstall -g kritrima-ai 2>/dev/null || true
sudo rm -f /usr/bin/kritrima-ai /usr/local/bin/kritrima-ai
sudo npm install -g kritrima-ai
```

## 🛠️ Troubleshooting Common Issues

### Issue 1: Permission Denied
```bash
# Error: EACCES: permission denied
# Solution: Use sudo or configure npm for user-local installs

# Option A: Use sudo
sudo npm install -g kritrima-ai

# Option B: Configure npm for user installs
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
npm install -g kritrima-ai
```

### Issue 2: Binary Not Found After Installation
```bash
# Check if binary exists
which kritrima-ai

# If not found, check npm global bin directory
npm config get prefix
ls -la $(npm config get prefix)/bin/

# Add npm bin to PATH
export PATH="$(npm config get prefix)/bin:$PATH"
```

### Issue 3: Module Resolution Errors
```bash
# Clear npm cache and reinstall
npm cache clean --force
sudo npm uninstall -g kritrima-ai
sudo npm install -g kritrima-ai
```

### Issue 4: Node.js Version Compatibility
```bash
# Check Node.js version (requires >= 18.0.0)
node --version

# If version is too old, update Node.js
# For Ubuntu/Debian:
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# For macOS with Homebrew:
brew install node@20

# For Windows:
# Download from https://nodejs.org/
```

## 🧪 Verification Steps

After installation, verify everything works:

```bash
# Check version
kritrima-ai --version

# Check help
kritrima-ai --help

# List available providers
kritrima-ai --list-providers

# Test basic functionality
kritrima-ai "echo hello" --single-pass
```

## 🔄 Complete Reinstallation

If all else fails, perform a complete clean reinstallation:

```bash
# 1. Remove all traces
sudo npm uninstall -g kritrima-ai 2>/dev/null || true
sudo rm -f /usr/bin/kritrima-ai /usr/local/bin/kritrima-ai /opt/homebrew/bin/kritrima-ai
rm -rf ~/.npm/_cacache
npm cache clean --force

# 2. Verify Node.js version
node --version  # Should be >= 18.0.0

# 3. Fresh installation
sudo npm install -g kritrima-ai

# 4. Verify
kritrima-ai --version
```

## 📞 Getting Help

If you continue to have issues:

1. **Check the logs**: Look at the complete npm error log mentioned in the error message
2. **Try different installation methods**: User-local vs global installation
3. **Check permissions**: Ensure you have proper permissions for the installation directory
4. **Update Node.js**: Make sure you're using Node.js >= 18.0.0
5. **Report issues**: Create an issue at https://github.com/kritrima-ai/kritrima-ai-cli/issues

## 🎯 Platform-Specific Notes

### Linux
- Requires Node.js >= 18.0.0
- May need sudo for global installation
- Binary installed to `/usr/bin/` or `/usr/local/bin/`

### macOS
- Works on both Intel and Apple Silicon
- May need sudo depending on Node.js installation method
- Homebrew Node.js typically doesn't require sudo

### Windows
- Works on Windows 10/11
- WSL support included
- No sudo required
- Binary installed to npm global directory
