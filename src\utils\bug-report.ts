/**
 * Bug Reporting & Telemetry System
 * 
 * Generates comprehensive bug reports with system information
 * Provides GitHub issue URL generation for easy reporting
 */

// Removed unused imports - these will be used in future bug report enhancements
import { CLI_VERSION } from '../version.js';
import { loadConfig } from './config.js';
import { getGitStatus } from './check-in-git.js';
import { getHistoryStats } from './storage/command-history.js';
import { getSessionStats } from './storage/save-rollout.js';

export interface BugReportData {
  version: string;
  timestamp: string;
  platform: {
    os: string;
    arch: string;
    node: string;
    shell?: string;
  };
  config: {
    provider: string;
    model: string;
    approvalMode: string;
    enableLogging: boolean;
    enableNotifications: boolean;
  };
  git: {
    inGit: boolean;
    root?: string;
    branch?: string;
    hasChanges?: boolean;
  };
  usage: {
    historyStats: any;
    sessionStats: any;
  };
  error?: {
    message: string;
    stack?: string;
    code?: string;
  };
  context?: {
    lastCommand?: string;
    workingDirectory: string;
    environment: Record<string, string>;
  };
}

/**
 * Generate comprehensive bug report
 */
export function generateBugReport(error?: Error, context?: any): BugReportData {
  const config = loadConfig();
  const gitStatus = getGitStatus();
  const historyStats = getHistoryStats();
  const sessionStats = getSessionStats();

  // Get shell information
  let shell: string | undefined;
  try {
    shell = process.env.SHELL || process.env.ComSpec || 'unknown';
  } catch (_e) {
    shell = 'unknown';
  }

  // Get environment variables (filtered for security)
  const environment: Record<string, string> = {};
  const safeEnvVars = [
    'NODE_VERSION',
    'NPM_VERSION',
    'TERM',
    'SHELL',
    'PATH',
    'HOME',
    'USER',
    'USERNAME',
    'COMPUTERNAME',
    'HOSTNAME'
  ];

  for (const key of safeEnvVars) {
    if (process.env[key]) {
      environment[key] = process.env[key]!;
    }
  }

  const report: BugReportData = {
    version: CLI_VERSION,
    timestamp: new Date().toISOString(),
    platform: {
      os: `${process.platform} ${process.arch}`,
      arch: process.arch,
      node: process.version,
      shell
    },
    config: {
      provider: config.provider,
      model: config.model,
      approvalMode: config.approvalMode,
      enableLogging: config.enableLogging || false,
      enableNotifications: config.enableNotifications !== false
    },
    git: gitStatus,
    usage: {
      historyStats,
      sessionStats
    },
    context: {
      workingDirectory: process.cwd(),
      environment
    }
  };

  // Add error information if provided
  if (error) {
    report.error = {
      message: error.message,
      stack: error.stack,
      code: (error as any).code
    };
  }

  // Add additional context if provided
  if (context) {
    report.context = {
      ...report.context,
      ...context
    };
  }

  return report;
}

/**
 * Generate GitHub issue URL for bug report
 */
export function generateGitHubIssueURL(
  report: BugReportData,
  title?: string,
  description?: string
): string {
  const baseURL = 'https://github.com/kritrima-ai/kritrima-ai-cli/issues/new';
  
  const issueTitle = title || (report.error 
    ? `Bug: ${report.error.message}` 
    : 'Bug Report');

  const issueBody = `
## Bug Report

**Description:**
${description || 'Please describe the issue you encountered.'}

## Environment

- **Version:** ${report.version}
- **Platform:** ${report.platform.os}
- **Node.js:** ${report.platform.node}
- **Shell:** ${report.platform.shell}

## Configuration

- **Provider:** ${report.config.provider}
- **Model:** ${report.config.model}
- **Approval Mode:** ${report.config.approvalMode}

## Error Details

${report.error ? `
**Error Message:** ${report.error.message}

**Stack Trace:**
\`\`\`
${report.error.stack || 'No stack trace available'}
\`\`\`
` : 'No error details available.'}

## Context

- **Working Directory:** ${report.context?.workingDirectory}
- **Git Repository:** ${report.git.inGit ? 'Yes' : 'No'}
${report.git.branch ? `- **Git Branch:** ${report.git.branch}` : ''}

## Usage Statistics

- **Command History:** ${report.usage.historyStats.totalCommands} commands
- **Sessions:** ${report.usage.sessionStats.totalSessions} sessions

## Additional Information

Please add any additional context, screenshots, or logs that might help diagnose the issue.

---

*This bug report was automatically generated by Kritrima AI CLI v${report.version}*
`.trim();

  const params = new URLSearchParams({
    title: issueTitle,
    body: issueBody,
    labels: 'bug'
  });

  return `${baseURL}?${params.toString()}`;
}

/**
 * Generate bug report and return GitHub URL
 */
export function createBugReportURL(
  error?: Error,
  title?: string,
  description?: string,
  context?: any
): string {
  const report = generateBugReport(error, context);
  return generateGitHubIssueURL(report, title, description);
}

/**
 * Save bug report to file
 */
export function saveBugReport(
  report: BugReportData,
  filePath?: string
): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const fileName = filePath || `bug-report-${timestamp}.json`;
  
  try {
    const fs = require('fs');
    fs.writeFileSync(fileName, JSON.stringify(report, null, 2));
    return fileName;
  } catch (error) {
    throw new Error(`Failed to save bug report: ${error}`);
  }
}

/**
 * Get system diagnostics
 */
export function getSystemDiagnostics(): {
  memory: NodeJS.MemoryUsage;
  uptime: number;
  loadAverage?: number[];
  cpuUsage: NodeJS.CpuUsage;
} {
  return {
    memory: process.memoryUsage(),
    uptime: process.uptime(),
    loadAverage: process.platform !== 'win32' ? require('os').loadavg() : undefined,
    cpuUsage: process.cpuUsage()
  };
}

/**
 * Check for common issues
 */
export function checkCommonIssues(): {
  issues: string[];
  warnings: string[];
} {
  const issues: string[] = [];
  const warnings: string[] = [];

  // Check Node.js version
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  if (majorVersion < 18) {
    issues.push(`Node.js version ${nodeVersion} is not supported. Please upgrade to Node.js 18 or later.`);
  }

  // Check for API keys
  const config = loadConfig();
  const envKey = `${config.provider.toUpperCase()}_API_KEY`;
  if (!process.env[envKey]) {
    issues.push(`Missing API key: ${envKey} environment variable is not set.`);
  }

  // Check memory usage
  const memory = process.memoryUsage();
  if (memory.heapUsed > 500 * 1024 * 1024) { // 500MB
    warnings.push('High memory usage detected. Consider restarting the CLI.');
  }

  // Check disk space (simplified)
  try {
    // File stats for future disk space analysis
    const _stats = require('fs').statSync(process.cwd());
    // This is a simplified check - in a real implementation you'd check available disk space
  } catch (_error) {
    warnings.push('Could not check disk space.');
  }

  return { issues, warnings };
}

/**
 * Format bug report for console display
 */
export function formatBugReportForConsole(report: BugReportData): string {
  const lines = [
    '🐛 Bug Report',
    '='.repeat(50),
    '',
    `Version: ${report.version}`,
    `Platform: ${report.platform.os}`,
    `Node.js: ${report.platform.node}`,
    `Provider: ${report.config.provider}/${report.config.model}`,
    '',
    'Error:',
    report.error ? `  ${report.error.message}` : '  No error information',
    '',
    'Context:',
    `  Working Directory: ${report.context?.workingDirectory}`,
    `  Git Repository: ${report.git.inGit ? 'Yes' : 'No'}`,
    '',
    'Usage:',
    `  Commands: ${report.usage.historyStats.totalCommands}`,
    `  Sessions: ${report.usage.sessionStats.totalSessions}`,
    '',
    '='.repeat(50)
  ];

  return lines.join('\n');
}
