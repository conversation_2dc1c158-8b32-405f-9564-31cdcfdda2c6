export interface NotificationOptions {
    title: string;
    message: string;
    icon?: string;
    sound?: boolean;
    timeout?: number;
    actions?: string[];
    wait?: boolean;
}
export declare function sendNotification(options: NotificationOptions): Promise<void>;
export declare function notifyCommandComplete(command: string, success: boolean, duration: number): Promise<void>;
export declare function notifyAIResponse(provider: string, model: string, responseLength: number): Promise<void>;
export declare function notifyError(title: string, error: string): Promise<void>;
export declare function notifyUpdate(currentVersion: string, latestVersion: string): Promise<void>;
export declare function notifySessionSaved(sessionId: string, itemCount: number): Promise<void>;
export declare function testNotifications(): Promise<boolean>;
export declare function areNotificationsSupported(): boolean;
export declare function getNotificationPreferences(): {
    enabled: boolean;
    sound: boolean;
    timeout: number;
};
export declare class NotificationManager {
    private queue;
    private isProcessing;
    private maxQueueSize;
    enqueue(options: NotificationOptions): Promise<void>;
    private processQueue;
    clear(): void;
    getQueueSize(): number;
}
export declare const notificationManager: NotificationManager;
//# sourceMappingURL=notifications.d.ts.map