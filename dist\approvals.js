export var ReviewDecision;
(function (ReviewDecision) {
    ReviewDecision["YES"] = "yes";
    ReviewDecision["NO_CONTINUE"] = "no_continue";
    ReviewDecision["NO_EXIT"] = "no_exit";
    ReviewDecision["ALWAYS"] = "always";
    ReviewDecision["EXPLAIN"] = "explain";
})(ReviewDecision || (ReviewDecision = {}));
export function canAutoApprove(command, approvalPolicy, safeCommands = []) {
    if (approvalPolicy === "suggest") {
        return false;
    }
    if (approvalPolicy === "full-auto") {
        return true;
    }
    const commandName = command[0]?.toLowerCase();
    return safeCommands.includes(commandName);
}
export function assessCommandRisk(command, workdir, config) {
    const commandName = command[0]?.toLowerCase();
    const dangerousCommands = config.dangerousCommands || [];
    const safeCommands = config.safeCommands || [];
    if (dangerousCommands.includes(commandName)) {
        return 'high';
    }
    const commandString = command.join(' ').toLowerCase();
    const dangerousPatterns = [
        /sudo/,
        /rm\s+-rf/,
        /chmod\s+777/,
        /dd\s+if=/,
        /mkfs/,
        /fdisk/,
        /format/,
        /del\s+\/s/,
        /rmdir\s+\/s/
    ];
    for (const pattern of dangerousPatterns) {
        if (pattern.test(commandString)) {
            return 'high';
        }
    }
    const mediumRiskPatterns = [
        /rm\s+/,
        /del\s+/,
        /move\s+/,
        /mv\s+/,
        /cp\s+.*\s+\//,
        /copy\s+.*\s+\\/,
        /chmod/,
        /chown/,
        /curl.*\|/,
        /wget.*\|/,
        />\s*\/dev/
    ];
    for (const pattern of mediumRiskPatterns) {
        if (pattern.test(commandString)) {
            return 'medium';
        }
    }
    if (safeCommands.includes(commandName)) {
        return 'low';
    }
    return 'medium';
}
export function generateCommandExplanation(command, _workdir) {
    const commandName = command[0]?.toLowerCase();
    const args = command.slice(1);
    const explanations = {
        'ls': (args) => `List directory contents${args.length ? ` with options: ${args.join(' ')}` : ''}`,
        'cat': (args) => `Display contents of file(s): ${args.join(', ')}`,
        'grep': (args) => `Search for pattern "${args[0]}" in ${args.slice(1).join(', ') || 'input'}`,
        'find': (args) => `Search for files/directories matching: ${args.join(' ')}`,
        'rm': (args) => `Delete file(s)/directory(ies): ${args.join(', ')}`,
        'mv': (args) => `Move/rename "${args[0]}" to "${args[1]}"`,
        'cp': (args) => `Copy "${args[0]}" to "${args[1]}"`,
        'mkdir': (args) => `Create directory(ies): ${args.join(', ')}`,
        'chmod': (args) => `Change permissions of "${args[1]}" to "${args[0]}"`,
        'chown': (args) => `Change ownership of "${args[1]}" to "${args[0]}"`,
        'curl': (args) => `Make HTTP request to: ${args.find(arg => arg.startsWith('http')) || args[0]}`,
        'wget': (args) => `Download file from: ${args.find(arg => arg.startsWith('http')) || args[0]}`,
        'git': (args) => `Git operation: ${args.join(' ')}`,
        'npm': (args) => `NPM operation: ${args.join(' ')}`,
        'pip': (args) => `Python package operation: ${args.join(' ')}`,
        'sudo': (args) => `Execute with elevated privileges: ${args.join(' ')}`
    };
    const explainer = explanations[commandName];
    if (explainer) {
        return explainer(args);
    }
    return `Execute command: ${command.join(' ')}`;
}
export function validateCommandSafety(command, workdir, config) {
    const warnings = [];
    const blockers = [];
    const commandString = command.join(' ');
    const risk = assessCommandRisk(command, workdir, config);
    if (risk === 'high') {
        const dangerousPatterns = [
            { pattern: /rm\s+-rf\s+\//, message: 'Attempting to delete root directory' },
            { pattern: /dd\s+if=.*of=\/dev/, message: 'Attempting to write to device file' },
            { pattern: /mkfs/, message: 'Attempting to format filesystem' },
            { pattern: /sudo\s+rm\s+-rf/, message: 'Attempting privileged recursive delete' }
        ];
        for (const { pattern, message } of dangerousPatterns) {
            if (pattern.test(commandString)) {
                blockers.push(message);
            }
        }
    }
    if (risk === 'medium' || risk === 'high') {
        const warningPatterns = [
            { pattern: /rm\s+/, message: 'Command will delete files' },
            { pattern: /chmod\s+777/, message: 'Setting overly permissive file permissions' },
            { pattern: /curl.*\|.*sh/, message: 'Downloading and executing script from internet' },
            { pattern: /sudo/, message: 'Command requires elevated privileges' }
        ];
        for (const { pattern, message } of warningPatterns) {
            if (pattern.test(commandString)) {
                warnings.push(message);
            }
        }
    }
    if (workdir === '/' || workdir === 'C:\\') {
        warnings.push('Command will execute in root directory');
    }
    return {
        safe: blockers.length === 0,
        warnings,
        blockers
    };
}
export function formatApprovalRequest(request) {
    const { command, workdir, explanation, metadata } = request;
    const commandString = command.join(' ');
    const title = `Execute Command: ${command[0]}`;
    const description = explanation || generateCommandExplanation(command, workdir);
    const details = [
        `Command: ${commandString}`,
        `Working Directory: ${workdir}`,
        `Risk Level: ${metadata?.estimatedRisk || 'unknown'}`
    ];
    if (metadata?.affectedFiles?.length) {
        details.push(`Affected Files: ${metadata.affectedFiles.join(', ')}`);
    }
    if (metadata?.networkAccess) {
        details.push('Network Access: Yes');
    }
    return {
        title,
        description,
        details,
        riskLevel: metadata?.estimatedRisk || 'unknown'
    };
}
export function createApprovalRequest(command, workdir, config) {
    const risk = assessCommandRisk(command, workdir, config);
    const explanation = generateCommandExplanation(command, workdir);
    validateCommandSafety(command, workdir, config);
    const affectedFiles = [];
    const commandString = command.join(' ');
    for (let i = 1; i < command.length; i++) {
        const arg = command[i];
        if (!arg.startsWith('-') && arg.includes('.')) {
            affectedFiles.push(arg);
        }
    }
    const networkAccess = /curl|wget|http|ftp|ssh|scp|rsync/.test(commandString);
    return {
        command,
        workdir,
        explanation,
        metadata: {
            estimatedRisk: risk,
            affectedFiles: affectedFiles.length > 0 ? affectedFiles : undefined,
            networkAccess: networkAccess || undefined
        }
    };
}
export function processApprovalResponse(response, request) {
    const { decision, customMessage, rememberChoice } = response;
    switch (decision) {
        case ReviewDecision.YES:
            return {
                approved: true,
                shouldContinue: true,
                shouldRemember: rememberChoice || false
            };
        case ReviewDecision.NO_CONTINUE:
            return {
                approved: false,
                shouldContinue: true,
                shouldRemember: rememberChoice || false,
                message: customMessage
            };
        case ReviewDecision.NO_EXIT:
            return {
                approved: false,
                shouldContinue: false,
                shouldRemember: false,
                message: customMessage
            };
        case ReviewDecision.ALWAYS:
            return {
                approved: true,
                shouldContinue: true,
                shouldRemember: true
            };
        case ReviewDecision.EXPLAIN:
            return {
                approved: false,
                shouldContinue: true,
                shouldRemember: false,
                message: `Explanation: ${request.explanation}`
            };
        default:
            return {
                approved: false,
                shouldContinue: true,
                shouldRemember: false
            };
    }
}
