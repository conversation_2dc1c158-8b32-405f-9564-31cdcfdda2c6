import notifier from 'node-notifier';
import { loadConfig } from './config.js';
import { logInfo, logError } from './logger/log.js';
export function sendNotification(options) {
    return new Promise((resolve, reject) => {
        const config = loadConfig();
        if (!config.enableNotifications) {
            resolve();
            return;
        }
        try {
            notifier.notify({
                title: options.title,
                message: options.message,
                icon: options.icon,
                sound: options.sound !== false,
                timeout: options.timeout || 5000,
                actions: options.actions,
                wait: options.wait || false
            }, (error, _response) => {
                if (error) {
                    logError('Notification failed', error);
                    reject(error);
                }
                else {
                    logInfo(`Notification sent: ${options.title}`);
                    resolve();
                }
            });
        }
        catch (error) {
            logError('Notification error', error instanceof Error ? error : new Error(String(error)));
            reject(error);
        }
    });
}
export async function notifyCommandComplete(command, success, duration) {
    const title = success ? '✅ Command Completed' : '❌ Command Failed';
    const message = `${command} (${duration}ms)`;
    await sendNotification({
        title,
        message,
        sound: !success
    });
}
export async function notifyAIResponse(provider, model, responseLength) {
    await sendNotification({
        title: '🤖 AI Response Ready',
        message: `${provider}/${model} - ${responseLength} characters`,
        sound: false
    });
}
export async function notifyError(title, error) {
    await sendNotification({
        title: `❌ ${title}`,
        message: error,
        sound: true,
        timeout: 10000
    });
}
export async function notifyUpdate(currentVersion, latestVersion) {
    await sendNotification({
        title: '🚀 Update Available',
        message: `Version ${latestVersion} is available (current: ${currentVersion})`,
        sound: false,
        timeout: 10000,
        actions: ['Update', 'Later']
    });
}
export async function notifySessionSaved(sessionId, itemCount) {
    await sendNotification({
        title: '💾 Session Saved',
        message: `Session ${sessionId} saved with ${itemCount} items`,
        sound: false,
        timeout: 3000
    });
}
export async function testNotifications() {
    try {
        await sendNotification({
            title: '🧪 Test Notification',
            message: 'Kritrima AI CLI notification system is working!',
            sound: false,
            timeout: 3000
        });
        return true;
    }
    catch (_error) {
        return false;
    }
}
export function areNotificationsSupported() {
    try {
        return !!notifier;
    }
    catch (_error) {
        return false;
    }
}
export function getNotificationPreferences() {
    const config = loadConfig();
    return {
        enabled: config.enableNotifications !== false,
        sound: true,
        timeout: 5000
    };
}
export class NotificationManager {
    queue = [];
    isProcessing = false;
    maxQueueSize = 10;
    async enqueue(options) {
        if (this.queue.length >= this.maxQueueSize) {
            this.queue.shift();
        }
        this.queue.push(options);
        await this.processQueue();
    }
    async processQueue() {
        if (this.isProcessing || this.queue.length === 0) {
            return;
        }
        this.isProcessing = true;
        while (this.queue.length > 0) {
            const notification = this.queue.shift();
            if (notification) {
                try {
                    await sendNotification(notification);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                catch (error) {
                    logError('Queued notification failed', error instanceof Error ? error : new Error(String(error)));
                }
            }
        }
        this.isProcessing = false;
    }
    clear() {
        this.queue = [];
    }
    getQueueSize() {
        return this.queue.length;
    }
}
export const notificationManager = new NotificationManager();
