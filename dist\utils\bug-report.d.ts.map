{"version": 3, "file": "bug-report.d.ts", "sourceRoot": "", "sources": ["../../src/utils/bug-report.ts"], "names": [], "mappings": "AAcA,MAAM,WAAW,aAAa;IAC5B,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE;QACR,EAAE,EAAE,MAAM,CAAC;QACX,IAAI,EAAE,MAAM,CAAC;QACb,IAAI,EAAE,MAAM,CAAC;QACb,KAAK,CAAC,EAAE,MAAM,CAAC;KAChB,CAAC;IACF,MAAM,EAAE;QACN,QAAQ,EAAE,MAAM,CAAC;QACjB,KAAK,EAAE,MAAM,CAAC;QACd,YAAY,EAAE,MAAM,CAAC;QACrB,aAAa,EAAE,OAAO,CAAC;QACvB,mBAAmB,EAAE,OAAO,CAAC;KAC9B,CAAC;IACF,GAAG,EAAE;QACH,KAAK,EAAE,OAAO,CAAC;QACf,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,UAAU,CAAC,EAAE,OAAO,CAAC;KACtB,CAAC;IACF,KAAK,EAAE;QACL,YAAY,EAAE,GAAG,CAAC;QAClB,YAAY,EAAE,GAAG,CAAC;KACnB,CAAC;IACF,KAAK,CAAC,EAAE;QACN,OAAO,EAAE,MAAM,CAAC;QAChB,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,IAAI,CAAC,EAAE,MAAM,CAAC;KACf,CAAC;IACF,OAAO,CAAC,EAAE;QACR,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,gBAAgB,EAAE,MAAM,CAAC;QACzB,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACrC,CAAC;CACH;AAKD,wBAAgB,iBAAiB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,aAAa,CAgF7E;AAKD,wBAAgB,sBAAsB,CACpC,MAAM,EAAE,aAAa,EACrB,KAAK,CAAC,EAAE,MAAM,EACd,WAAW,CAAC,EAAE,MAAM,GACnB,MAAM,CAgER;AAKD,wBAAgB,kBAAkB,CAChC,KAAK,CAAC,EAAE,KAAK,EACb,KAAK,CAAC,EAAE,MAAM,EACd,WAAW,CAAC,EAAE,MAAM,EACpB,OAAO,CAAC,EAAE,GAAG,GACZ,MAAM,CAGR;AAKD,wBAAgB,aAAa,CAC3B,MAAM,EAAE,aAAa,EACrB,QAAQ,CAAC,EAAE,MAAM,GAChB,MAAM,CAWR;AAKD,wBAAgB,oBAAoB,IAAI;IACtC,MAAM,EAAE,MAAM,CAAC,WAAW,CAAC;IAC3B,MAAM,EAAE,MAAM,CAAC;IACf,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC;IACvB,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC;CAC3B,CAOA;AAKD,wBAAgB,iBAAiB,IAAI;IACnC,MAAM,EAAE,MAAM,EAAE,CAAC;IACjB,QAAQ,EAAE,MAAM,EAAE,CAAC;CACpB,CAkCA;AAKD,wBAAgB,yBAAyB,CAAC,MAAM,EAAE,aAAa,GAAG,MAAM,CAyBvE"}