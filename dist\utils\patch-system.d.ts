export interface PatchOperation {
    type: 'add' | 'remove' | 'modify';
    file: string;
    lineNumber: number;
    content: string;
    originalContent?: string;
}
export interface Patch {
    id: string;
    timestamp: string;
    description: string;
    operations: PatchOperation[];
    checksum: string;
    metadata: {
        author?: string;
        version?: string;
        dependencies?: string[];
    };
}
export interface PatchResult {
    success: boolean;
    appliedOperations: number;
    failedOperations: PatchOperation[];
    conflicts: PatchConflict[];
    warnings: string[];
}
export interface PatchConflict {
    file: string;
    lineNumber: number;
    expected: string;
    actual: string;
    operation: PatchOperation;
}
export declare class PatchManager {
    private appliedPatches;
    private backupDirectory;
    constructor(backupDirectory?: string);
    generatePatch(originalFile: string, modifiedFile: string, description?: string): Promise<Patch>;
    applyPatch(patch: Patch, dryRun?: boolean): Promise<PatchResult>;
    private applyOperation;
    rollbackPatch(patchId: string): Promise<boolean>;
    private diffToOperations;
    private calculateChecksum;
    private generatePatchId;
    private backupFile;
    private restoreFile;
    private ensureBackupDirectory;
    private savePatchRecord;
    private removePatchRecord;
}
export declare const patchManager: PatchManager;
//# sourceMappingURL=patch-system.d.ts.map