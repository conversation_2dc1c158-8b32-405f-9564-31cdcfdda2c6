export interface BugReportData {
    version: string;
    timestamp: string;
    platform: {
        os: string;
        arch: string;
        node: string;
        shell?: string;
    };
    config: {
        provider: string;
        model: string;
        approvalMode: string;
        enableLogging: boolean;
        enableNotifications: boolean;
    };
    git: {
        inGit: boolean;
        root?: string;
        branch?: string;
        hasChanges?: boolean;
    };
    usage: {
        historyStats: any;
        sessionStats: any;
    };
    error?: {
        message: string;
        stack?: string;
        code?: string;
    };
    context?: {
        lastCommand?: string;
        workingDirectory: string;
        environment: Record<string, string>;
    };
}
export declare function generateBugReport(error?: Error, context?: any): BugReportData;
export declare function generateGitHubIssueURL(report: BugReportData, title?: string, description?: string): string;
export declare function createBugReportURL(error?: Error, title?: string, description?: string, context?: any): string;
export declare function saveBugReport(report: BugReportData, filePath?: string): string;
export declare function getSystemDiagnostics(): {
    memory: NodeJS.MemoryUsage;
    uptime: number;
    loadAverage?: number[];
    cpuUsage: NodeJS.CpuUsage;
};
export declare function checkCommonIssues(): {
    issues: string[];
    warnings: string[];
};
export declare function formatBugReportForConsole(report: BugReportData): string;
//# sourceMappingURL=bug-report.d.ts.map