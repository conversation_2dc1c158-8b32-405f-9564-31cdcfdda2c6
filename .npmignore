# Source files
src/
tsconfig.json
eslint.config.js

# Development files
.vscode/
.idea/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test files
test/
tests/
**/*.test.ts
**/*.test.tsx
**/*.spec.ts
**/*.spec.tsx
coverage/
.nyc_output/

# Build artifacts (keep dist/)
node_modules/
.cache/
.temp/
.tmp/

# Development configuration
.env
.env.local
.env.development
.env.test
.env.production

# Git files
.git/
.gitignore
.gitattributes

# Documentation (keep README.md)
docs/
*.md
!README.md

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Editor files
.DS_Store
Thumbs.db
*.swp
*.swo
*~

# Package manager files
yarn.lock
pnpm-lock.yaml
package-lock.json

# Development scripts
scripts/
tools/

# Plan and development files
plan.md
TODO.md
CHANGELOG.md
