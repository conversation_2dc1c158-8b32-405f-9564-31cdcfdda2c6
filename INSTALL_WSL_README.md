# Kritrima AI CLI - WSL Installation Script

## 🚀 Automatic WSL Installation Script

This script (`install-wsl.sh`) provides a complete, automated installation of the Kritrima AI CLI in your WSL environment. It handles all prerequisites, dependencies, configuration, and testing automatically.

## ✨ Features

- **Automatic Environment Detection**: Detects WSL version and Linux distribution
- **Node.js Management**: Installs or updates Node.js to the required version (18+)
- **Smart Project Setup**: Optimizes project location for WSL performance
- **Dependency Management**: Handles all npm dependencies and build processes
- **Configuration Setup**: Creates environment files and configuration
- **Global Installation**: Multiple installation methods (sudo, user-local, or fix script)
- **Comprehensive Testing**: Verifies installation and functionality
- **Error Handling**: Robust error handling with detailed feedback
- **Interactive & Automated Modes**: Supports both interactive and fully automated installation

## 🎯 Quick Start

### Method 1: Interactive Installation (Recommended)

```bash
# In your WSL terminal, navigate to the project directory
cd "/mnt/c/Users/<USER>/OneDrive/Documents/New folder (2)"

# Make the script executable
chmod +x install-wsl.sh

# Run the installation script
./install-wsl.sh
```

### Method 2: Fully Automated Installation

```bash
# For completely automated installation (auto-confirms all prompts)
./install-wsl.sh --yes

# Skip tests for faster installation
./install-wsl.sh --yes --skip-tests
```

### Method 3: Copy to WSL File System First (Best Performance)

```bash
# Copy project to WSL home directory for better performance
cp -r "/mnt/c/Users/<USER>/OneDrive/Documents/New folder (2)" ~/kritrima-ai-cli
cd ~/kritrima-ai-cli

# Make executable and run
chmod +x install-wsl.sh
./install-wsl.sh
```

## 📋 Installation Steps

The script performs these steps automatically:

1. **WSL Environment Check** - Verifies WSL compatibility and system requirements
2. **System Package Update** - Updates system packages and installs build tools
3. **Node.js Installation** - Installs/updates Node.js to version 18+ via NodeSource or nvm
4. **Project Directory Setup** - Optimizes project location for WSL performance
5. **Dependency Installation** - Installs all npm dependencies
6. **Project Build** - Compiles TypeScript and builds distribution files
7. **Environment Configuration** - Creates .env file and environment variables
8. **Configuration File Setup** - Creates ~/.kritrima.json with optimal settings
9. **Global Installation** - Installs CLI globally with multiple method options
10. **Installation Testing** - Verifies all functionality works correctly
11. **Integration Tests** - Runs project test suite (optional)
12. **Final Setup** - Creates workspace directories and quick-start script

## ⚙️ Command Line Options

```bash
./install-wsl.sh [OPTIONS]

Options:
  -y, --yes, --auto    Auto-confirm all prompts (fully automated)
  --skip-tests         Skip running the test suite
  -h, --help           Show help message
```

## 🔧 What Gets Installed/Configured

### System Requirements
- Node.js 18+ (installed automatically)
- npm (comes with Node.js)
- Git (installed if missing)
- Build tools (gcc, make, etc.)

### Project Files
- All npm dependencies
- Compiled TypeScript (dist/ directory)
- Executable binary (bin/kritrima-ai.js)

### Configuration Files
- `.env` - Environment variables and API keys
- `~/.kritrima.json` - Global configuration
- `~/.bashrc` - Updated with environment variables and PATH

### Global Installation
- `kritrima-ai` command available system-wide
- Multiple installation methods supported

### Additional Files
- `~/kritrima-ai-workspace/` - Workspace directories
- `~/kritrima-ai-quickstart.sh` - Quick start guide script

## 🎯 Post-Installation Steps

After the script completes successfully:

### 1. Configure API Key
```bash
# Edit the .env file to add your API key(s)
nano .env

# The script sets DeepSeek as default (recommended for cost-effectiveness):
DEEPSEEK_API_KEY=sk-your-actual-deepseek-key-here

# Or use other supported providers:
OPENAI_API_KEY=sk-your-actual-openai-key-here
ANTHROPIC_API_KEY=sk-ant-your-actual-anthropic-key-here
GEMINI_API_KEY=your-gemini-key-here
MISTRAL_API_KEY=your-mistral-key-here
XAI_API_KEY=your-xai-key-here
GROQ_API_KEY=your-groq-key-here
OPENROUTER_API_KEY=your-openrouter-key-here
```

### 2. Test Installation
```bash
# Check version
kritrima-ai --version

# Show help
kritrima-ai --help

# Test basic functionality
kritrima-ai "echo hello world" --single-pass
```

### 3. Start Using
```bash
# Interactive mode (uses DeepSeek by default)
kritrima-ai

# Use specific providers
kritrima-ai --provider deepseek --model deepseek-chat
kritrima-ai --provider openai --model gpt-4
kritrima-ai --provider anthropic --model claude-3-sonnet
kritrima-ai --provider gemini --model gemini-pro

# Single question mode
kritrima-ai "help me debug this code" --single-pass

# List all available providers
kritrima-ai --list-providers

# List models for a specific provider
kritrima-ai --list-models --provider deepseek
```

## 🛠️ Troubleshooting

### Command Not Found
```bash
# Reload shell configuration
source ~/.bashrc

# Check if binary exists
which kritrima-ai

# Check PATH
echo $PATH
```

### Permission Issues
```bash
# If global installation fails, try user-local installation
npm config set prefix ~/.local
npm install -g .
export PATH="$HOME/.local/bin:$PATH"
```

### Build Failures
```bash
# Clean and rebuild
rm -rf node_modules package-lock.json
npm install
npm run build
```

### API Key Issues
```bash
# Verify API key is set
echo $OPENAI_API_KEY

# Check .env file
cat .env

# Test with explicit key
OPENAI_API_KEY=your-key kritrima-ai "test" --single-pass
```

## 📁 File Locations

- **Project Directory**: Current directory or `~/kritrima-ai-cli`
- **Global Config**: `~/.kritrima.json`
- **Environment**: `.env` (in project directory)
- **Shell Config**: `~/.bashrc`
- **Workspace**: `~/kritrima-ai-workspace/`
- **Quick Start**: `~/kritrima-ai-quickstart.sh`

## 🔄 Reinstallation

To reinstall or fix issues:

```bash
# Run the script again (it handles cleanup automatically)
./install-wsl.sh

# For completely fresh installation
./install-wsl.sh --yes

# Manual cleanup before reinstall
sudo npm uninstall -g kritrima-ai
rm -rf node_modules package-lock.json dist
./install-wsl.sh
```

## 📞 Support

If you encounter issues:

1. **Check the detailed output** - The script provides comprehensive logging
2. **Try different installation methods** - The script offers multiple approaches
3. **Run with verbose output** - All commands show detailed progress
4. **Check system requirements** - Ensure WSL and Node.js compatibility
5. **Review the logs** - Look for specific error messages

## 🎉 Success Indicators

The installation is successful when you see:

- ✅ All installation steps completed without errors
- ✅ `kritrima-ai --version` shows the version number
- ✅ `kritrima-ai --help` displays help information
- ✅ Configuration files are created
- ✅ Environment variables are set

---

**Ready to install? Run `./install-wsl.sh` in your WSL terminal!** 🚀
