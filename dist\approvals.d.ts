import type { ApprovalPolicy, AppConfig } from './types/index.js';
export declare enum ReviewDecision {
    YES = "yes",
    NO_CONTINUE = "no_continue",
    NO_EXIT = "no_exit",
    ALWAYS = "always",
    EXPLAIN = "explain"
}
export interface ApprovalRequest {
    command: string[];
    workdir: string;
    explanation?: string;
    metadata?: {
        estimatedRisk: 'low' | 'medium' | 'high';
        affectedFiles?: string[];
        networkAccess?: boolean;
    };
}
export interface ApprovalResponse {
    decision: ReviewDecision;
    customMessage?: string;
    rememberChoice?: boolean;
}
export declare function canAutoApprove(command: string[], approvalPolicy: ApprovalPolicy, safeCommands?: string[]): boolean;
export declare function assessCommandRisk(command: string[], workdir: string, config: AppConfig): 'low' | 'medium' | 'high';
export declare function generateCommandExplanation(command: string[], _workdir: string): string;
export declare function validateCommandSafety(command: string[], workdir: string, config: AppConfig): {
    safe: boolean;
    warnings: string[];
    blockers: string[];
};
export declare function formatApprovalRequest(request: ApprovalRequest): {
    title: string;
    description: string;
    details: string[];
    riskLevel: string;
};
export declare function createApprovalRequest(command: string[], workdir: string, config: AppConfig): ApprovalRequest;
export declare function processApprovalResponse(response: ApprovalResponse, request: ApprovalRequest): {
    approved: boolean;
    shouldContinue: boolean;
    shouldRemember: boolean;
    message?: string;
};
//# sourceMappingURL=approvals.d.ts.map