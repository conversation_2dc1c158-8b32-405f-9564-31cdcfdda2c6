import { useState, useCallback, useRef, useEffect } from 'react';
export function useAgentState(options = {}) {
    const [state, setState] = useState('idle');
    const [currentOperation, setCurrentOperation] = useState(null);
    const [error, setError] = useState(null);
    const [progress, setProgress] = useState(0);
    const optionsRef = useRef(options);
    optionsRef.current = options;
    const isIdle = state === 'idle';
    const isThinking = state === 'thinking';
    const isExecuting = state === 'executing';
    const isStreaming = state === 'streaming';
    const hasError = state === 'error';
    useEffect(() => {
        optionsRef.current.onStateChange?.(state);
    }, [state]);
    useEffect(() => {
        if (error) {
            optionsRef.current.onError?.(error);
        }
    }, [error]);
    const startThinking = useCallback((operation) => {
        setState('thinking');
        setError(null);
        setProgress(0);
        if (operation) {
            setCurrentOperation(operation);
        }
    }, []);
    const startExecuting = useCallback((operation) => {
        setState('executing');
        setError(null);
        setProgress(0);
        setCurrentOperation(operation);
    }, []);
    const startStreaming = useCallback((operation) => {
        setState('streaming');
        setError(null);
        setProgress(0);
        if (operation) {
            setCurrentOperation(operation);
        }
    }, []);
    const setProgressValue = useCallback((newProgress) => {
        setProgress(Math.max(0, Math.min(100, newProgress)));
    }, []);
    const setErrorValue = useCallback((newError) => {
        setState('error');
        setError(newError);
        setProgress(0);
    }, []);
    const complete = useCallback((result) => {
        setState('idle');
        setError(null);
        setProgress(100);
        setTimeout(() => {
            setCurrentOperation(null);
            setProgress(0);
        }, 1000);
        optionsRef.current.onComplete?.(result);
    }, []);
    const reset = useCallback(() => {
        setState('idle');
        setCurrentOperation(null);
        setError(null);
        setProgress(0);
    }, []);
    const updateOperation = useCallback((updates) => {
        setCurrentOperation(prev => prev ? { ...prev, ...updates } : null);
    }, []);
    const addStep = useCallback((step) => {
        setCurrentOperation(prev => {
            if (!prev)
                return null;
            return {
                ...prev,
                steps: [...(prev.steps || []), {
                        id: `step-${Date.now()}`,
                        description: step,
                        status: 'pending',
                        timestamp: Date.now()
                    }]
            };
        });
    }, []);
    const completeStep = useCallback((stepIndex) => {
        setCurrentOperation(prev => {
            if (!prev || !prev.steps)
                return prev;
            const updatedSteps = [...prev.steps];
            if (updatedSteps[stepIndex]) {
                updatedSteps[stepIndex] = {
                    ...updatedSteps[stepIndex],
                    status: 'completed',
                    completedAt: Date.now()
                };
            }
            return {
                ...prev,
                steps: updatedSteps
            };
        });
    }, []);
    return {
        state,
        isIdle,
        isThinking,
        isExecuting,
        isStreaming,
        hasError,
        currentOperation,
        error,
        progress,
        startThinking,
        startExecuting,
        startStreaming,
        setProgress: setProgressValue,
        setError: setErrorValue,
        complete,
        reset,
        updateOperation,
        addStep,
        completeStep
    };
}
export function useAgentQueue(options = {}) {
    const [queue, setQueue] = useState([]);
    const [active, setActive] = useState([]);
    const [completed, setCompleted] = useState([]);
    const maxConcurrent = options.maxConcurrent || 1;
    const addToQueue = useCallback((operation) => {
        setQueue(prev => [...prev, operation]);
    }, []);
    const removeFromQueue = useCallback((operationId) => {
        setQueue(prev => prev.filter(op => op.id !== operationId));
    }, []);
    const startNext = useCallback(() => {
        if (active.length >= maxConcurrent || queue.length === 0) {
            return null;
        }
        const nextOperation = queue[0];
        setQueue(prev => prev.slice(1));
        setActive(prev => [...prev, nextOperation]);
        return nextOperation;
    }, [queue, active.length, maxConcurrent]);
    const completeOperation = useCallback((operationId, result) => {
        setActive(prev => {
            const operation = prev.find(op => op.id === operationId);
            if (operation) {
                setCompleted(completedPrev => [...completedPrev, {
                        ...operation,
                        status: 'completed',
                        result,
                        completedAt: Date.now()
                    }]);
            }
            return prev.filter(op => op.id !== operationId);
        });
    }, []);
    const failOperation = useCallback((operationId, error) => {
        setActive(prev => {
            const operation = prev.find(op => op.id === operationId);
            if (operation) {
                setCompleted(completedPrev => [...completedPrev, {
                        ...operation,
                        status: 'failed',
                        error,
                        completedAt: Date.now()
                    }]);
            }
            return prev.filter(op => op.id !== operationId);
        });
    }, []);
    const clearCompleted = useCallback(() => {
        setCompleted([]);
    }, []);
    const clearAll = useCallback(() => {
        setQueue([]);
        setActive([]);
        setCompleted([]);
    }, []);
    useEffect(() => {
        if (active.length < maxConcurrent && queue.length > 0) {
            const timer = setTimeout(startNext, 0);
            return () => clearTimeout(timer);
        }
    }, [active.length, queue.length, maxConcurrent, startNext]);
    useEffect(() => {
        options.onQueueChange?.(queue);
    }, [queue, options]);
    return {
        queue,
        active,
        completed,
        addToQueue,
        removeFromQueue,
        startNext,
        completeOperation,
        failOperation,
        clearCompleted,
        clearAll,
        totalPending: queue.length,
        totalActive: active.length,
        totalCompleted: completed.length,
        canStartNext: active.length < maxConcurrent && queue.length > 0
    };
}
export function useAgentMetrics() {
    const [metrics, setMetrics] = useState({
        totalOperations: 0,
        successfulOperations: 0,
        failedOperations: 0,
        averageExecutionTime: 0,
        totalExecutionTime: 0,
        operationsPerMinute: 0,
        lastOperationTime: 0
    });
    const recordOperation = useCallback((success, executionTime) => {
        setMetrics(prev => {
            const newTotal = prev.totalOperations + 1;
            const newSuccessful = success ? prev.successfulOperations + 1 : prev.successfulOperations;
            const newFailed = success ? prev.failedOperations : prev.failedOperations + 1;
            const newTotalTime = prev.totalExecutionTime + executionTime;
            const newAverageTime = newTotalTime / newTotal;
            const now = Date.now();
            const timeSinceStart = now - (prev.lastOperationTime || now);
            const opsPerMinute = timeSinceStart > 0 ? (newTotal / timeSinceStart) * 60000 : 0;
            return {
                totalOperations: newTotal,
                successfulOperations: newSuccessful,
                failedOperations: newFailed,
                averageExecutionTime: newAverageTime,
                totalExecutionTime: newTotalTime,
                operationsPerMinute: opsPerMinute,
                lastOperationTime: now
            };
        });
    }, []);
    const resetMetrics = useCallback(() => {
        setMetrics({
            totalOperations: 0,
            successfulOperations: 0,
            failedOperations: 0,
            averageExecutionTime: 0,
            totalExecutionTime: 0,
            operationsPerMinute: 0,
            lastOperationTime: 0
        });
    }, []);
    return {
        metrics,
        recordOperation,
        resetMetrics,
        successRate: metrics.totalOperations > 0 ?
            (metrics.successfulOperations / metrics.totalOperations) * 100 : 0,
        failureRate: metrics.totalOperations > 0 ?
            (metrics.failedOperations / metrics.totalOperations) * 100 : 0
    };
}
