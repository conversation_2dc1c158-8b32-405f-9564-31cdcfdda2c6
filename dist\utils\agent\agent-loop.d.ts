import type { ApprovalPolicy, ResponseItem, ResponseInputItem, ResponseFunctionToolCall, ResponseToolResult } from '../../types/index.js';
export interface AgentLoopConfig {
    model: string;
    provider: string;
    approvalPolicy: ApprovalPolicy;
    maxIterations?: number;
    timeout?: number;
    additionalWritableRoots?: string[];
    singlePass?: boolean;
    planningMode?: boolean;
    executionMode?: boolean;
    validationMode?: boolean;
    useFullContext?: boolean;
}
export interface AgentLoopCallbacks {
    onDelta?: (delta: string) => void;
    onComplete?: (content: string) => void;
    onError?: (error: string) => void;
    onToolCall?: (toolCall: ResponseFunctionToolCall) => void;
    onToolResult?: (result: ResponseToolResult) => void;
    getCommandConfirmation?: (command: string[], workdir: string) => Promise<boolean>;
}
export declare class AgentLoop {
    private model;
    private provider;
    private oai;
    private approvalPolicy;
    private transcript;
    private cumulativeThinkingMs;
    private additionalWritableRoots;
    private config;
    constructor(config: AgentLoopConfig);
    executeLoop(userInput: ResponseInputItem, callbacks?: AgentLoopCallbacks, maxIterations?: number): Promise<ResponseItem[]>;
    executeLoop(userInput: ResponseInputItem, options: {
        callbacks?: AgentLoopCallbacks;
        maxIterations?: number;
        singlePass?: boolean;
        planningMode?: boolean;
        executionMode?: boolean;
        validationMode?: boolean;
    }): Promise<ResponseItem[]>;
    private handleFunctionCall;
    private handleShellCommand;
    private isCommandSafe;
    private getAvailableTools;
    getTranscript(): ResponseInputItem[];
    clearTranscript(): void;
    getCumulativeThinkingTime(): number;
    updateConfig(newConfig: Partial<AgentLoopConfig>): void;
}
//# sourceMappingURL=agent-loop.d.ts.map