import type { Instance } from 'ink';
export declare function setInk<PERSON>enderer(renderer: Instance): void;
export declare function getInkRenderer(): Instance | null;
export declare function clearTerminal(): void;
export declare function clearScreen(): void;
export declare function moveCursor(row: number, col: number): void;
export declare function hideCursor(): void;
export declare function showCursor(): void;
export declare function enableAlternateScreen(): void;
export declare function disableAlternateScreen(): void;
export declare function setTerminalTitle(title: string): void;
export declare function restoreTerminalTitle(): void;
export declare function enableDebugMode(): void;
export declare function disableDebugMode(): void;
export declare function isDebugMode(): boolean;
export declare function debugLog(message: string, data?: any): void;
export declare function measureFPS(): () => void;
export declare function onExit(): void;
export declare function setupTerminal(): void;
export declare function getTerminalCapabilities(): {
    isTTY: boolean;
    hasColors: boolean;
    columns: number;
    rows: number;
    supportsUnicode: boolean;
    platform: string;
};
//# sourceMappingURL=terminal.d.ts.map