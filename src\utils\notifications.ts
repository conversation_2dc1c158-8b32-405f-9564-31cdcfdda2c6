/**
 * Desktop Notifications System
 * 
 * Provides cross-platform desktop notifications for important events
 * Includes notification management and user preferences
 */

import notifier from 'node-notifier';
import { loadConfig } from './config.js';
import { logInfo, logError } from './logger/log.js';

export interface NotificationOptions {
  title: string;
  message: string;
  icon?: string;
  sound?: boolean;
  timeout?: number;
  actions?: string[];
  wait?: boolean;
}

/**
 * Send desktop notification
 */
export function sendNotification(options: NotificationOptions): Promise<void> {
  return new Promise((resolve, reject) => {
    const config = loadConfig();
    
    // Check if notifications are enabled
    if (!config.enableNotifications) {
      resolve();
      return;
    }

    try {
      notifier.notify({
        title: options.title,
        message: options.message,
        icon: options.icon,
        sound: options.sound !== false,
        timeout: options.timeout || 5000,
        actions: options.actions,
        wait: options.wait || false
      }, (error, _response) => {
        if (error) {
          logError('Notification failed', error);
          reject(error);
        } else {
          logInfo(`Notification sent: ${options.title}`);
          resolve();
        }
      });
    } catch (error) {
      logError('Notification error', error instanceof Error ? error : new Error(String(error)));
      reject(error);
    }
  });
}

/**
 * Send command completion notification
 */
export async function notifyCommandComplete(
  command: string,
  success: boolean,
  duration: number
): Promise<void> {
  const title = success ? '✅ Command Completed' : '❌ Command Failed';
  const message = `${command} (${duration}ms)`;
  
  await sendNotification({
    title,
    message,
    sound: !success // Only sound on failure
  });
}

/**
 * Send AI response notification
 */
export async function notifyAIResponse(
  provider: string,
  model: string,
  responseLength: number
): Promise<void> {
  await sendNotification({
    title: '🤖 AI Response Ready',
    message: `${provider}/${model} - ${responseLength} characters`,
    sound: false
  });
}

/**
 * Send error notification
 */
export async function notifyError(
  title: string,
  error: string
): Promise<void> {
  await sendNotification({
    title: `❌ ${title}`,
    message: error,
    sound: true,
    timeout: 10000
  });
}

/**
 * Send update notification
 */
export async function notifyUpdate(
  currentVersion: string,
  latestVersion: string
): Promise<void> {
  await sendNotification({
    title: '🚀 Update Available',
    message: `Version ${latestVersion} is available (current: ${currentVersion})`,
    sound: false,
    timeout: 10000,
    actions: ['Update', 'Later']
  });
}

/**
 * Send session saved notification
 */
export async function notifySessionSaved(
  sessionId: string,
  itemCount: number
): Promise<void> {
  await sendNotification({
    title: '💾 Session Saved',
    message: `Session ${sessionId} saved with ${itemCount} items`,
    sound: false,
    timeout: 3000
  });
}

/**
 * Test notification system
 */
export async function testNotifications(): Promise<boolean> {
  try {
    await sendNotification({
      title: '🧪 Test Notification',
      message: 'Kritrima AI CLI notification system is working!',
      sound: false,
      timeout: 3000
    });
    return true;
  } catch (_error) {
    return false;
  }
}

/**
 * Check if notifications are supported
 */
export function areNotificationsSupported(): boolean {
  try {
    return !!notifier;
  } catch (_error) {
    return false;
  }
}

/**
 * Get notification preferences
 */
export function getNotificationPreferences(): {
  enabled: boolean;
  sound: boolean;
  timeout: number;
} {
  const config = loadConfig();
  
  return {
    enabled: config.enableNotifications !== false,
    sound: true, // Default to sound enabled
    timeout: 5000 // Default 5 second timeout
  };
}

/**
 * Notification manager class for advanced usage
 */
export class NotificationManager {
  private queue: NotificationOptions[] = [];
  private isProcessing = false;
  private maxQueueSize = 10;

  /**
   * Add notification to queue
   */
  async enqueue(options: NotificationOptions): Promise<void> {
    if (this.queue.length >= this.maxQueueSize) {
      this.queue.shift(); // Remove oldest notification
    }
    
    this.queue.push(options);
    await this.processQueue();
  }

  /**
   * Process notification queue
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.queue.length > 0) {
      const notification = this.queue.shift();
      if (notification) {
        try {
          await sendNotification(notification);
          // Small delay between notifications
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (error) {
          logError('Queued notification failed', error instanceof Error ? error : new Error(String(error)));
        }
      }
    }

    this.isProcessing = false;
  }

  /**
   * Clear notification queue
   */
  clear(): void {
    this.queue = [];
  }

  /**
   * Get queue size
   */
  getQueueSize(): number {
    return this.queue.length;
  }
}

// Global notification manager instance
export const notificationManager = new NotificationManager();
