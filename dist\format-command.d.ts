export declare function formatCommandForDisplay(command: Array<string>): string;
export declare function formatCommandForLogging(command: Array<string>): string;
export declare function formatCommandWithContext(command: Array<string>, workdir?: string): string;
export declare function truncateCommand(command: string, maxLength?: number): string;
export declare function formatCommandResult(command: Array<string>, exitCode: number, duration: number, workdir?: string): string;
export declare function getCommandName(command: Array<string>): string;
export declare function isDangerousCommand(command: Array<string>): boolean;
export declare function getCommandCategory(command: Array<string>): string;
//# sourceMappingURL=format-command.d.ts.map