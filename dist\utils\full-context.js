import { promises as fs } from 'fs';
import { dirname, extname, relative } from 'path';
import { glob } from 'glob';
import { getGitStatus } from './check-in-git.js';
import { logInfo, logError, logWarn } from './logger/log.js';
export class FullContextAnalyzer {
    maxFileSize = 1024 * 1024;
    maxTotalSize = 50 * 1024 * 1024;
    excludePatterns = [
        'node_modules/**',
        '.git/**',
        'dist/**',
        'build/**',
        '*.log',
        '*.tmp',
        '.DS_Store',
        'Thumbs.db'
    ];
    async analyzeProject(rootPath = process.cwd()) {
        logInfo(`Starting full context analysis for ${rootPath}`);
        try {
            const gitInfo = getGitStatus();
            const files = await this.discoverFiles(rootPath);
            const analyzedFiles = await this.analyzeFiles(files, rootPath);
            const structure = this.buildProjectStructure(analyzedFiles);
            const dependencies = await this.mapDependencies(analyzedFiles);
            const metadata = this.calculateMetadata(analyzedFiles);
            const context = {
                rootPath,
                files: analyzedFiles,
                structure,
                dependencies,
                gitInfo,
                metadata
            };
            logInfo(`Context analysis complete: ${analyzedFiles.length} files, ${metadata.totalSize} bytes`);
            return context;
        }
        catch (error) {
            logError('Failed to analyze project context', error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    async discoverFiles(rootPath) {
        const patterns = [
            '**/*.{js,jsx,ts,tsx,py,java,cpp,c,h,cs,php,rb,go,rs,swift,kt}',
            '**/*.{json,yaml,yml,xml,toml,ini,cfg,conf}',
            '**/*.{md,txt,rst,adoc}',
            '**/package.json',
            '**/requirements.txt',
            '**/Cargo.toml',
            '**/go.mod',
            '**/pom.xml',
            '**/build.gradle',
            '**/Makefile',
            '**/Dockerfile',
            '**/.env*',
            '**/README*'
        ];
        const allFiles = [];
        for (const pattern of patterns) {
            try {
                const files = await glob(pattern, {
                    cwd: rootPath,
                    ignore: this.excludePatterns,
                    absolute: true
                });
                allFiles.push(...files);
            }
            catch (error) {
                logWarn(`Failed to glob pattern ${pattern}: ${error}`);
            }
        }
        return [...new Set(allFiles)].sort();
    }
    async analyzeFiles(filePaths, rootPath) {
        const analyzedFiles = [];
        let totalSize = 0;
        for (const filePath of filePaths) {
            try {
                const stats = await fs.stat(filePath);
                if (stats.size > this.maxFileSize) {
                    logWarn(`Skipping large file: ${filePath} (${stats.size} bytes)`);
                    continue;
                }
                if (totalSize + stats.size > this.maxTotalSize) {
                    logWarn(`Reached total size limit, skipping remaining files`);
                    break;
                }
                const relativePath = relative(rootPath, filePath);
                const file = {
                    path: filePath,
                    relativePath,
                    size: stats.size,
                    type: this.determineFileType(filePath),
                    language: this.detectLanguage(filePath),
                    importance: this.calculateImportance(filePath, relativePath)
                };
                if (file.importance > 0.3 || file.type === 'config') {
                    try {
                        const content = await fs.readFile(filePath, 'utf-8');
                        file.content = content;
                        file.summary = this.generateSummary(content, file.language);
                        if (file.language) {
                            const analysis = this.analyzeSourceCode(content, file.language);
                            file.dependencies = analysis.dependencies;
                            file.exports = analysis.exports;
                            file.imports = analysis.imports;
                        }
                    }
                    catch (error) {
                        logWarn(`Failed to read file ${filePath}: ${error}`);
                    }
                }
                analyzedFiles.push(file);
                totalSize += stats.size;
            }
            catch (error) {
                logWarn(`Failed to analyze file ${filePath}: ${error}`);
            }
        }
        return analyzedFiles.sort((a, b) => b.importance - a.importance);
    }
    determineFileType(filePath) {
        const ext = extname(filePath).toLowerCase();
        const basename = filePath.split('/').pop()?.toLowerCase() || '';
        if (ext === '.json' || ext === '.yaml' || ext === '.yml' || ext === '.toml' ||
            ext === '.ini' || ext === '.cfg' || ext === '.conf' || basename.startsWith('.env')) {
            return 'config';
        }
        if (ext === '.md' || ext === '.txt' || ext === '.rst' || ext === '.adoc' ||
            basename.startsWith('readme')) {
            return 'documentation';
        }
        if (['.js', '.jsx', '.ts', '.tsx', '.py', '.java', '.cpp', '.c', '.h',
            '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt'].includes(ext)) {
            return 'source';
        }
        if (['.csv', '.xml', '.sql'].includes(ext)) {
            return 'data';
        }
        return 'other';
    }
    detectLanguage(filePath) {
        const ext = extname(filePath).toLowerCase();
        const languageMap = {
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.py': 'python',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.cs': 'csharp',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go',
            '.rs': 'rust',
            '.swift': 'swift',
            '.kt': 'kotlin'
        };
        return languageMap[ext];
    }
    calculateImportance(filePath, relativePath) {
        let importance = 0.1;
        const basename = filePath.split('/').pop()?.toLowerCase() || '';
        const pathParts = relativePath.split('/');
        if (['index.js', 'index.ts', 'main.py', 'app.py', 'main.java'].includes(basename)) {
            importance += 0.8;
        }
        if (['package.json', 'tsconfig.json', 'webpack.config.js', 'babel.config.js'].includes(basename)) {
            importance += 0.7;
        }
        if (basename.startsWith('readme')) {
            importance += 0.6;
        }
        importance += Math.max(0, 0.5 - pathParts.length * 0.1);
        if (pathParts.includes('src') || pathParts.includes('lib')) {
            importance += 0.3;
        }
        if (pathParts.includes('test') || pathParts.includes('tests') || basename.includes('test')) {
            importance -= 0.3;
        }
        return Math.max(0, Math.min(1, importance));
    }
    generateSummary(content, language) {
        const lines = content.split('\n');
        const maxLines = 5;
        if (language) {
            const summary = [];
            for (const line of lines.slice(0, 50)) {
                const trimmed = line.trim();
                if (trimmed.startsWith('class ') || trimmed.startsWith('function ') ||
                    trimmed.startsWith('def ') || trimmed.startsWith('export ')) {
                    summary.push(trimmed);
                    if (summary.length >= maxLines)
                        break;
                }
            }
            if (summary.length > 0) {
                return summary.join('\n');
            }
        }
        const nonEmptyLines = lines.filter(line => line.trim().length > 0);
        return nonEmptyLines.slice(0, maxLines).join('\n');
    }
    analyzeSourceCode(content, language) {
        const dependencies = [];
        const exports = [];
        const imports = [];
        const lines = content.split('\n');
        for (const line of lines) {
            const trimmed = line.trim();
            if (language === 'javascript' || language === 'typescript') {
                const importMatch = trimmed.match(/import.*from\s+['"]([^'"]+)['"]/);
                if (importMatch) {
                    imports.push(importMatch[1]);
                    if (!importMatch[1].startsWith('.')) {
                        dependencies.push(importMatch[1]);
                    }
                }
                const requireMatch = trimmed.match(/require\(['"]([^'"]+)['"]\)/);
                if (requireMatch) {
                    imports.push(requireMatch[1]);
                    if (!requireMatch[1].startsWith('.')) {
                        dependencies.push(requireMatch[1]);
                    }
                }
                if (trimmed.startsWith('export ')) {
                    const exportMatch = trimmed.match(/export\s+(?:class|function|const|let|var)\s+(\w+)/);
                    if (exportMatch) {
                        exports.push(exportMatch[1]);
                    }
                }
            }
            if (language === 'python') {
                const importMatch = trimmed.match(/^(?:from\s+(\S+)\s+)?import\s+(.+)/);
                if (importMatch) {
                    const module = importMatch[1] || importMatch[2].split(',')[0].trim();
                    imports.push(module);
                    if (!module.startsWith('.')) {
                        dependencies.push(module);
                    }
                }
                if (trimmed.startsWith('def ') || trimmed.startsWith('class ')) {
                    const defMatch = trimmed.match(/(?:def|class)\s+(\w+)/);
                    if (defMatch) {
                        exports.push(defMatch[1]);
                    }
                }
            }
        }
        return {
            dependencies: [...new Set(dependencies)],
            exports: [...new Set(exports)],
            imports: [...new Set(imports)]
        };
    }
    buildProjectStructure(files) {
        const directories = new Set();
        const filesByType = {};
        const configFiles = [];
        const entryPoints = [];
        for (const file of files) {
            const dir = dirname(file.relativePath);
            if (dir !== '.') {
                directories.add(dir);
            }
            if (!filesByType[file.type]) {
                filesByType[file.type] = [];
            }
            filesByType[file.type].push(file.relativePath);
            if (file.type === 'config') {
                configFiles.push(file.relativePath);
            }
            if (file.importance > 0.8) {
                entryPoints.push(file.relativePath);
            }
        }
        return {
            directories: Array.from(directories).sort(),
            filesByType,
            configFiles,
            entryPoints
        };
    }
    async mapDependencies(files) {
        const internal = {};
        const external = {};
        const circular = [];
        for (const file of files) {
            if (file.imports) {
                internal[file.relativePath] = [];
                external[file.relativePath] = [];
                for (const imp of file.imports) {
                    if (imp.startsWith('.')) {
                        internal[file.relativePath].push(imp);
                    }
                    else {
                        external[file.relativePath].push(imp);
                    }
                }
            }
        }
        for (const [file, deps] of Object.entries(internal)) {
            for (const dep of deps) {
                if (internal[dep]?.includes(file)) {
                    circular.push([file, dep]);
                }
            }
        }
        return { internal, external, circular };
    }
    calculateMetadata(files) {
        const languages = new Set();
        const frameworks = new Set();
        const buildTools = new Set();
        let totalSize = 0;
        for (const file of files) {
            totalSize += file.size;
            if (file.language) {
                languages.add(file.language);
            }
            if (file.dependencies) {
                for (const dep of file.dependencies) {
                    if (['react', 'vue', 'angular', 'express', 'fastapi', 'django'].includes(dep)) {
                        frameworks.add(dep);
                    }
                    if (['webpack', 'vite', 'rollup', 'parcel', 'gulp', 'grunt'].includes(dep)) {
                        buildTools.add(dep);
                    }
                }
            }
        }
        return {
            totalFiles: files.length,
            totalSize,
            languages: Array.from(languages),
            frameworks: Array.from(frameworks),
            buildTools: Array.from(buildTools)
        };
    }
}
export const fullContextAnalyzer = new FullContextAnalyzer();
let cachedContext = null;
let cacheTimestamp = 0;
const cacheTimeout = 5 * 60 * 1000;
export async function getProjectContext(rootPath = process.cwd(), forceRefresh = false) {
    const now = Date.now();
    if (!forceRefresh && cachedContext && (now - cacheTimestamp) < cacheTimeout) {
        return cachedContext;
    }
    cachedContext = await fullContextAnalyzer.analyzeProject(rootPath);
    cacheTimestamp = now;
    return cachedContext;
}
