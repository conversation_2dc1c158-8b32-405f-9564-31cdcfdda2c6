export interface SlashCommand {
    command: string;
    description: string;
    parameters?: string[];
    examples?: string[];
    handler?: (args: string[]) => Promise<string> | string;
}
export declare const SLASH_COMMANDS: SlashCommand[];
export declare function findSlashCommand(commandName: string): SlashCommand | null;
export declare function getSlashCommandNames(): string[];
export declare function isSlashCommand(text: string): boolean;
export declare function parseSlashCommand(text: string): {
    command: string;
    args: string[];
    isValid: boolean;
};
export declare function executeSlashCommand(text: string): Promise<{
    success: boolean;
    result: string;
    command?: string;
}>;
export declare function getSlashCommandSuggestions(input: string): string[];
//# sourceMappingURL=slash-commands.d.ts.map