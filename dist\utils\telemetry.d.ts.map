{"version": 3, "file": "telemetry.d.ts", "sourceRoot": "", "sources": ["../../src/utils/telemetry.ts"], "names": [], "mappings": "AAaA,MAAM,WAAW,cAAc;IAC7B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,kBAAkB,CAAC;IACzB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC1B,QAAQ,EAAE;QACR,OAAO,EAAE,MAAM,CAAC;QAChB,QAAQ,EAAE,MAAM,CAAC;QACjB,IAAI,EAAE,MAAM,CAAC;QACb,WAAW,EAAE,MAAM,CAAC;KACrB,CAAC;CACH;AAED,MAAM,MAAM,kBAAkB,GAC1B,WAAW,GACX,UAAU,GACV,kBAAkB,GAClB,eAAe,GACf,kBAAkB,GAClB,gBAAgB,GAChB,oBAAoB,GACpB,cAAc,GACd,iBAAiB,GACjB,gBAAgB,GAChB,gBAAgB,GAChB,aAAa,GACb,oBAAoB,GACpB,kBAAkB,GAClB,iBAAiB,CAAC;AAEtB,MAAM,WAAW,eAAe;IAC9B,OAAO,EAAE,OAAO,CAAC;IACjB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,aAAa,EAAE,MAAM,CAAC;IACtB,aAAa,EAAE,MAAM,CAAC;IACtB,SAAS,EAAE,OAAO,CAAC;IACnB,kBAAkB,EAAE,OAAO,CAAC;IAC5B,aAAa,EAAE,OAAO,CAAC;IACvB,YAAY,EAAE,OAAO,CAAC;CACvB;AAED,MAAM,WAAW,iBAAiB;IAChC,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAC/B;AAED,MAAM,WAAW,UAAU;IACzB,aAAa,EAAE,MAAM,CAAC;IACtB,aAAa,EAAE,MAAM,CAAC;IACtB,WAAW,EAAE,MAAM,CAAC;IACpB,sBAAsB,EAAE,MAAM,CAAC;IAC/B,gBAAgB,EAAE,MAAM,CAAC;IACzB,aAAa,EAAE,MAAM,CAAC;IACtB,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,YAAY,EAAE,MAAM,CAAC;CACtB;AAED,cAAM,gBAAgB;IACpB,OAAO,CAAC,MAAM,CAAkB;IAChC,OAAO,CAAC,SAAS,CAAS;IAC1B,OAAO,CAAC,MAAM,CAAC,CAAS;IACxB,OAAO,CAAC,MAAM,CAAwB;IACtC,OAAO,CAAC,UAAU,CAAC,CAAiB;IACpC,OAAO,CAAC,YAAY,CAAS;IAC7B,OAAO,CAAC,gBAAgB,CAAS;gBAErB,MAAM,GAAE,OAAO,CAAC,eAAe,CAAM;IAuBjD,OAAO,CAAC,mBAAmB;IAiC3B,UAAU,CAAC,IAAI,EAAE,kBAAkB,EAAE,IAAI,GAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAM,GAAG,IAAI;IAoC1E,gBAAgB,CAAC,MAAM,EAAE,iBAAiB,GAAG,IAAI;IAgBjD,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,GAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAM,GAAG,IAAI;IAgBjE,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,GAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAM,GAAG,IAAI;IAgB1G,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,GAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAM,GAAG,IAAI;IAcnE,aAAa,IAAI,UAAU;IA2B3B,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI;IAgB9C,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IA8BtB,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;IAyB/B,OAAO,CAAC,iBAAiB;IAOzB,OAAO,CAAC,eAAe;IAOvB,OAAO,CAAC,UAAU;IAkBlB,OAAO,CAAC,UAAU;IAiBlB,OAAO,CAAC,YAAY;IAgBpB,OAAO,CAAC,eAAe;IAYvB,OAAO,CAAC,eAAe;YAST,iBAAiB;YAgBjB,oBAAoB;CA6BnC;AAQD,wBAAgB,mBAAmB,CAAC,MAAM,GAAE,OAAO,CAAC,eAAe,CAAM,GAAG,IAAI,CAI/E;AAKD,wBAAgB,YAAY,IAAI,gBAAgB,GAAG,IAAI,CAEtD;AAKD,wBAAgB,UAAU,CAAC,IAAI,EAAE,kBAAkB,EAAE,IAAI,GAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAM,GAAG,IAAI,CAEzF;AAKD,wBAAgB,gBAAgB,CAAC,MAAM,EAAE,iBAAiB,GAAG,IAAI,CAEhE;AAKD,wBAAgB,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,GAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAM,GAAG,IAAI,CAEhF;AAKD,wBAAgB,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,GAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAM,GAAG,IAAI,CAEzH;AAKD,wBAAgB,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,GAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAM,GAAG,IAAI,CAElF;AAKD,wBAAsB,iBAAiB,IAAI,OAAO,CAAC,IAAI,CAAC,CAKvD"}