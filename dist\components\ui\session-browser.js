import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { listSessions, deleteSession, getSessionStats, formatFileSize } from '../../utils/storage/save-rollout.js';
export function SessionBrowser({ onLoad, onCancel }) {
    const [sessions, setSessions] = useState([]);
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [mode, setMode] = useState('browse');
    const [stats, setStats] = useState(null);
    const [loading, setLoading] = useState(true);
    useEffect(() => {
        const loadSessions = async () => {
            try {
                setLoading(true);
                const sessionList = listSessions();
                const sessionStats = getSessionStats();
                setSessions(sessionList);
                setStats(sessionStats);
            }
            catch (error) {
                console.error('Failed to load sessions:', error);
            }
            finally {
                setLoading(false);
            }
        };
        loadSessions();
    }, []);
    useInput((input, key) => {
        if (key.escape) {
            if (mode === 'confirm-delete') {
                setMode('browse');
            }
            else {
                onCancel();
            }
            return;
        }
        if (key.return) {
            const selectedSession = sessions[selectedIndex];
            if (!selectedSession)
                return;
            if (mode === 'confirm-delete') {
                const success = deleteSession(selectedSession.id);
                if (success) {
                    setSessions(prev => prev.filter((_, index) => index !== selectedIndex));
                    setSelectedIndex(prev => Math.min(prev, sessions.length - 2));
                }
                setMode('browse');
            }
            else {
                onLoad(selectedSession.id);
            }
            return;
        }
        if (key.upArrow) {
            setSelectedIndex(prev => Math.max(0, prev - 1));
            return;
        }
        if (key.downArrow) {
            setSelectedIndex(prev => Math.min(sessions.length - 1, prev + 1));
            return;
        }
        if (input === 'd' && mode === 'browse') {
            setMode('confirm-delete');
            return;
        }
        if (input === 'r' && mode === 'browse') {
            const sessionList = listSessions();
            setSessions(sessionList);
            return;
        }
    });
    const formatTimestamp = (timestamp) => {
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        if (diffHours < 1)
            return 'less than 1h ago';
        if (diffHours < 24)
            return `${diffHours}h ago`;
        if (diffDays < 7)
            return `${diffDays}d ago`;
        return date.toLocaleDateString();
    };
    const getProviderIcon = (provider) => {
        const icons = {
            'openai': '🤖',
            'anthropic': '🧠',
            'gemini': '💎',
            'mistral': '🌪️',
            'deepseek': '🔍',
            'xai': '🚀',
            'groq': '⚡',
            'ollama': '🦙'
        };
        return icons[provider.toLowerCase()] || '🤖';
    };
    if (loading) {
        return (_jsx(Box, { justifyContent: "center", alignItems: "center", height: 10, children: _jsx(Text, { color: "yellow", children: "Loading sessions..." }) }));
    }
    return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsx(Box, { borderStyle: "single", paddingX: 1, children: _jsx(Text, { bold: true, color: "blue", children: "Session Browser" }) }), stats && (_jsx(Box, { children: _jsxs(Text, { children: ["Total Sessions: ", _jsx(Text, { color: "cyan", children: stats.totalSessions }), stats.totalSize > 0 && (_jsxs(_Fragment, { children: [' • ', "Size: ", _jsx(Text, { color: "yellow", children: formatFileSize(stats.totalSize) })] }))] }) })), _jsx(Box, { flexDirection: "column", height: 12, children: sessions.length === 0 ? (_jsx(Box, { justifyContent: "center", alignItems: "center", height: "100%", children: _jsx(Text, { color: "gray", children: "No saved sessions found" }) })) : (sessions.slice(0, 12).map((session, index) => {
                    const isSelected = index === selectedIndex;
                    const providerIcon = getProviderIcon(session.provider);
                    const timestamp = formatTimestamp(session.timestamp);
                    return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Box, { children: _jsxs(Text, { color: isSelected ? 'cyan' : undefined, children: [isSelected ? '▶ ' : '  ', providerIcon, ' ', _jsxs(Text, { bold: isSelected, children: [session.provider, "/", session.model] }), _jsxs(Text, { color: "gray", children: [" \u2022 ", session.itemCount, " items"] })] }) }), isSelected && (_jsxs(Box, { flexDirection: "column", children: [_jsxs(Text, { color: "gray", children: ["ID: ", session.id] }), _jsxs(Text, { color: "gray", children: ["Created: ", timestamp] }), _jsxs(Text, { color: "gray", children: ["Last Activity: ", formatTimestamp(session.lastActivity)] })] }))] }, session.id));
                })) }), sessions.length > 12 && (_jsx(Box, { justifyContent: "center", children: _jsxs(Text, { color: "gray", children: ["... and ", sessions.length - 12, " more sessions"] }) })), sessions[selectedIndex] && (_jsx(Box, { borderStyle: "single", paddingX: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { bold: true, color: "cyan", children: "Selected Session:" }), _jsxs(Text, { children: [_jsx(Text, { color: "yellow", children: sessions[selectedIndex].provider }), "/", _jsx(Text, { color: "green", children: sessions[selectedIndex].model }), ' • ', _jsxs(Text, { color: "gray", children: [sessions[selectedIndex].itemCount, " items"] })] })] }) })), mode === 'confirm-delete' && sessions[selectedIndex] && (_jsx(Box, { borderStyle: "single", paddingX: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { bold: true, color: "red", children: "\u26A0\uFE0F Confirm Deletion" }), _jsxs(Text, { color: "yellow", children: ["Delete session: ", sessions[selectedIndex].id, "?"] }), _jsx(Text, { color: "gray", children: "This action cannot be undone." })] }) })), _jsx(Box, { borderStyle: "single", paddingX: 1, children: _jsx(Box, { flexDirection: "column", children: mode === 'confirm-delete' ? (_jsx(_Fragment, { children: _jsx(Text, { color: "red", children: "Enter Confirm deletion \u2022 Esc Cancel" }) })) : (_jsxs(_Fragment, { children: [_jsx(Text, { color: "gray", children: "\u2191/\u2193 Navigate \u2022 Enter Load \u2022 D Delete \u2022 R Refresh \u2022 Esc Cancel" }), sessions[selectedIndex] && (_jsxs(Text, { color: "cyan", children: ["Press Enter to load session: ", sessions[selectedIndex].id] }))] })) }) })] }));
}
