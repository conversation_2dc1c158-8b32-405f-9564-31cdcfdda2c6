export interface KeyboardShortcut {
    key: string;
    ctrl?: boolean;
    alt?: boolean;
    shift?: boolean;
    meta?: boolean;
    description: string;
    action: () => void;
    context?: string;
}
export declare function useKeyboardShortcuts(shortcuts: KeyboardShortcut[], context?: string, enabled?: boolean): {
    registerShortcut: (shortcut: KeyboardShortcut) => void;
    unregisterShortcut: (key: string) => void;
    getShortcuts: () => KeyboardShortcut[];
};
export declare const DEFAULT_SHORTCUTS: KeyboardShortcut[];
export declare function formatShortcut(shortcut: KeyboardShortcut): string;
//# sourceMappingURL=use-keyboard-shortcuts.d.ts.map