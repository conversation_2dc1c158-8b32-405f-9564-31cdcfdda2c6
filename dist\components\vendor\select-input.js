import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import { useState, useEffect, useCallback } from 'react';
import { Box, Text } from 'ink';
export function SelectInput({ options, value, onChange, onSubmit, multiple = false, placeholder = 'Select an option...', disabled = false, focus = false, maxHeight = 10, showIndicators = true, indicatorSelected = '●', indicatorUnselected = '○', indicatorFocus = '▶', }) {
    const [focusedIndex, setFocusedIndex] = useState(0);
    const [isFocused, setIsFocused] = useState(focus);
    const [selectedValues, setSelectedValues] = useState(() => {
        if (multiple) {
            return Array.isArray(value) ? value : value ? [value] : [];
        }
        return value ? [value] : [];
    });
    useEffect(() => {
        if (multiple) {
            setSelectedValues(Array.isArray(value) ? value : value ? [value] : []);
        }
        else {
            setSelectedValues(value ? [value] : []);
        }
    }, [value, multiple]);
    useEffect(() => {
        setIsFocused(focus);
    }, [focus]);
    const handleKeyPress = useCallback((ch, key) => {
        if (disabled || !isFocused)
            return;
        if (key.name === 'up' || key.name === 'k') {
            setFocusedIndex(prev => Math.max(0, prev - 1));
            return;
        }
        if (key.name === 'down' || key.name === 'j') {
            setFocusedIndex(prev => Math.min(options.length - 1, prev + 1));
            return;
        }
        if (key.name === 'space') {
            const option = options[focusedIndex];
            if (option && !option.disabled) {
                if (multiple) {
                    const newSelected = selectedValues.includes(option.value)
                        ? selectedValues.filter(v => v !== option.value)
                        : [...selectedValues, option.value];
                    setSelectedValues(newSelected);
                    onChange(newSelected);
                }
                else {
                    const newSelected = [option.value];
                    setSelectedValues(newSelected);
                    onChange(option.value);
                }
            }
            return;
        }
        if (key.name === 'return' || key.name === 'enter') {
            if (onSubmit) {
                onSubmit(multiple ? selectedValues : selectedValues[0] || '');
            }
            return;
        }
        if (ch && ch.match(/[a-zA-Z0-9]/)) {
            const matchingIndex = options.findIndex(option => option.label.toLowerCase().startsWith(ch.toLowerCase()));
            if (matchingIndex !== -1) {
                setFocusedIndex(matchingIndex);
            }
        }
    }, [disabled, isFocused, focusedIndex, options, selectedValues, multiple, onChange, onSubmit]);
    const getDisplayOptions = () => {
        if (options.length <= maxHeight) {
            return { options, startIndex: 0 };
        }
        const halfHeight = Math.floor(maxHeight / 2);
        let startIndex = Math.max(0, focusedIndex - halfHeight);
        let endIndex = startIndex + maxHeight;
        if (endIndex > options.length) {
            endIndex = options.length;
            startIndex = Math.max(0, endIndex - maxHeight);
        }
        return {
            options: options.slice(startIndex, endIndex),
            startIndex,
        };
    };
    const renderOption = (option, index, globalIndex) => {
        const isSelected = selectedValues.includes(option.value);
        const isFocusedOption = globalIndex === focusedIndex;
        const isDisabled = option.disabled;
        let indicator = '';
        if (showIndicators) {
            if (isFocusedOption) {
                indicator = indicatorFocus + ' ';
            }
            else {
                indicator = '  ';
            }
            if (multiple) {
                indicator += isSelected ? indicatorSelected : indicatorUnselected;
                indicator += ' ';
            }
        }
        const textColor = isDisabled ? 'gray' :
            isFocusedOption ? 'cyan' :
                isSelected ? 'green' : 'white';
        const backgroundColor = isFocusedOption ? 'blue' : undefined;
        return (_jsxs(Box, { children: [_jsxs(Text, { color: textColor, backgroundColor: backgroundColor, children: [indicator, option.label] }), option.description && (_jsxs(Text, { color: "gray", dimColor: true, children: [" - ", option.description] }))] }, option.value));
    };
    useEffect(() => {
        if (isFocused && typeof process !== 'undefined' && process.stdin) {
            process.stdin.on('keypress', handleKeyPress);
            return () => {
                process.stdin.off('keypress', handleKeyPress);
            };
        }
    }, [isFocused, handleKeyPress]);
    const { options: displayOptions, startIndex } = getDisplayOptions();
    if (options.length === 0) {
        return (_jsx(Box, { children: _jsx(Text, { dimColor: true, children: placeholder }) }));
    }
    return (_jsxs(Box, { flexDirection: "column", children: [displayOptions.map((option, index) => renderOption(option, index, startIndex + index)), options.length > maxHeight && (_jsx(Box, { marginTop: 1, children: _jsxs(Text, { dimColor: true, children: ["Showing ", startIndex + 1, "-", startIndex + displayOptions.length, " of ", options.length, focusedIndex < startIndex && ' (↑ more above)', focusedIndex >= startIndex + displayOptions.length && ' (↓ more below)'] }) })), multiple && selectedValues.length > 0 && (_jsx(Box, { marginTop: 1, children: _jsxs(Text, { color: "green", children: ["Selected: ", selectedValues.length, " item", selectedValues.length !== 1 ? 's' : ''] }) }))] }));
}
export function createOption(label, value, description, disabled = false) {
    return {
        label,
        value: value || label,
        description,
        disabled,
    };
}
export function createOptionsFromStrings(items) {
    return items.map(item => createOption(item));
}
