export interface SandboxOptions {
    timeout?: number;
    maxMemory?: number;
    maxCpu?: number;
    workingDirectory?: string;
    environment?: Record<string, string>;
    allowNetworking?: boolean;
    allowFileSystem?: boolean;
    restrictedPaths?: string[];
}
export interface ExecutionResult {
    success: boolean;
    stdout: string;
    stderr: string;
    exitCode: number | null;
    signal: string | null;
    duration: number;
    memoryUsage?: number;
    error?: string;
}
export interface SandboxEnvironment {
    id: string;
    workingDirectory: string;
    cleanup: () => Promise<void>;
}
export declare class SandboxExecutor {
    private activeProcesses;
    private environments;
    createEnvironment(options?: SandboxOptions): Promise<SandboxEnvironment>;
    executeCommand(command: string, args?: string[], options?: SandboxOptions): Promise<ExecutionResult>;
    executeJavaScript(code: string, options?: SandboxOptions): Promise<ExecutionResult>;
    executePython(code: string, options?: SandboxOptions): Promise<ExecutionResult>;
    executeShell(script: string, options?: SandboxOptions): Promise<ExecutionResult>;
    killAllProcesses(): void;
    cleanup(): Promise<void>;
    getActiveProcessCount(): number;
    getEnvironmentCount(): number;
}
export declare const sandboxExecutor: SandboxExecutor;
export declare function executeCode(code: string, language?: string, options?: SandboxOptions): Promise<ExecutionResult>;
export declare function validateSandboxOptions(options: SandboxOptions): string[];
export declare function createSecureSandbox(options?: SandboxOptions): Promise<SandboxEnvironment>;
export declare function executeWithMonitoring(command: string, args?: string[], options?: SandboxOptions): Promise<ExecutionResult & {
    resourceUsage: any;
}>;
//# sourceMappingURL=sandbox-execution.d.ts.map