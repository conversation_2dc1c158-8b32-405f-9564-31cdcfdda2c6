export type SpinnerType = 'dots' | 'line' | 'pipe' | 'star' | 'arrow' | 'bounce' | 'pulse' | 'wave' | 'clock' | 'earth';
export interface SpinnerProps {
    type?: SpinnerType;
    text?: string;
    color?: string;
    interval?: number;
    enabled?: boolean;
}
export declare function Spinner({ type, text, color, interval, enabled }: SpinnerProps): import("react/jsx-runtime").JSX.Element | null;
export interface LoadingSpinnerProps extends SpinnerProps {
    message?: string;
    showElapsed?: boolean;
    startTime?: number;
}
export declare function LoadingSpinner({ message, showElapsed, startTime, ...spinnerProps }: LoadingSpinnerProps): import("react/jsx-runtime").JSX.Element;
export interface ProgressSpinnerProps {
    steps: string[];
    currentStep: number;
    type?: SpinnerType;
    color?: string;
    completedColor?: string;
    enabled?: boolean;
}
export declare function ProgressSpinner({ steps, currentStep, type, color, completedColor, enabled }: ProgressSpinnerProps): import("react/jsx-runtime").JSX.Element;
export interface StatusSpinnerProps extends SpinnerProps {
    status: 'loading' | 'success' | 'error' | 'warning';
    successText?: string;
    errorText?: string;
    warningText?: string;
}
export declare function StatusSpinner({ status, text, successText, errorText, warningText, ...spinnerProps }: StatusSpinnerProps): import("react/jsx-runtime").JSX.Element;
export interface TypingIndicatorProps {
    color?: string;
    enabled?: boolean;
}
export declare function TypingIndicator({ color, enabled }: TypingIndicatorProps): import("react/jsx-runtime").JSX.Element | null;
export interface PulseIndicatorProps {
    color?: string;
    character?: string;
    interval?: number;
    enabled?: boolean;
}
export declare function PulseIndicator({ color, character, interval, enabled }: PulseIndicatorProps): import("react/jsx-runtime").JSX.Element | null;
export interface CustomSpinnerProps {
    frames: string[];
    text?: string;
    color?: string;
    interval?: number;
    enabled?: boolean;
}
export declare function CustomSpinner({ frames, text, color, interval, enabled }: CustomSpinnerProps): import("react/jsx-runtime").JSX.Element | null;
//# sourceMappingURL=spinner.d.ts.map