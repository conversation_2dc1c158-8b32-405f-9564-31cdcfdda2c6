import chalk from 'chalk';
import { AgentLoop } from './utils/agent/agent-loop.js';
import { createInputItem } from './utils/input-utils.js';
import { expandFileTags } from './utils/file-tag-utils.js';
import { formatCommandOutput } from './utils/agent/handle-exec-command.js';
import { logInfo, logError } from './utils/logger/log.js';
export async function runSinglePass(options) {
    const { originalPrompt, config, rootPath, fullContext: _fullContext } = options;
    console.log(chalk.blue('🤖 Kritrima AI CLI - Single Pass Mode'));
    console.log(chalk.gray(`Provider: ${config.provider} | Model: ${config.model}`));
    console.log(chalk.gray(`Approval: ${config.approvalMode}`));
    console.log();
    try {
        const agentLoop = new AgentLoop({
            model: config.model,
            provider: config.provider,
            approvalPolicy: config.approvalMode,
            additionalWritableRoots: config.additionalWritableRoots || []
        });
        console.log(chalk.blue('📝 Processing prompt...'));
        const expandedPrompt = await expandFileTags(originalPrompt, rootPath);
        if (expandedPrompt !== originalPrompt) {
            console.log(chalk.gray('✓ File references expanded'));
        }
        const inputItem = await createInputItem(expandedPrompt, []);
        console.log(chalk.blue('🚀 Executing AI request...'));
        console.log();
        let hasOutput = false;
        let toolCallCount = 0;
        const commandResults = [];
        const results = await agentLoop.executeLoop(inputItem, {
            onDelta: (delta) => {
                if (!hasOutput) {
                    console.log(chalk.green('🤖 AI Response:'));
                    hasOutput = true;
                }
                process.stdout.write(delta);
            },
            onComplete: (content) => {
                if (hasOutput) {
                    console.log();
                }
                logInfo(`Single-pass response completed - Length: ${content.length}`);
            },
            onError: (error) => {
                console.error(chalk.red('❌ Error:'), error);
                logError('Single-pass error', new Error(error));
            },
            onToolCall: (toolCall) => {
                toolCallCount++;
                console.log();
                console.log(chalk.yellow(`🔧 Tool Call ${toolCallCount}: ${toolCall.name}`));
                try {
                    const args = JSON.parse(toolCall.arguments);
                    if (args.command) {
                        console.log(chalk.gray(`Command: ${Array.isArray(args.command) ? args.command.join(' ') : args.command}`));
                    }
                }
                catch (_error) {
                    console.log(chalk.gray(`Arguments: ${toolCall.arguments}`));
                }
                logInfo(`Single-pass tool call: ${toolCall.name}`);
            },
            onToolResult: (result) => {
                const status = result.success ? chalk.green('✅ Success') : chalk.red('❌ Failed');
                console.log(`${status}: ${result.success ? 'Command executed' : 'Command failed'}`);
                if (result.metadata?.command) {
                    const formattedOutput = formatCommandOutput({
                        success: result.success,
                        output: result.result,
                        error: result.success ? undefined : result.result,
                        exitCode: result.metadata.exitCode || (result.success ? 0 : 1),
                        command: result.metadata.command,
                        workdir: result.metadata.workdir || process.cwd(),
                        duration: result.metadata.duration || 0
                    });
                    commandResults.push(formattedOutput);
                }
                logInfo(`Single-pass tool result - Success: ${result.success}, Command: ${result.metadata?.command?.join(' ') || 'unknown'}`);
            },
            getCommandConfirmation: async (command, workdir) => {
                return await handleSinglePassApproval(command, workdir, config.approvalMode);
            }
        }, 20);
        console.log();
        console.log(chalk.blue('📊 Execution Summary:'));
        console.log(chalk.gray(`• Total interactions: ${results.length}`));
        console.log(chalk.gray(`• Tool calls: ${toolCallCount}`));
        console.log(chalk.gray(`• Commands executed: ${commandResults.length}`));
        const thinkingTime = agentLoop.getCumulativeThinkingTime();
        if (thinkingTime > 0) {
            console.log(chalk.gray(`• Total thinking time: ${thinkingTime}ms`));
        }
        if (commandResults.length > 0) {
            console.log();
            console.log(chalk.blue('📋 Command Results:'));
            commandResults.forEach((result, index) => {
                console.log(chalk.gray(`--- Command ${index + 1} ---`));
                console.log(result);
                console.log();
            });
        }
        const hasErrors = results.some(item => item.type === 'tool_result' && !item.success);
        if (hasErrors) {
            console.log(chalk.yellow('⚠ Completed with some errors'));
            process.exit(1);
        }
        else {
            console.log(chalk.green('✅ Completed successfully'));
            process.exit(0);
        }
    }
    catch (error) {
        console.error();
        console.error(chalk.red('❌ Single-pass execution failed:'));
        console.error(chalk.red(error instanceof Error ? error.message : 'Unknown error'));
        if (process.env.DEBUG) {
            console.error(chalk.gray('Stack trace:'));
            console.error(error instanceof Error ? error.stack : error);
        }
        logError('Single-pass execution failed', error instanceof Error ? error : new Error(String(error)));
        process.exit(1);
    }
}
async function handleSinglePassApproval(command, workdir, approvalPolicy) {
    if (approvalPolicy === 'full-auto') {
        console.log(chalk.green('✓ Auto-approved (full-auto mode)'));
        return true;
    }
    if (approvalPolicy === 'auto-edit') {
        const safeCommands = ['ls', 'cat', 'grep', 'find', 'head', 'tail', 'wc', 'echo', 'pwd', 'which'];
        const commandName = command[0]?.toLowerCase();
        if (safeCommands.includes(commandName)) {
            console.log(chalk.green('✓ Auto-approved (safe command)'));
            return true;
        }
    }
    console.log(chalk.yellow('⚠ Command requires approval:'));
    console.log(chalk.gray(`  Command: ${command.join(' ')}`));
    console.log(chalk.gray(`  Working directory: ${workdir}`));
    console.log(chalk.yellow('⚠ Auto-approving for single-pass mode'));
    console.log(chalk.gray('  Use interactive mode for manual approval'));
    return true;
}
export function validateSinglePassConfig(config) {
    if (!config.originalPrompt || !config.originalPrompt.trim()) {
        throw new Error('Prompt is required for single-pass mode');
    }
    if (!config.config.model) {
        throw new Error('Model is required');
    }
    if (!config.config.provider) {
        throw new Error('Provider is required');
    }
    if (!config.rootPath) {
        throw new Error('Root path is required');
    }
}
export function getSinglePassHelp() {
    return `
Single-Pass Mode Usage:

  kritrima-ai "Your prompt here"
  kritrima-ai --single-pass "Your prompt here"

Options:
  --model <model>        AI model to use
  --provider <provider>  AI provider to use  
  --approval <mode>      Approval mode (suggest, auto-edit, full-auto)
  --full-context         Enable experimental full-context mode
  --debug               Enable debug logging

Examples:
  kritrima-ai "List all TypeScript files in this project"
  kritrima-ai --model gpt-4 "Analyze the code structure"
  kritrima-ai --approval full-auto "Run tests and fix any issues"

File References:
  Use @filename.ext to include file contents in your prompt
  Example: kritrima-ai "Review @src/app.ts and suggest improvements"

Note: Single-pass mode executes once and exits. Use interactive mode
for ongoing conversations and manual command approval.
`.trim();
}
