import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import { useState, useEffect, useCallback } from 'react';
import { Box, Text } from 'ink';
export function Notification({ notification, onDismiss, onAction: _onAction }) {
    const [timeLeft, setTimeLeft] = useState(notification.duration || 0);
    useEffect(() => {
        if (notification.duration && notification.duration > 0) {
            const _startTime = Date.now();
            const endTime = notification.timestamp + notification.duration;
            const timer = setInterval(() => {
                const now = Date.now();
                const remaining = Math.max(0, endTime - now);
                setTimeLeft(remaining);
                if (remaining === 0) {
                    onDismiss(notification.id);
                }
            }, 100);
            return () => clearInterval(timer);
        }
    }, [notification.id, notification.duration, notification.timestamp, onDismiss]);
    const getIcon = () => {
        switch (notification.type) {
            case 'success': return '✓';
            case 'error': return '✗';
            case 'warning': return '⚠';
            case 'info': return 'ℹ';
            default: return '•';
        }
    };
    const getColor = () => {
        switch (notification.type) {
            case 'success': return 'green';
            case 'error': return 'red';
            case 'warning': return 'yellow';
            case 'info': return 'blue';
            default: return 'white';
        }
    };
    const formatTimeLeft = (ms) => {
        const seconds = Math.ceil(ms / 1000);
        return `${seconds}s`;
    };
    return (_jsxs(Box, { borderStyle: "round", borderColor: getColor(), paddingX: 1, paddingY: 0, flexDirection: "column", children: [_jsxs(Box, { justifyContent: "space-between", children: [_jsx(Box, { children: _jsxs(Text, { color: getColor(), bold: true, children: [getIcon(), " ", notification.title] }) }), _jsxs(Box, { children: [notification.duration && notification.duration > 0 && timeLeft > 0 && (_jsx(Text, { color: "gray", dimColor: true, children: formatTimeLeft(timeLeft) })), _jsx(Text, { color: "gray", dimColor: true, children: "[ESC]" })] })] }), notification.message && (_jsx(Box, { children: _jsx(Text, { children: notification.message }) })), notification.actions && notification.actions.length > 0 && (_jsx(Box, { gap: 1, children: notification.actions.map((action, _index) => (_jsxs(Text, { color: "cyan", children: ["[", action.key, "] ", action.label] }, action.key))) }))] }));
}
export function NotificationManager({ notifications, maxVisible = 5, position = 'top', onDismiss, onAction }) {
    const sortedNotifications = [...notifications].sort((a, b) => {
        return position === 'top' ? b.timestamp - a.timestamp : a.timestamp - b.timestamp;
    });
    const visibleNotifications = sortedNotifications.slice(0, maxVisible);
    if (visibleNotifications.length === 0) {
        return null;
    }
    return (_jsxs(Box, { position: "absolute", width: 60, flexDirection: "column", children: [visibleNotifications.map(notification => (_jsx(Notification, { notification: notification, onDismiss: onDismiss, onAction: onAction }, notification.id))), notifications.length > maxVisible && (_jsx(Box, { paddingX: 1, children: _jsxs(Text, { color: "gray", dimColor: true, children: ["+", notifications.length - maxVisible, " more notifications"] }) }))] }));
}
export function useNotifications() {
    const [notifications, setNotifications] = useState([]);
    const addNotification = useCallback((type, title, message, options) => {
        const notification = {
            id: options?.id || `notification-${Date.now()}-${Math.random()}`,
            type,
            title,
            message,
            duration: options?.duration ?? (type === 'error' ? 0 : 5000),
            actions: options?.actions,
            timestamp: Date.now()
        };
        setNotifications(prev => [...prev, notification]);
        return notification.id;
    }, []);
    const dismissNotification = useCallback((id) => {
        setNotifications(prev => prev.filter(n => n.id !== id));
    }, []);
    const handleAction = useCallback((id, actionKey) => {
        const notification = notifications.find(n => n.id === id);
        if (notification) {
            const action = notification.actions?.find(a => a.key === actionKey);
            if (action) {
                action.action();
                dismissNotification(id);
            }
        }
    }, [notifications, dismissNotification]);
    const clearAll = useCallback(() => {
        setNotifications([]);
    }, []);
    const showInfo = useCallback((title, message, options) => {
        return addNotification('info', title, message, options);
    }, [addNotification]);
    const showSuccess = useCallback((title, message, options) => {
        return addNotification('success', title, message, options);
    }, [addNotification]);
    const showWarning = useCallback((title, message, options) => {
        return addNotification('warning', title, message, options);
    }, [addNotification]);
    const showError = useCallback((title, message, options) => {
        return addNotification('error', title, message, options);
    }, [addNotification]);
    return {
        notifications,
        addNotification,
        dismissNotification,
        handleAction,
        clearAll,
        showInfo,
        showSuccess,
        showWarning,
        showError
    };
}
export function Toast({ type, message, visible, onDismiss }) {
    useEffect(() => {
        if (visible && onDismiss) {
            const timer = setTimeout(onDismiss, 3000);
            return () => clearTimeout(timer);
        }
    }, [visible, onDismiss]);
    if (!visible) {
        return null;
    }
    const getIcon = () => {
        switch (type) {
            case 'success': return '✓';
            case 'error': return '✗';
            case 'warning': return '⚠';
            case 'info': return 'ℹ';
            default: return '•';
        }
    };
    const getColor = () => {
        switch (type) {
            case 'success': return 'green';
            case 'error': return 'red';
            case 'warning': return 'yellow';
            case 'info': return 'blue';
            default: return 'white';
        }
    };
    return (_jsx(Box, { position: "absolute", borderStyle: "round", borderColor: getColor(), paddingX: 2, paddingY: 1, children: _jsxs(Text, { color: getColor(), children: [getIcon(), " ", message] }) }));
}
export function ProgressNotification({ title, progress, message, visible }) {
    if (!visible) {
        return null;
    }
    const progressBar = '█'.repeat(Math.floor(progress / 5)) +
        '░'.repeat(20 - Math.floor(progress / 5));
    return (_jsxs(Box, { position: "absolute", borderStyle: "round", borderColor: "blue", paddingX: 2, paddingY: 1, flexDirection: "column", children: [_jsx(Text, { color: "blue", bold: true, children: title }), _jsx(Box, { children: _jsxs(Text, { color: "cyan", children: ["[", progressBar, "] ", Math.round(progress), "%"] }) }), message && (_jsx(Box, { children: _jsx(Text, { color: "gray", children: message }) }))] }));
}
