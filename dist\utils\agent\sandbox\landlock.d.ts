import type { ExecInput, ExecResult, AppConfig } from '../../../types/index.js';
export declare function execWithLandlock(input: ExecInput, config: AppConfig, additionalWritableRoots?: ReadonlyArray<string>): Promise<ExecResult>;
export declare function testLandlock(): Promise<{
    available: boolean;
    version?: string;
    capabilities: string[];
    limitations: string[];
}>;
export declare function getLandlockEnvironment(): {
    platform: string;
    sandboxing: boolean;
    restrictions: string[];
};
//# sourceMappingURL=landlock.d.ts.map