export interface DiffOptions {
    cached?: boolean;
    files?: string[];
    context?: number;
    wordDiff?: boolean;
    noColor?: boolean;
    unified?: boolean;
}
export interface DiffResult {
    success: boolean;
    output: string;
    error?: string;
    files: string[];
    stats: {
        additions: number;
        deletions: number;
        files: number;
    };
}
export declare function getGitDiff(options?: DiffOptions, workdir?: string): DiffResult;
export declare function getDiffStats(workdir?: string, options?: DiffOptions): {
    additions: number;
    deletions: number;
    files: number;
};
export declare function formatDiffOutput(diffOutput: string): string;
export declare function getFileDiff(filePath: string, options?: DiffOptions, workdir?: string): DiffResult;
export declare function getCommitDiff(fromCommit: string, toCommit?: string, options?: DiffOptions, workdir?: string): DiffResult;
export declare function hasChangesToDiff(options?: DiffOptions, workdir?: string): boolean;
export declare function getDiffSummary(options?: DiffOptions, workdir?: string): {
    hasChanges: boolean;
    stats: {
        additions: number;
        deletions: number;
        files: number;
    };
    files: string[];
};
export declare function createPatchFile(outputPath: string, options?: DiffOptions, workdir?: string): boolean;
//# sourceMappingURL=diff-utils.d.ts.map