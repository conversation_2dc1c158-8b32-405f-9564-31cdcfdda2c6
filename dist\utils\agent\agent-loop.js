import { createOpenAIClient } from '../openai-client.js';
import { loadConfig } from '../config.js';
import { logAgentExecution, logError } from '../logger/log.js';
import { handleExecCommand } from './handle-exec-command.js';
import { convertMessagesToOpenAI, estimateTokenCount } from '../responses.js';
export class AgentLoop {
    model;
    provider;
    oai;
    approvalPolicy;
    transcript = [];
    cumulativeThinkingMs = 0;
    additionalWritableRoots;
    config;
    constructor(config) {
        this.model = config.model;
        this.provider = config.provider;
        this.approvalPolicy = config.approvalPolicy;
        this.additionalWritableRoots = config.additionalWritableRoots || [];
        this.config = loadConfig();
        this.oai = createOpenAIClient({
            provider: this.provider,
            timeout: config.timeout
        });
    }
    async executeLoop(userInput, callbacksOrOptions = {}, maxIterations = 10) {
        let callbacks = {};
        let actualMaxIterations = maxIterations;
        let singlePass = false;
        if ('callbacks' in callbacksOrOptions || 'maxIterations' in callbacksOrOptions) {
            const options = callbacksOrOptions;
            callbacks = options.callbacks || {};
            actualMaxIterations = options.maxIterations || 10;
            singlePass = options.singlePass || false;
        }
        else {
            callbacks = callbacksOrOptions;
        }
        if (singlePass) {
            actualMaxIterations = 1;
        }
        const startTime = Date.now();
        const results = [];
        try {
            this.transcript.push(userInput);
            results.push(userInput);
            logAgentExecution('loop_start', {
                model: this.model,
                provider: this.provider,
                approvalPolicy: this.approvalPolicy
            });
            let iteration = 0;
            while (iteration < actualMaxIterations) {
                iteration++;
                logAgentExecution('iteration_start', { iteration });
                const messages = convertMessagesToOpenAI([...this.transcript, userInput]);
                const tokenCount = estimateTokenCount(messages);
                logAgentExecution('token_count', { tokens: tokenCount });
                const tools = this.getAvailableTools();
                const iterationStartTime = Date.now();
                try {
                    const completion = await this.oai.chat.completions.create({
                        model: this.model,
                        messages,
                        tools: tools.length > 0 ? tools : undefined,
                        tool_choice: tools.length > 0 ? 'auto' : undefined,
                        temperature: this.config.temperature || 0.7,
                        max_tokens: this.config.maxTokens || 4096,
                        stream: false
                    });
                    const iterationTime = Date.now() - iterationStartTime;
                    this.cumulativeThinkingMs += iterationTime;
                    const choice = completion.choices?.[0];
                    if (!choice) {
                        throw new Error('No response choice received');
                    }
                    const content = choice.message?.content || '';
                    if (content.trim()) {
                        callbacks.onDelta?.(content);
                        callbacks.onComplete?.(content);
                        const assistantResponse = {
                            role: 'assistant',
                            content,
                            type: 'output',
                            timestamp: Date.now(),
                            metadata: {
                                model: this.model,
                                provider: this.provider,
                                thinkingTime: iterationTime
                            }
                        };
                        results.push(assistantResponse);
                    }
                    if (choice.message?.tool_calls) {
                        for (const toolCall of choice.message.tool_calls) {
                            if (toolCall.type === 'function') {
                                const functionCall = {
                                    id: toolCall.id,
                                    name: toolCall.function.name,
                                    arguments: toolCall.function.arguments,
                                    type: 'function_call',
                                    timestamp: Date.now()
                                };
                                results.push(functionCall);
                                callbacks.onToolCall?.(functionCall);
                                const toolResults = await this.handleFunctionCall(functionCall, callbacks);
                                results.push(...toolResults);
                            }
                        }
                    }
                    else {
                        logAgentExecution('loop_complete', {
                            iterations: iteration,
                            totalTime: Date.now() - startTime
                        });
                        return results;
                    }
                }
                catch (error) {
                    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                    logError('Agent loop iteration failed', error instanceof Error ? error : new Error(errorMessage));
                    callbacks.onError?.(errorMessage);
                    throw error;
                }
            }
            logAgentExecution('loop_max_iterations', { maxIterations: actualMaxIterations });
            return results;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            logError('Agent loop failed', error instanceof Error ? error : new Error(errorMessage));
            callbacks.onError?.(errorMessage);
            throw error;
        }
    }
    async handleFunctionCall(functionCall, callbacks) {
        const { name, arguments: argsString } = functionCall;
        try {
            let args;
            try {
                args = JSON.parse(argsString);
            }
            catch (error) {
                throw new Error(`Invalid function arguments: ${argsString}`);
            }
            if (name === 'shell' || name === 'local_shell') {
                return await this.handleShellCommand(args, functionCall.id, callbacks);
            }
            throw new Error(`Unknown function: ${name}`);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorResult = {
                id: functionCall.id,
                result: `Error: ${errorMessage}`,
                success: false,
                type: 'tool_result',
                timestamp: Date.now()
            };
            return [errorResult];
        }
    }
    async handleShellCommand(args, toolCallId, callbacks) {
        try {
            const execInput = {
                command: args.command || [],
                workdir: args.workdir || process.cwd(),
                timeout: args.timeout || 30000
            };
            const needsApproval = this.approvalPolicy === 'suggest' ||
                (this.approvalPolicy === 'auto-edit' && !this.isCommandSafe(execInput.command));
            if (needsApproval && callbacks.getCommandConfirmation) {
                const approved = await callbacks.getCommandConfirmation(execInput.command, execInput.workdir || process.cwd());
                if (!approved) {
                    const deniedResult = {
                        id: toolCallId,
                        result: 'Command execution denied by user',
                        success: false,
                        type: 'tool_result',
                        timestamp: Date.now(),
                        metadata: {
                            command: execInput.command,
                            workdir: execInput.workdir
                        }
                    };
                    return [deniedResult];
                }
            }
            const execResult = await handleExecCommand(execInput, this.config, this.approvalPolicy, this.additionalWritableRoots);
            const toolResult = {
                id: toolCallId,
                result: execResult.output,
                success: execResult.success,
                type: 'tool_result',
                timestamp: Date.now(),
                metadata: {
                    command: execResult.command,
                    workdir: execResult.workdir,
                    exitCode: execResult.exitCode,
                    duration: execResult.duration
                }
            };
            callbacks.onToolResult?.(toolResult);
            return [toolResult];
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorResult = {
                id: toolCallId,
                result: `Error executing command: ${errorMessage}`,
                success: false,
                type: 'tool_result',
                timestamp: Date.now()
            };
            return [errorResult];
        }
    }
    isCommandSafe(command) {
        const safeCommands = this.config.safeCommands || [];
        const commandName = command[0]?.toLowerCase();
        return safeCommands.includes(commandName);
    }
    getAvailableTools() {
        const tools = [];
        tools.push({
            type: 'function',
            function: {
                name: 'shell',
                description: 'Execute shell commands and return their output. Use this to run system commands, file operations, and interact with the environment.',
                parameters: {
                    type: 'object',
                    properties: {
                        command: {
                            type: 'array',
                            items: { type: 'string' },
                            description: 'The command to execute as an array of strings (command and arguments)'
                        },
                        workdir: {
                            type: 'string',
                            description: 'Working directory for command execution (optional, defaults to current directory)'
                        },
                        timeout: {
                            type: 'number',
                            description: 'Timeout in milliseconds (optional, defaults to 30000)'
                        }
                    },
                    required: ['command']
                }
            }
        });
        return tools;
    }
    getTranscript() {
        return [...this.transcript];
    }
    clearTranscript() {
        this.transcript = [];
    }
    getCumulativeThinkingTime() {
        return this.cumulativeThinkingMs;
    }
    updateConfig(newConfig) {
        if (newConfig.model)
            this.model = newConfig.model;
        if (newConfig.provider)
            this.provider = newConfig.provider;
        if (newConfig.approvalPolicy)
            this.approvalPolicy = newConfig.approvalPolicy;
        if (newConfig.additionalWritableRoots)
            this.additionalWritableRoots = newConfig.additionalWritableRoots;
        if (newConfig.provider || newConfig.model) {
            this.oai = createOpenAIClient({
                provider: this.provider,
                timeout: newConfig.timeout
            });
        }
    }
}
