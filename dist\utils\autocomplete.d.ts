export interface CompletionResult {
    suggestions: string[];
    prefix: string;
    hasMore: boolean;
    type: 'command' | 'file' | 'slash' | 'history' | 'mixed';
}
export interface CompletionOptions {
    maxSuggestions?: number;
    includeHidden?: boolean;
    fuzzyMatch?: boolean;
    contextAware?: boolean;
}
export declare function getCompletions(input: string, cursorPosition?: number, options?: CompletionOptions): CompletionResult;
export declare function getFileTypeCompletions(directory?: string, extensions?: string[]): string[];
export declare function getDirectoryCompletions(basePath?: string, includeHidden?: boolean): string[];
export declare class AutoCompleteManager {
    private cache;
    private cacheTimeout;
    getCompletions(input: string, cursorPosition?: number, options?: CompletionOptions): CompletionResult;
    clearCache(): void;
    getCacheSize(): number;
}
export declare const autoCompleteManager: AutoCompleteManager;
export declare function getFileSystemSuggestions(query: string, basePath?: string, maxResults?: number): string[];
//# sourceMappingURL=autocomplete.d.ts.map