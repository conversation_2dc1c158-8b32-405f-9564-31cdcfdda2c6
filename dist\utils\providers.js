export const providers = {
    openai: {
        name: "OpenAI",
        baseURL: "https://api.openai.com/v1",
        envKey: "OPENAI_API_KEY"
    },
    azure: {
        name: "Azure OpenAI",
        baseURL: "https://your-resource.openai.azure.com",
        envKey: "AZURE_OPENAI_API_KEY"
    },
    gemini: {
        name: "Google Gemini",
        baseURL: "https://generativelanguage.googleapis.com/v1beta",
        envKey: "GEMINI_API_KEY"
    },
    ollama: {
        name: "Ollama",
        baseURL: "http://localhost:11434/v1",
        envKey: "OLLAMA_API_KEY"
    },
    mistral: {
        name: "Mistral AI",
        baseURL: "https://api.mistral.ai/v1",
        envKey: "MISTRAL_API_KEY"
    },
    deepseek: {
        name: "DeepSeek",
        baseURL: "https://api.deepseek.com/v1",
        envKey: "DEEPSEEK_API_KEY"
    },
    xai: {
        name: "xAI",
        baseURL: "https://api.x.ai/v1",
        envKey: "XAI_API_KEY"
    },
    groq: {
        name: "Groq",
        baseURL: "https://api.groq.com/openai/v1",
        envKey: "GROQ_API_KEY"
    },
    arceeai: {
        name: "ArceeAI",
        baseURL: "https://api.arcee.ai/v1",
        envKey: "ARCEEAI_API_KEY"
    },
    openrouter: {
        name: "OpenRouter",
        baseURL: "https://openrouter.ai/api/v1",
        envKey: "OPENROUTER_API_KEY"
    }
};
export function getProviderConfig(providerName, customProviders) {
    if (customProviders && customProviders[providerName.toLowerCase()]) {
        return customProviders[providerName.toLowerCase()];
    }
    const provider = providers[providerName.toLowerCase()];
    if (provider) {
        return provider;
    }
    return null;
}
export function getAvailableProviders(customProviders) {
    const builtInProviders = Object.keys(providers);
    const customProviderNames = customProviders ? Object.keys(customProviders) : [];
    return [...builtInProviders, ...customProviderNames];
}
export function validateProviderConfig(config) {
    return !!(config.name &&
        config.baseURL &&
        config.envKey &&
        typeof config.name === 'string' &&
        typeof config.baseURL === 'string' &&
        typeof config.envKey === 'string');
}
export function getProviderDisplayName(providerName, customProviders) {
    const config = getProviderConfig(providerName, customProviders);
    return config?.name || providerName;
}
export function providerSupportsFeature(providerName, feature) {
    const featureMatrix = {
        openai: { images: true, tools: true, streaming: true },
        azure: { images: true, tools: true, streaming: true },
        gemini: { images: true, tools: true, streaming: true },
        ollama: { images: false, tools: true, streaming: true },
        mistral: { images: false, tools: true, streaming: true },
        deepseek: { images: false, tools: true, streaming: true },
        xai: { images: false, tools: true, streaming: true },
        groq: { images: false, tools: true, streaming: true },
        arceeai: { images: false, tools: true, streaming: true },
        openrouter: { images: true, tools: true, streaming: true }
    };
    const providerFeatures = featureMatrix[providerName.toLowerCase()];
    return providerFeatures?.[feature] || false;
}
export function getDefaultModel(providerName) {
    const defaultModels = {
        openai: 'gpt-4',
        azure: 'gpt-4',
        gemini: 'gemini-pro',
        ollama: 'llama2',
        mistral: 'mistral-large-latest',
        deepseek: 'deepseek-chat',
        xai: 'grok-beta',
        groq: 'llama3-8b-8192',
        arceeai: 'arcee-nova',
        openrouter: 'openai/gpt-4'
    };
    return defaultModels[providerName.toLowerCase()] || 'gpt-4';
}
