import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { getHistory, searchHistory, getHistoryStats } from '../../utils/storage/command-history.js';
export function HistoryBrowser({ onSelect, onCancel }) {
    const [history, setHistory] = useState([]);
    const [filteredHistory, setFilteredHistory] = useState([]);
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [searchQuery, setSearchQuery] = useState('');
    const [mode, setMode] = useState('browse');
    const [stats, setStats] = useState(null);
    useEffect(() => {
        const loadHistory = () => {
            const historyData = getHistory();
            const historyStats = getHistoryStats();
            const reversedHistory = [...historyData].reverse();
            setHistory(reversedHistory);
            setFilteredHistory(reversedHistory);
            setStats(historyStats);
        };
        loadHistory();
    }, []);
    useEffect(() => {
        if (searchQuery.trim()) {
            const results = searchHistory(searchQuery);
            setFilteredHistory([...results].reverse());
            setSelectedIndex(0);
        }
        else {
            setFilteredHistory(history);
            setSelectedIndex(0);
        }
    }, [searchQuery, history]);
    useInput((input, key) => {
        if (key.escape) {
            if (mode === 'search' && searchQuery) {
                setSearchQuery('');
                setMode('browse');
            }
            else {
                onCancel();
            }
            return;
        }
        if (key.return) {
            const selectedEntry = filteredHistory[selectedIndex];
            if (selectedEntry) {
                onSelect(selectedEntry.command);
            }
            return;
        }
        if (key.upArrow) {
            setSelectedIndex(prev => Math.max(0, prev - 1));
            return;
        }
        if (key.downArrow) {
            setSelectedIndex(prev => Math.min(filteredHistory.length - 1, prev + 1));
            return;
        }
        if (key.ctrl && input === 'f') {
            setMode('search');
            return;
        }
        if (key.ctrl && input === 'c') {
            onCancel();
            return;
        }
        if (key.backspace || key.delete) {
            if (mode === 'search') {
                setSearchQuery(prev => prev.slice(0, -1));
            }
            return;
        }
        if (mode === 'search' && input && !key.ctrl && !key.meta) {
            setSearchQuery(prev => prev + input);
        }
    });
    const formatTimestamp = (timestamp) => {
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffMins = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        if (diffMins < 1)
            return 'just now';
        if (diffMins < 60)
            return `${diffMins}m ago`;
        if (diffHours < 24)
            return `${diffHours}h ago`;
        if (diffDays < 7)
            return `${diffDays}d ago`;
        return date.toLocaleDateString();
    };
    const getStatusIcon = (entry) => {
        if (entry.success === true)
            return '✅';
        if (entry.success === false)
            return '❌';
        return '❓';
    };
    const getStatusColor = (entry) => {
        if (entry.success === true)
            return 'green';
        if (entry.success === false)
            return 'red';
        return 'gray';
    };
    return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsx(Box, { borderStyle: "single", paddingX: 1, children: _jsx(Text, { bold: true, color: "blue", children: "Command History Browser" }) }), stats && (_jsx(Box, { children: _jsxs(Text, { children: ["Total: ", _jsx(Text, { color: "cyan", children: stats.totalCommands }), " \u2022 Successful: ", _jsx(Text, { color: "green", children: stats.successfulCommands }), " \u2022 Failed: ", _jsx(Text, { color: "red", children: stats.failedCommands }), " \u2022 Unique: ", _jsx(Text, { color: "yellow", children: stats.uniqueCommands })] }) })), _jsx(Box, { borderStyle: "single", paddingX: 1, children: _jsxs(Text, { children: [mode === 'search' ? '🔍 ' : '📝 ', "Search: ", _jsx(Text, { color: mode === 'search' ? 'cyan' : 'gray', children: searchQuery || (mode === 'search' ? '█' : 'Press Ctrl+F to search') })] }) }), _jsx(Box, { flexDirection: "column", height: 15, children: filteredHistory.length === 0 ? (_jsx(Box, { justifyContent: "center", alignItems: "center", height: "100%", children: _jsx(Text, { color: "gray", children: searchQuery ? 'No commands found matching your search' : 'No command history available' }) })) : (filteredHistory.slice(0, 15).map((entry, index) => {
                    const isSelected = index === selectedIndex;
                    const statusIcon = getStatusIcon(entry);
                    const statusColor = getStatusColor(entry);
                    const timestamp = formatTimestamp(entry.timestamp);
                    return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Box, { children: _jsxs(Text, { color: isSelected ? 'cyan' : undefined, children: [isSelected ? '▶ ' : '  ', _jsx(Text, { color: statusColor, children: statusIcon }), ' ', _jsx(Text, { bold: isSelected, children: entry.command })] }) }), isSelected && (_jsx(Box, { children: _jsxs(Text, { color: "gray", children: ["Executed: ", timestamp, entry.success !== undefined && (_jsxs(Text, { color: statusColor, children: [' • ', entry.success ? 'Success' : 'Failed'] }))] }) }))] }, `${entry.timestamp}-${index}`));
                })) }), filteredHistory.length > 15 && (_jsx(Box, { justifyContent: "center", children: _jsxs(Text, { color: "gray", children: ["... and ", filteredHistory.length - 15, " more commands"] }) })), filteredHistory[selectedIndex] && (_jsx(Box, { borderStyle: "single", paddingX: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { bold: true, color: "cyan", children: "Selected Command:" }), _jsx(Text, { color: "yellow", children: filteredHistory[selectedIndex].command })] }) })), _jsx(Box, { borderStyle: "single", paddingX: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: "gray", children: "\u2191/\u2193 Navigate \u2022 Enter Execute \u2022 Ctrl+F Search \u2022 Esc Cancel" }), mode === 'search' && (_jsx(Text, { color: "yellow", children: "Type to search commands \u2022 Esc to clear search" })), filteredHistory[selectedIndex] && (_jsxs(Text, { color: "cyan", children: ["Press Enter to execute: ", filteredHistory[selectedIndex].command] }))] }) })] }));
}
