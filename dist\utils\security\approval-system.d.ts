export interface SecurityAction {
    id: string;
    type: 'file_write' | 'file_delete' | 'command_execute' | 'network_request' | 'system_modify';
    description: string;
    target: string;
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    metadata: Record<string, any>;
    timestamp: string;
}
export interface ApprovalRequest {
    id: string;
    action: SecurityAction;
    requiredApprovals: number;
    approvals: Approval[];
    status: 'pending' | 'approved' | 'rejected' | 'expired';
    expiresAt: string;
    createdAt: string;
}
export interface Approval {
    userId: string;
    decision: 'approve' | 'reject';
    reason?: string;
    timestamp: string;
}
export interface SecurityPolicy {
    autoApprove: {
        lowRisk: boolean;
        mediumRisk: boolean;
        highRisk: boolean;
        criticalRisk: boolean;
    };
    requiredApprovals: {
        low: number;
        medium: number;
        high: number;
        critical: number;
    };
    approvalTimeout: number;
    auditLogging: boolean;
    restrictedPaths: string[];
    allowedCommands: string[];
    blockedCommands: string[];
}
export declare class ApprovalManager {
    private pendingRequests;
    private auditLog;
    private policy;
    constructor(policy?: Partial<SecurityPolicy>);
    requestApproval(action: SecurityAction): Promise<ApprovalRequest>;
    provideApproval(requestId: string, userId: string, decision: 'approve' | 'reject', reason?: string): Promise<boolean>;
    assessRisk(action: Omit<SecurityAction, 'id' | 'riskLevel' | 'timestamp'>): 'low' | 'medium' | 'high' | 'critical';
    createSecurityAction(type: SecurityAction['type'], description: string, target: string, metadata?: Record<string, any>): SecurityAction;
    private isRestrictedPath;
    private shouldAutoApprove;
    private generateRequestId;
    private generateActionId;
    private saveAuditLog;
    getPendingRequests(): ApprovalRequest[];
    getAuditLog(): SecurityAction[];
    updatePolicy(updates: Partial<SecurityPolicy>): void;
    getPolicy(): SecurityPolicy;
    cleanupExpiredRequests(): void;
}
export declare const approvalManager: ApprovalManager;
export declare function requestFileApproval(operation: 'read' | 'write' | 'delete', filePath: string, description?: string): Promise<ApprovalRequest>;
export declare function requestCommandApproval(command: string, args?: string[], description?: string): Promise<ApprovalRequest>;
//# sourceMappingURL=approval-system.d.ts.map