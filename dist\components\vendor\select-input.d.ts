export interface SelectOption {
    label: string;
    value: string;
    description?: string;
    disabled?: boolean;
}
export interface SelectInputProps {
    options: SelectOption[];
    value?: string | string[];
    onChange: (value: string | string[]) => void;
    onSubmit?: (value: string | string[]) => void;
    multiple?: boolean;
    placeholder?: string;
    disabled?: boolean;
    focus?: boolean;
    maxHeight?: number;
    showIndicators?: boolean;
    indicatorSelected?: string;
    indicatorUnselected?: string;
    indicatorFocus?: string;
}
export declare function SelectInput({ options, value, onChange, onSubmit, multiple, placeholder, disabled, focus, maxHeight, showIndicators, indicatorSelected, indicatorUnselected, indicatorFocus, }: SelectInputProps): import("react/jsx-runtime").JSX.Element;
export declare function createOption(label: string, value?: string, description?: string, disabled?: boolean): SelectOption;
export declare function createOptionsFromStrings(items: string[]): SelectOption[];
//# sourceMappingURL=select-input.d.ts.map