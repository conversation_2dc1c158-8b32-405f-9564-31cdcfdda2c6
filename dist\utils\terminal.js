let inkRenderer = null;
let originalTerminalState = null;
let debugMode = false;
export function setInkRenderer(renderer) {
    inkRenderer = renderer;
    if (process.stdin.isTTY) {
        originalTerminalState = {
            isRaw: process.stdin.isRaw,
            setRawMode: process.stdin.setRawMode,
        };
    }
}
export function getInkRenderer() {
    return inkRenderer;
}
export function clearTerminal() {
    if (process.stdout.isTTY) {
        process.stdout.write('\x1b[2J\x1b[H');
        process.stdout.write('\x1b[3J');
    }
}
export function clearScreen() {
    if (process.stdout.isTTY) {
        process.stdout.write('\x1b[2J\x1b[H');
    }
}
export function moveCursor(row, col) {
    if (process.stdout.isTTY) {
        process.stdout.write(`\x1b[${row};${col}H`);
    }
}
export function hideCursor() {
    if (process.stdout.isTTY) {
        process.stdout.write('\x1b[?25l');
    }
}
export function showCursor() {
    if (process.stdout.isTTY) {
        process.stdout.write('\x1b[?25h');
    }
}
export function enableAlternateScreen() {
    if (process.stdout.isTTY) {
        process.stdout.write('\x1b[?1049h');
    }
}
export function disableAlternateScreen() {
    if (process.stdout.isTTY) {
        process.stdout.write('\x1b[?1049l');
    }
}
export function setTerminalTitle(title) {
    if (process.stdout.isTTY) {
        process.stdout.write(`\x1b]0;${title}\x07`);
    }
}
export function restoreTerminalTitle() {
    if (process.stdout.isTTY) {
        process.stdout.write('\x1b]0;\x07');
    }
}
export function enableDebugMode() {
    debugMode = true;
    if (typeof process !== 'undefined' && process.env) {
        process.env.DEBUG_FPS = '1';
    }
}
export function disableDebugMode() {
    debugMode = false;
    if (typeof process !== 'undefined' && process.env) {
        delete process.env.DEBUG_FPS;
    }
}
export function isDebugMode() {
    return debugMode || process.env.DEBUG_FPS === '1';
}
export function debugLog(message, data) {
    if (isDebugMode()) {
        const timestamp = new Date().toISOString();
        console.error(`[DEBUG ${timestamp}] ${message}`, data || '');
    }
}
export function measureFPS() {
    if (!isDebugMode()) {
        return () => { };
    }
    let frameCount = 0;
    let lastTime = Date.now();
    const interval = setInterval(() => {
        const currentTime = Date.now();
        const deltaTime = currentTime - lastTime;
        const fps = Math.round((frameCount * 1000) / deltaTime);
        debugLog(`FPS: ${fps}, Frames: ${frameCount}, Delta: ${deltaTime}ms`);
        frameCount = 0;
        lastTime = currentTime;
    }, 1000);
    return () => {
        frameCount++;
        return () => {
            clearInterval(interval);
        };
    };
}
export function onExit() {
    try {
        showCursor();
        restoreTerminalTitle();
        disableAlternateScreen();
        if (originalTerminalState && process.stdin.isTTY) {
            if (originalTerminalState.setRawMode) {
                process.stdin.setRawMode(originalTerminalState.isRaw);
            }
        }
        if (process.stdout.isTTY) {
            process.stdout.write('\n');
        }
    }
    catch (error) {
    }
}
export function setupTerminal() {
    setTerminalTitle('Kritrima AI CLI');
    if (process.stdin.isTTY && process.stdin.setRawMode) {
        process.stdin.setRawMode(true);
    }
    process.on('exit', onExit);
    process.on('SIGINT', () => {
        onExit();
        process.exit(0);
    });
    process.on('SIGTERM', () => {
        onExit();
        process.exit(0);
    });
    process.on('uncaughtException', (error) => {
        onExit();
        console.error('Uncaught Exception:', error);
        process.exit(1);
    });
}
export function getTerminalCapabilities() {
    return {
        isTTY: process.stdout.isTTY || false,
        hasColors: process.stdout.hasColors ? process.stdout.hasColors() : false,
        columns: process.stdout.columns || 80,
        rows: process.stdout.rows || 24,
        supportsUnicode: process.env.LANG?.includes('UTF-8') || process.platform !== 'win32',
        platform: process.platform,
    };
}
