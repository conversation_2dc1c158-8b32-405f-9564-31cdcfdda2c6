export interface PatchOperation {
    type: 'create' | 'edit' | 'delete' | 'move';
    path: string;
    content?: string;
    newPath?: string;
    backup?: string;
}
export interface PatchResult {
    success: boolean;
    operations: PatchOperation[];
    errors: string[];
    backups: string[];
}
export interface DiffHunk {
    oldStart: number;
    oldCount: number;
    newStart: number;
    newCount: number;
    lines: string[];
}
export interface ParsedDiff {
    oldFile: string;
    newFile: string;
    hunks: DiffHunk[];
    isNew: boolean;
    isDeleted: boolean;
}
export declare function processPatch(patch: string, workingDirectory?: string, createBackups?: boolean): PatchResult;
export declare function parsePatch(patch: string): ParsedDiff[];
export declare function restoreFromBackup(backupPath: string): boolean;
export declare function cleanupBackups(directory: string, maxAge?: number): number;
//# sourceMappingURL=apply-patch.d.ts.map