export interface ContextFile {
    path: string;
    relativePath: string;
    size: number;
    type: 'source' | 'config' | 'documentation' | 'data' | 'other';
    language?: string;
    importance: number;
    content?: string;
    summary?: string;
    dependencies?: string[];
    exports?: string[];
    imports?: string[];
}
export interface ProjectContext {
    rootPath: string;
    files: ContextFile[];
    structure: ProjectStructure;
    dependencies: DependencyMap;
    gitInfo: any;
    metadata: {
        totalFiles: number;
        totalSize: number;
        languages: string[];
        frameworks: string[];
        buildTools: string[];
    };
}
export interface ProjectStructure {
    directories: string[];
    filesByType: Record<string, string[]>;
    configFiles: string[];
    entryPoints: string[];
}
export interface DependencyMap {
    internal: Record<string, string[]>;
    external: Record<string, string[]>;
    circular: string[][];
}
export declare class FullContextAnalyzer {
    private maxFileSize;
    private maxTotalSize;
    private excludePatterns;
    analyzeProject(rootPath?: string): Promise<ProjectContext>;
    private discoverFiles;
    private analyzeFiles;
    private determineFileType;
    private detectLanguage;
    private calculateImportance;
    private generateSummary;
    private analyzeSourceCode;
    private buildProjectStructure;
    private mapDependencies;
    private calculateMetadata;
}
export declare const fullContextAnalyzer: FullContextAnalyzer;
export declare function getProjectContext(rootPath?: string, forceRefresh?: boolean): Promise<ProjectContext>;
//# sourceMappingURL=full-context.d.ts.map