import type { ExecInput, ExecResult, AppConfig, ApprovalPolicy } from '../../types/index.js';
export declare function handleExecCommand(input: ExecInput, config: AppConfig, approvalPolicy: ApprovalPolicy, additionalWritableRoots?: string[], signal?: AbortSignal): Promise<ExecResult>;
export declare function validateCommandSafety(command: string[], workdir: string, config: AppConfig): {
    safe: boolean;
    warnings: string[];
    blockers: string[];
};
export declare function formatCommandOutput(result: ExecResult): string;
export declare function estimateExecutionTime(command: string[]): number;
export declare function commandModifiesFiles(command: string[]): boolean;
export declare function getCommandDescription(command: string[]): string;
//# sourceMappingURL=handle-exec-command.d.ts.map