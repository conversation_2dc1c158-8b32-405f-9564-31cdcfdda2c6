import type { ExecInput, ExecResult, AppConfig } from '../../../types/index.js';
export declare function execWithSeatbelt(input: ExecInput, config: AppConfig): Promise<ExecResult>;
export declare function testSeatbelt(): Promise<{
    available: boolean;
    capabilities: string[];
    limitations: string[];
}>;
export declare function getSeatbeltEnvironment(): {
    platform: string;
    sandboxing: boolean;
    restrictions: string[];
};
//# sourceMappingURL=macos-seatbelt.d.ts.map