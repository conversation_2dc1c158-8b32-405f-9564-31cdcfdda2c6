import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
let CLI_VERSION;
try {
    const packageJsonPath = join(__dirname, '..', 'package.json');
    const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
    CLI_VERSION = packageJson.version;
}
catch (error) {
    console.warn('Warning: Could not read version from package.json, using fallback');
    CLI_VERSION = '1.0.0';
}
export { CLI_VERSION };
export function getVersion() {
    return CLI_VERSION;
}
export function getVersionInfo() {
    return {
        version: CLI_VERSION,
        node: process.version,
        platform: process.platform,
        arch: process.arch
    };
}
