export default class TextBuffer {
    lines = [""];
    cursorRow = 0;
    cursorCol = 0;
    scrollRow = 0;
    scrollCol = 0;
    version = 0;
    undoStack = [];
    redoStack = [];
    maxUndoSteps = 100;
    constructor(initialText = "") {
        if (initialText) {
            this.lines = initialText.split('\n');
            if (this.lines.length === 0) {
                this.lines = [""];
            }
        }
        this.saveUndoState();
    }
    getText() {
        return this.lines.join('\n');
    }
    setText(text) {
        this.saveUndoState();
        this.lines = text.split('\n');
        if (this.lines.length === 0) {
            this.lines = [""];
        }
        this.cursorRow = 0;
        this.cursorCol = 0;
        this.scrollRow = 0;
        this.scrollCol = 0;
        this.version++;
    }
    insertText(text) {
        this.saveUndoState();
        const lines = text.split('\n');
        const currentLine = this.lines[this.cursorRow];
        if (lines.length === 1) {
            const newLine = currentLine.slice(0, this.cursorCol) + text + currentLine.slice(this.cursorCol);
            this.lines[this.cursorRow] = newLine;
            this.cursorCol += text.length;
        }
        else {
            const beforeCursor = currentLine.slice(0, this.cursorCol);
            const afterCursor = currentLine.slice(this.cursorCol);
            this.lines[this.cursorRow] = beforeCursor + lines[0];
            for (let i = 1; i < lines.length - 1; i++) {
                this.lines.splice(this.cursorRow + i, 0, lines[i]);
            }
            const lastLineIndex = this.cursorRow + lines.length - 1;
            this.lines.splice(lastLineIndex, 0, lines[lines.length - 1] + afterCursor);
            this.cursorRow = lastLineIndex;
            this.cursorCol = lines[lines.length - 1].length;
        }
        this.version++;
    }
    deleteChar() {
        if (this.cursorCol === 0 && this.cursorRow === 0) {
            return;
        }
        this.saveUndoState();
        if (this.cursorCol > 0) {
            const line = this.lines[this.cursorRow];
            this.lines[this.cursorRow] = line.slice(0, this.cursorCol - 1) + line.slice(this.cursorCol);
            this.cursorCol--;
        }
        else {
            if (this.cursorRow > 0) {
                const currentLine = this.lines[this.cursorRow];
                const prevLine = this.lines[this.cursorRow - 1];
                this.lines[this.cursorRow - 1] = prevLine + currentLine;
                this.lines.splice(this.cursorRow, 1);
                this.cursorRow--;
                this.cursorCol = prevLine.length;
            }
        }
        this.version++;
    }
    deleteCharForward() {
        const line = this.lines[this.cursorRow];
        if (this.cursorCol < line.length) {
            this.saveUndoState();
            this.lines[this.cursorRow] = line.slice(0, this.cursorCol) + line.slice(this.cursorCol + 1);
            this.version++;
        }
        else if (this.cursorRow < this.lines.length - 1) {
            this.saveUndoState();
            const nextLine = this.lines[this.cursorRow + 1];
            this.lines[this.cursorRow] = line + nextLine;
            this.lines.splice(this.cursorRow + 1, 1);
            this.version++;
        }
    }
    setCursor(row, col) {
        this.cursorRow = Math.max(0, Math.min(row, this.lines.length - 1));
        const line = this.lines[this.cursorRow];
        this.cursorCol = Math.max(0, Math.min(col, line.length));
    }
    getCursor() {
        return { row: this.cursorRow, col: this.cursorCol };
    }
    moveCursorLeft() {
        if (this.cursorCol > 0) {
            this.cursorCol--;
        }
        else if (this.cursorRow > 0) {
            this.cursorRow--;
            this.cursorCol = this.lines[this.cursorRow].length;
        }
    }
    moveCursorRight() {
        const line = this.lines[this.cursorRow];
        if (this.cursorCol < line.length) {
            this.cursorCol++;
        }
        else if (this.cursorRow < this.lines.length - 1) {
            this.cursorRow++;
            this.cursorCol = 0;
        }
    }
    moveCursorUp() {
        if (this.cursorRow > 0) {
            this.cursorRow--;
            const line = this.lines[this.cursorRow];
            this.cursorCol = Math.min(this.cursorCol, line.length);
        }
    }
    moveCursorDown() {
        if (this.cursorRow < this.lines.length - 1) {
            this.cursorRow++;
            const line = this.lines[this.cursorRow];
            this.cursorCol = Math.min(this.cursorCol, line.length);
        }
    }
    moveCursorToLineStart() {
        this.cursorCol = 0;
    }
    moveCursorToLineEnd() {
        this.cursorCol = this.lines[this.cursorRow].length;
    }
    moveCursorToStart() {
        this.cursorRow = 0;
        this.cursorCol = 0;
    }
    moveCursorToEnd() {
        this.cursorRow = this.lines.length - 1;
        this.cursorCol = this.lines[this.cursorRow].length;
    }
    moveCursorToNextWord() {
        const line = this.lines[this.cursorRow];
        let col = this.cursorCol;
        while (col < line.length && /\w/.test(line[col])) {
            col++;
        }
        while (col < line.length && /\s/.test(line[col])) {
            col++;
        }
        if (col >= line.length && this.cursorRow < this.lines.length - 1) {
            this.cursorRow++;
            this.cursorCol = 0;
        }
        else {
            this.cursorCol = col;
        }
    }
    moveCursorToPrevWord() {
        let col = this.cursorCol;
        const line = this.lines[this.cursorRow];
        if (col === 0 && this.cursorRow > 0) {
            this.cursorRow--;
            this.cursorCol = this.lines[this.cursorRow].length;
            return;
        }
        col = Math.max(0, col - 1);
        while (col > 0 && /\s/.test(line[col])) {
            col--;
        }
        while (col > 0 && /\w/.test(line[col - 1])) {
            col--;
        }
        this.cursorCol = col;
    }
    deleteWordBackward() {
        this.saveUndoState();
        const startCol = this.cursorCol;
        this.moveCursorToPrevWord();
        const endCol = this.cursorCol;
        if (this.cursorRow === this.cursorRow) {
            const line = this.lines[this.cursorRow];
            this.lines[this.cursorRow] = line.slice(0, endCol) + line.slice(startCol);
        }
        this.version++;
    }
    insertNewLine() {
        this.saveUndoState();
        const line = this.lines[this.cursorRow];
        const beforeCursor = line.slice(0, this.cursorCol);
        const afterCursor = line.slice(this.cursorCol);
        this.lines[this.cursorRow] = beforeCursor;
        this.lines.splice(this.cursorRow + 1, 0, afterCursor);
        this.cursorRow++;
        this.cursorCol = 0;
        this.version++;
    }
    deleteLine() {
        if (this.lines.length === 1) {
            this.lines[0] = "";
            this.cursorCol = 0;
        }
        else {
            this.saveUndoState();
            this.lines.splice(this.cursorRow, 1);
            if (this.cursorRow >= this.lines.length) {
                this.cursorRow = this.lines.length - 1;
            }
            this.cursorCol = Math.min(this.cursorCol, this.lines[this.cursorRow].length);
            this.version++;
        }
    }
    deleteToLineEnd() {
        this.saveUndoState();
        const line = this.lines[this.cursorRow];
        this.lines[this.cursorRow] = line.slice(0, this.cursorCol);
        this.version++;
    }
    saveUndoState() {
        const state = {
            lines: [...this.lines],
            cursorRow: this.cursorRow,
            cursorCol: this.cursorCol,
            version: this.version
        };
        this.undoStack.push(state);
        if (this.undoStack.length > this.maxUndoSteps) {
            this.undoStack.shift();
        }
        this.redoStack = [];
    }
    undo() {
        if (this.undoStack.length === 0) {
            return false;
        }
        const currentState = {
            lines: [...this.lines],
            cursorRow: this.cursorRow,
            cursorCol: this.cursorCol,
            version: this.version
        };
        this.redoStack.push(currentState);
        const prevState = this.undoStack.pop();
        this.lines = [...prevState.lines];
        this.cursorRow = prevState.cursorRow;
        this.cursorCol = prevState.cursorCol;
        this.version = prevState.version;
        return true;
    }
    redo() {
        if (this.redoStack.length === 0) {
            return false;
        }
        this.saveUndoState();
        const nextState = this.redoStack.pop();
        this.lines = [...nextState.lines];
        this.cursorRow = nextState.cursorRow;
        this.cursorCol = nextState.cursorCol;
        this.version = nextState.version;
        return true;
    }
    getViewport() {
        return {
            scrollRow: this.scrollRow,
            scrollCol: this.scrollCol,
            visibleRows: 0,
            visibleCols: 0
        };
    }
    ensureCursorVisible(viewportRows, viewportCols) {
        if (this.cursorRow < this.scrollRow) {
            this.scrollRow = this.cursorRow;
        }
        else if (this.cursorRow >= this.scrollRow + viewportRows) {
            this.scrollRow = this.cursorRow - viewportRows + 1;
        }
        if (this.cursorCol < this.scrollCol) {
            this.scrollCol = this.cursorCol;
        }
        else if (this.cursorCol >= this.scrollCol + viewportCols) {
            this.scrollCol = this.cursorCol - viewportCols + 1;
        }
        this.scrollRow = Math.max(0, this.scrollRow);
        this.scrollCol = Math.max(0, this.scrollCol);
    }
    getLineCount() {
        return this.lines.length;
    }
    getLine(index) {
        return this.lines[index] || "";
    }
    getLines() {
        return [...this.lines];
    }
    getVersion() {
        return this.version;
    }
    isEmpty() {
        return this.lines.length === 1 && this.lines[0] === "";
    }
    getCharCount() {
        return this.getText().length;
    }
    getCursorPosition() {
        return this.getCursor();
    }
    backspace() {
        this.deleteChar();
    }
    delete() {
        this.deleteCharForward();
    }
    clear() {
        this.saveUndoState();
        this.lines = [''];
        this.cursorRow = 0;
        this.cursorCol = 0;
        this.scrollRow = 0;
        this.scrollCol = 0;
        this.version++;
    }
    selectAll() {
        this.moveCursorToStart();
    }
    getSelectedText() {
        return '';
    }
}
