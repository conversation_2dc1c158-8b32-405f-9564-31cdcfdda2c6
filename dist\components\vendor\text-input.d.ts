export interface TextInputProps {
    value: string;
    onChange: (value: string) => void;
    onSubmit?: (value: string) => void;
    placeholder?: string;
    multiline?: boolean;
    maxLines?: number;
    showCursor?: boolean;
    cursorChar?: string;
    disabled?: boolean;
    focus?: boolean;
    onFocus?: () => void;
    onBlur?: () => void;
    onKeyPress?: (ch: string, key: any) => void;
}
export declare function TextInput({ value, onChange, onSubmit, placeholder, multiline, maxLines, showCursor, cursorChar, disabled, focus, onFocus: _onFocus, onBlur: _onBlur, onKeyPress, }: TextInputProps): import("react/jsx-runtime").JSX.Element;
//# sourceMappingURL=text-input.d.ts.map