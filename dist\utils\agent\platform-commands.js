const COMMAND_MAP = {
    'ls': 'dir',
    'cat': 'type',
    'rm': 'del',
    'mv': 'move',
    'cp': 'copy',
    'mkdir': 'md',
    'rmdir': 'rd',
    'touch': 'echo.',
    'pwd': 'cd',
    'which': 'where',
    'grep': 'findstr',
    'head': 'more',
    'tail': 'more',
    'wc': 'find /c /v ""',
    'ps': 'tasklist',
    'kill': 'taskkill',
    'killall': 'taskkill',
    'curl': 'curl',
    'wget': 'curl',
    'tar': 'tar',
    'unzip': 'tar',
    'uname': 'ver',
    'whoami': 'whoami',
    'id': 'whoami',
    'date': 'date',
    'uptime': 'systeminfo'
};
const ARGUMENT_MAP = {
    'dir': (args) => {
        const dirArgs = [];
        for (const arg of args) {
            switch (arg) {
                case '-l':
                case '-la':
                case '-al':
                    dirArgs.push('/Q');
                    break;
                case '-a':
                    dirArgs.push('/A');
                    break;
                case '-h':
                    break;
                case '-R':
                case '-r':
                    dirArgs.push('/S');
                    break;
                case '-t':
                    dirArgs.push('/O:D');
                    break;
                case '-S':
                    dirArgs.push('/O:S');
                    break;
                default:
                    if (!arg.startsWith('-')) {
                        dirArgs.push(arg);
                    }
            }
        }
        return dirArgs;
    },
    'type': (args) => {
        return args.filter(arg => !arg.startsWith('-'));
    },
    'del': (args) => {
        const delArgs = [];
        for (const arg of args) {
            switch (arg) {
                case '-r':
                case '-rf':
                case '-R':
                    delArgs.push('/S');
                    delArgs.push('/Q');
                    break;
                case '-f':
                    delArgs.push('/Q');
                    break;
                case '-i':
                    delArgs.push('/P');
                    break;
                default:
                    if (!arg.startsWith('-')) {
                        delArgs.push(arg);
                    }
            }
        }
        return delArgs;
    },
    'findstr': (args) => {
        const findstrArgs = [];
        let pattern = '';
        let files = [];
        let inFiles = false;
        for (let i = 0; i < args.length; i++) {
            const arg = args[i];
            switch (arg) {
                case '-i':
                    findstrArgs.push('/I');
                    break;
                case '-r':
                case '-E':
                    findstrArgs.push('/R');
                    break;
                case '-n':
                    findstrArgs.push('/N');
                    break;
                case '-v':
                    findstrArgs.push('/V');
                    break;
                case '-l':
                    findstrArgs.push('/M');
                    break;
                case '-c':
                    findstrArgs.push('/C');
                    break;
                default:
                    if (!arg.startsWith('-')) {
                        if (!pattern) {
                            pattern = arg;
                        }
                        else {
                            files.push(arg);
                        }
                    }
            }
        }
        if (pattern) {
            findstrArgs.push(pattern);
        }
        if (files.length > 0) {
            findstrArgs.push(...files);
        }
        return findstrArgs;
    },
    'tasklist': (args) => {
        const tasklistArgs = [];
        for (const arg of args) {
            switch (arg) {
                case '-e':
                case '-A':
                    break;
                case '-f':
                    tasklistArgs.push('/FO', 'LIST');
                    break;
                case '-u':
                    tasklistArgs.push('/FO', 'CSV');
                    break;
                default:
                    if (!arg.startsWith('-')) {
                        tasklistArgs.push(arg);
                    }
            }
        }
        return tasklistArgs;
    },
    'taskkill': (args) => {
        const taskkillArgs = [];
        for (let i = 0; i < args.length; i++) {
            const arg = args[i];
            switch (arg) {
                case '-9':
                    taskkillArgs.push('/F');
                    break;
                case '-TERM':
                case '-15':
                    break;
                default:
                    if (!arg.startsWith('-')) {
                        taskkillArgs.push('/PID', arg);
                    }
            }
        }
        return taskkillArgs;
    }
};
export function adaptCommandForPlatform(command) {
    if (process.platform !== 'win32') {
        return [...command];
    }
    const [cmd, ...args] = command;
    const lowerCmd = cmd.toLowerCase();
    const mappedCmd = COMMAND_MAP[lowerCmd];
    if (!mappedCmd) {
        return [...command];
    }
    const argMapper = ARGUMENT_MAP[mappedCmd];
    const mappedArgs = argMapper ? argMapper(args) : args;
    return [mappedCmd, ...mappedArgs];
}
export function isCommandAvailable(command) {
    const lowerCmd = command.toLowerCase();
    if (process.platform === 'win32') {
        const windowsCommands = [
            'dir', 'type', 'del', 'move', 'copy', 'md', 'rd', 'cd', 'where',
            'findstr', 'more', 'tasklist', 'taskkill', 'whoami', 'date', 'ver',
            'systeminfo', 'echo', 'set', 'cls', 'help'
        ];
        if (windowsCommands.includes(lowerCmd)) {
            return true;
        }
        if (COMMAND_MAP[lowerCmd]) {
            return true;
        }
        const modernWindowsCommands = ['curl', 'tar', 'ssh', 'git'];
        if (modernWindowsCommands.includes(lowerCmd)) {
            return true;
        }
    }
    else {
        const commonUnixCommands = [
            'ls', 'cat', 'rm', 'mv', 'cp', 'mkdir', 'rmdir', 'touch', 'pwd', 'which',
            'grep', 'head', 'tail', 'wc', 'ps', 'kill', 'killall', 'curl', 'wget',
            'tar', 'unzip', 'uname', 'whoami', 'id', 'date', 'uptime'
        ];
        if (commonUnixCommands.includes(lowerCmd)) {
            return true;
        }
    }
    return true;
}
export function getCommandSuggestions(command) {
    const lowerCmd = command.toLowerCase();
    const suggestions = [];
    if (process.platform === 'win32') {
        const windowsSuggestions = {
            'ls': ['dir', 'Get-ChildItem'],
            'cat': ['type', 'Get-Content'],
            'rm': ['del', 'Remove-Item'],
            'mv': ['move', 'Move-Item'],
            'cp': ['copy', 'Copy-Item'],
            'grep': ['findstr', 'Select-String'],
            'ps': ['tasklist', 'Get-Process'],
            'kill': ['taskkill', 'Stop-Process'],
            'which': ['where', 'Get-Command'],
            'curl': ['curl', 'Invoke-WebRequest'],
            'wget': ['curl', 'Invoke-WebRequest']
        };
        if (windowsSuggestions[lowerCmd]) {
            suggestions.push(...windowsSuggestions[lowerCmd]);
        }
    }
    else {
        const unixSuggestions = {
            'dir': ['ls'],
            'type': ['cat'],
            'del': ['rm'],
            'move': ['mv'],
            'copy': ['cp'],
            'findstr': ['grep'],
            'tasklist': ['ps'],
            'taskkill': ['kill'],
            'where': ['which']
        };
        if (unixSuggestions[lowerCmd]) {
            suggestions.push(...unixSuggestions[lowerCmd]);
        }
    }
    return suggestions;
}
export function getCommandHelp(command) {
    const lowerCmd = command.toLowerCase();
    const helpTexts = {
        'ls': 'List directory contents. Use -l for detailed view, -a for hidden files.',
        'dir': 'List directory contents. Use /Q for ownership, /A for hidden files.',
        'cat': 'Display file contents. Usage: cat <filename>',
        'type': 'Display file contents. Usage: type <filename>',
        'rm': 'Remove files/directories. Use -r for recursive, -f for force.',
        'del': 'Delete files. Use /S for recursive, /Q for quiet.',
        'grep': 'Search text patterns. Usage: grep <pattern> <files>',
        'findstr': 'Search text patterns. Usage: findstr <pattern> <files>',
        'ps': 'List running processes. Use -e for all processes.',
        'tasklist': 'List running processes. Use /FO LIST for detailed view.',
        'kill': 'Terminate processes. Usage: kill <PID>',
        'taskkill': 'Terminate processes. Usage: taskkill /PID <PID>'
    };
    return helpTexts[lowerCmd] || `No help available for command: ${command}`;
}
export function normalizePath(path) {
    if (process.platform === 'win32') {
        return path.replace(/\//g, '\\');
    }
    else {
        return path.replace(/\\/g, '/');
    }
}
export function getShellCommand() {
    if (process.platform === 'win32') {
        return ['cmd', '/c'];
    }
    else {
        return ['sh', '-c'];
    }
}
export function escapeArgument(arg) {
    if (process.platform === 'win32') {
        if (arg.includes(' ') || arg.includes('"')) {
            return `"${arg.replace(/"/g, '""')}"`;
        }
        return arg;
    }
    else {
        if (arg.includes(' ') || arg.includes("'") || arg.includes('"')) {
            return `'${arg.replace(/'/g, "'\"'\"'")}'`;
        }
        return arg;
    }
}
