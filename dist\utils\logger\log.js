import { writeFileSync, existsSync, mkdirSync, symlinkSync, unlinkSync } from 'fs';
import { join, dirname } from 'path';
import { tmpdir, homedir } from 'os';
class AsyncLogger {
    queue = [];
    isWriting = false;
    logPath;
    enabled;
    constructor(logPath, enabled = false) {
        this.logPath = logPath;
        this.enabled = enabled;
        if (this.enabled) {
            this.ensureLogDirectory();
            this.createSymlink();
        }
    }
    ensureLogDirectory() {
        const logDir = dirname(this.logPath);
        if (!existsSync(logDir)) {
            mkdirSync(logDir, { recursive: true });
        }
    }
    createSymlink() {
        try {
            const logDir = dirname(this.logPath);
            const symlinkPath = join(logDir, 'kritrima-ai-latest.log');
            if (existsSync(symlinkPath)) {
                unlinkSync(symlinkPath);
            }
            symlinkSync(this.logPath, symlinkPath);
        }
        catch (error) {
        }
    }
    now() {
        return new Date().toISOString();
    }
    formatMessage(level, message, error) {
        let formatted = `[${this.now()}] [${level.toUpperCase()}] ${message}`;
        if (error) {
            formatted += `\nError: ${error.message}`;
            if (error.stack) {
                formatted += `\nStack: ${error.stack}`;
            }
        }
        return formatted + '\n';
    }
    async maybeWrite() {
        if (!this.enabled || this.isWriting || this.queue.length === 0) {
            return;
        }
        this.isWriting = true;
        const entries = this.queue.splice(0);
        try {
            const content = entries.join('');
            writeFileSync(this.logPath, content, { flag: 'a' });
        }
        catch (error) {
            this.queue.unshift(...entries);
        }
        finally {
            this.isWriting = false;
            if (this.queue.length > 0) {
                setImmediate(() => this.maybeWrite());
            }
        }
    }
    log(message, details) {
        if (!this.enabled)
            return;
        let fullMessage = message;
        if (details) {
            fullMessage += ` - ${JSON.stringify(details)}`;
        }
        const entry = this.formatMessage('info', fullMessage);
        this.queue.push(entry);
        this.maybeWrite();
    }
    error(message, error) {
        if (!this.enabled)
            return;
        const entry = this.formatMessage('error', message, error);
        this.queue.push(entry);
        this.maybeWrite();
    }
    warn(message) {
        if (!this.enabled)
            return;
        const entry = this.formatMessage('warn', message);
        this.queue.push(entry);
        this.maybeWrite();
    }
    info(message, details) {
        if (!this.enabled)
            return;
        let fullMessage = message;
        if (details) {
            fullMessage += ` - ${JSON.stringify(details)}`;
        }
        const entry = this.formatMessage('info', fullMessage);
        this.queue.push(entry);
        this.maybeWrite();
    }
    debug(message) {
        if (!this.enabled)
            return;
        const entry = this.formatMessage('debug', message);
        this.queue.push(entry);
        this.maybeWrite();
    }
}
let globalLogger = null;
function initializeLogger() {
    if (globalLogger) {
        return globalLogger;
    }
    const enabled = isLoggingEnabled();
    let logPath;
    if (process.platform === 'darwin' || process.platform === 'win32') {
        const logDir = join(tmpdir(), 'kritrima-ai');
        logPath = join(logDir, `kritrima-ai-cli-${Date.now()}.log`);
    }
    else {
        const logDir = join(homedir(), '.local', 'kritrima-ai');
        logPath = join(logDir, `kritrima-ai-cli-${Date.now()}.log`);
    }
    globalLogger = new AsyncLogger(logPath, enabled);
    return globalLogger;
}
export function isLoggingEnabled() {
    return process.env.DEBUG === '1' ||
        process.env.KRITRIMA_AI_DEBUG === 'true' ||
        process.env.KRITRIMA_AI_ENABLE_LOGGING === 'true';
}
export function getLogger() {
    return initializeLogger();
}
export function log(message) {
    getLogger().log(message);
}
export function logError(message, error) {
    getLogger().error(message, error);
}
export function logWarn(message) {
    getLogger().warn(message);
}
export function logInfo(message, metadata) {
    const fullMessage = metadata ? `${message} ${JSON.stringify(metadata)}` : message;
    getLogger().info(fullMessage);
}
export function logDebug(message) {
    getLogger().debug(message);
}
export class PerformanceMonitor {
    frameCount = 0;
    lastTime = Date.now();
    fpsHistory = [];
    logFrame() {
        if (!isLoggingEnabled())
            return;
        this.frameCount++;
        const now = Date.now();
        const elapsed = now - this.lastTime;
        if (elapsed >= 1000) {
            const fps = Math.round((this.frameCount * 1000) / elapsed);
            this.fpsHistory.push(fps);
            if (this.fpsHistory.length > 10) {
                this.fpsHistory.shift();
            }
            logDebug(`UI FPS: ${fps} (avg: ${Math.round(this.fpsHistory.reduce((a, b) => a + b, 0) / this.fpsHistory.length)})`);
            this.frameCount = 0;
            this.lastTime = now;
        }
    }
    getAverageFPS() {
        if (this.fpsHistory.length === 0)
            return 0;
        return Math.round(this.fpsHistory.reduce((a, b) => a + b, 0) / this.fpsHistory.length);
    }
}
export const performanceMonitor = new PerformanceMonitor();
export function logAgentExecution(action, details, duration) {
    if (!isLoggingEnabled())
        return;
    let message = `Agent: ${action}`;
    if (duration !== undefined) {
        message += ` (${duration}ms)`;
    }
    if (details) {
        message += ` - ${JSON.stringify(details)}`;
    }
    logDebug(message);
}
export function logNetworkRequest(method, url, status, duration) {
    if (!isLoggingEnabled())
        return;
    let message = `Network: ${method} ${url}`;
    if (status !== undefined) {
        message += ` -> ${status}`;
    }
    if (duration !== undefined) {
        message += ` (${duration}ms)`;
    }
    logDebug(message);
}
export function getRecentLogs(count = 50) {
    const logger = getLogger();
    if (typeof logger.getRecentLogs === 'function') {
        return logger.getRecentLogs(count);
    }
    return [];
}
