import { OpenAI } from 'openai';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { getApiKey, getBaseUrl, loadConfig } from './config.js';
import { getProviderConfig } from './providers.js';
export function createOpenAIClient(options = {}) {
    const config = loadConfig();
    const provider = options.provider || config.provider;
    const apiKey = options.apiKey || getApiKey(provider);
    if (!apiKey) {
        throw new Error(`No API key found for provider: ${provider}. Please set the appropriate environment variable.`);
    }
    const baseURL = options.baseURL || getBaseUrl(provider);
    const proxyUrl = process.env.HTTPS_PROXY || process.env.https_proxy;
    const httpAgent = proxyUrl ? new HttpsProxyAgent(proxyUrl) : undefined;
    const clientConfig = {
        apiKey,
        baseURL,
        timeout: options.timeout || config.timeout || 30000,
        httpAgent,
    };
    if (provider === 'openai') {
        if (options.organization || process.env.OPENAI_ORG_ID) {
            clientConfig.organization = options.organization || process.env.OPENAI_ORG_ID;
        }
        if (options.project || process.env.OPENAI_PROJECT_ID) {
            clientConfig.project = options.project || process.env.OPENAI_PROJECT_ID;
        }
    }
    if (provider === 'azure') {
        const apiVersion = process.env.AZURE_OPENAI_API_VERSION || '2024-02-15-preview';
        clientConfig.defaultQuery = { 'api-version': apiVersion };
        clientConfig.defaultHeaders = {
            'api-key': apiKey,
        };
    }
    return new OpenAI(clientConfig);
}
export async function testClientConnection(provider = 'openai') {
    try {
        const client = createOpenAIClient({ provider });
        await client.models.list();
        return { success: true };
    }
    catch (error) {
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
}
export async function createClientWithRetry(options = {}, maxRetries = 3) {
    let lastError = null;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const client = createOpenAIClient(options);
            await client.models.list();
            return client;
        }
        catch (error) {
            lastError = error instanceof Error ? error : new Error('Unknown error');
            if (attempt < maxRetries) {
                const delay = Math.pow(2, attempt - 1) * 1000;
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }
    throw lastError || new Error('Failed to create client after retries');
}
export function createMultiProviderClients(providers) {
    const clients = {};
    for (const provider of providers) {
        try {
            clients[provider] = createOpenAIClient({ provider });
        }
        catch (error) {
            console.warn(`Warning: Could not create client for provider ${provider}:`, error);
        }
    }
    return clients;
}
export function getClientConfig(provider = 'openai') {
    const config = loadConfig();
    const baseURL = getBaseUrl(provider);
    const apiKey = getApiKey(provider);
    return {
        provider,
        baseURL,
        hasApiKey: !!apiKey,
        timeout: config.timeout || 30000
    };
}
export function validateClientConfig(provider = 'openai') {
    const errors = [];
    const providerConfig = getProviderConfig(provider);
    if (!providerConfig) {
        errors.push(`Unsupported provider: ${provider}`);
    }
    const apiKey = getApiKey(provider);
    if (!apiKey) {
        errors.push(`Missing API key for provider: ${provider}`);
    }
    const baseURL = getBaseUrl(provider);
    if (!baseURL) {
        errors.push(`Missing base URL for provider: ${provider}`);
    }
    return {
        valid: errors.length === 0,
        errors
    };
}
