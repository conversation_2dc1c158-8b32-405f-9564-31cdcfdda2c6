export interface UndoState {
    lines: string[];
    cursorRow: number;
    cursorCol: number;
    version: number;
}
export interface ViewportInfo {
    scrollRow: number;
    scrollCol: number;
    visibleRows: number;
    visibleCols: number;
}
export interface CursorPosition {
    row: number;
    col: number;
}
export default class TextBuffer {
    private lines;
    private cursorRow;
    private cursorCol;
    private scrollRow;
    private scrollCol;
    private version;
    private undoStack;
    private redoStack;
    private maxUndoSteps;
    constructor(initialText?: string);
    getText(): string;
    setText(text: string): void;
    insertText(text: string): void;
    deleteChar(): void;
    deleteCharForward(): void;
    setCursor(row: number, col: number): void;
    getCursor(): CursorPosition;
    moveCursorLeft(): void;
    moveCursorRight(): void;
    moveCursorUp(): void;
    moveCursorDown(): void;
    moveCursorToLineStart(): void;
    moveCursorToLineEnd(): void;
    moveCursorToStart(): void;
    moveCursorToEnd(): void;
    moveCursorToNextWord(): void;
    moveCursorToPrevWord(): void;
    deleteWordBackward(): void;
    insertNewLine(): void;
    deleteLine(): void;
    deleteToLineEnd(): void;
    private saveUndoState;
    undo(): boolean;
    redo(): boolean;
    getViewport(): ViewportInfo;
    ensureCursorVisible(viewportRows: number, viewportCols: number): void;
    getLineCount(): number;
    getLine(index: number): string;
    getLines(): string[];
    getVersion(): number;
    isEmpty(): boolean;
    getCharCount(): number;
    getCursorPosition(): CursorPosition;
    backspace(): void;
    delete(): void;
    clear(): void;
    selectAll(): void;
    getSelectedText(): string;
}
//# sourceMappingURL=text-buffer.d.ts.map