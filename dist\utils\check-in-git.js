import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { join } from 'path';
export function checkInGit(workdir = process.cwd()) {
    try {
        execSync('git rev-parse --is-inside-work-tree', {
            cwd: workdir,
            stdio: 'ignore',
            timeout: 5000
        });
        return true;
    }
    catch (_error) {
        return checkGitDirectory(workdir);
    }
}
function checkGitDirectory(startDir) {
    let currentDir = startDir;
    while (currentDir !== '/' && currentDir !== '' && currentDir.length > 1) {
        const gitPath = join(currentDir, '.git');
        if (existsSync(gitPath)) {
            return true;
        }
        const parentDir = join(currentDir, '..');
        if (parentDir === currentDir) {
            break;
        }
        currentDir = parentDir;
    }
    return false;
}
export function getGitRoot(workdir = process.cwd()) {
    try {
        const result = execSync('git rev-parse --show-toplevel', {
            cwd: workdir,
            encoding: 'utf-8',
            timeout: 5000
        });
        return result.trim();
    }
    catch (_error) {
        return null;
    }
}
export function getCurrentBranch(workdir = process.cwd()) {
    try {
        const result = execSync('git rev-parse --abbrev-ref HEAD', {
            cwd: workdir,
            encoding: 'utf-8',
            timeout: 5000
        });
        return result.trim();
    }
    catch (_error) {
        return null;
    }
}
export function hasUncommittedChanges(workdir = process.cwd()) {
    try {
        const result = execSync('git status --porcelain', {
            cwd: workdir,
            encoding: 'utf-8',
            timeout: 5000
        });
        return result.trim().length > 0;
    }
    catch (_error) {
        return false;
    }
}
export function getGitStatus(workdir = process.cwd()) {
    const inGit = checkInGit(workdir);
    if (!inGit) {
        return { inGit: false };
    }
    const root = getGitRoot(workdir);
    const branch = getCurrentBranch(workdir);
    const hasChanges = hasUncommittedChanges(workdir);
    return {
        inGit: true,
        root: root || undefined,
        branch: branch || undefined,
        hasChanges,
        isClean: !hasChanges
    };
}
export function isFileTracked(filePath, workdir = process.cwd()) {
    try {
        execSync(`git ls-files --error-unmatch "${filePath}"`, {
            cwd: workdir,
            stdio: 'ignore',
            timeout: 5000
        });
        return true;
    }
    catch (_error) {
        return false;
    }
}
export function getModifiedFiles(workdir = process.cwd()) {
    try {
        const result = execSync('git diff --name-only', {
            cwd: workdir,
            encoding: 'utf-8',
            timeout: 5000
        });
        return result
            .trim()
            .split('\n')
            .filter(line => line.length > 0);
    }
    catch (_error) {
        return [];
    }
}
export function getStagedFiles(workdir = process.cwd()) {
    try {
        const result = execSync('git diff --cached --name-only', {
            cwd: workdir,
            encoding: 'utf-8',
            timeout: 5000
        });
        return result
            .trim()
            .split('\n')
            .filter(line => line.length > 0);
    }
    catch (_error) {
        return [];
    }
}
export function getUntrackedFiles(workdir = process.cwd()) {
    try {
        const result = execSync('git ls-files --others --exclude-standard', {
            cwd: workdir,
            encoding: 'utf-8',
            timeout: 5000
        });
        return result
            .trim()
            .split('\n')
            .filter(line => line.length > 0);
    }
    catch (_error) {
        return [];
    }
}
export function getFileStatus(workdir = process.cwd()) {
    const modified = getModifiedFiles(workdir);
    const staged = getStagedFiles(workdir);
    const untracked = getUntrackedFiles(workdir);
    return {
        modified,
        staged,
        untracked,
        total: modified.length + staged.length + untracked.length
    };
}
