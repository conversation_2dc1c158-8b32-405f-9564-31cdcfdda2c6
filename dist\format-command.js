export function formatCommandForDisplay(command) {
    if (!command || command.length === 0) {
        return '';
    }
    try {
        const unwrapped = unwrapBashCommand(command);
        const quoted = unwrapped.map(arg => shellQuote(arg));
        return quoted.join(' ');
    }
    catch (_error) {
        return command.join(' ');
    }
}
function unwrapBashCommand(command) {
    if (command.length >= 3 &&
        command[0] === 'bash' &&
        command[1] === '-lc') {
        const wrappedCommand = command[2];
        try {
            return parseShellCommand(wrappedCommand);
        }
        catch {
            return [wrappedCommand];
        }
    }
    if (command.length >= 3 &&
        (command[0] === 'sh' || command[0] === 'zsh') &&
        command[1] === '-c') {
        const wrappedCommand = command[2];
        try {
            return parseShellCommand(wrappedCommand);
        }
        catch {
            return [wrappedCommand];
        }
    }
    return command;
}
function parseShellCommand(commandString) {
    const args = [];
    let current = '';
    let inQuotes = false;
    let quoteChar = '';
    let escaped = false;
    for (let i = 0; i < commandString.length; i++) {
        const char = commandString[i];
        if (escaped) {
            current += char;
            escaped = false;
            continue;
        }
        if (char === '\\') {
            escaped = true;
            continue;
        }
        if (!inQuotes && (char === '"' || char === "'")) {
            inQuotes = true;
            quoteChar = char;
            continue;
        }
        if (inQuotes && char === quoteChar) {
            inQuotes = false;
            quoteChar = '';
            continue;
        }
        if (!inQuotes && char === ' ') {
            if (current) {
                args.push(current);
                current = '';
            }
            continue;
        }
        current += char;
    }
    if (current) {
        args.push(current);
    }
    return args;
}
function shellQuote(arg) {
    if (isSimpleArg(arg)) {
        return arg;
    }
    if (isAlreadyQuoted(arg)) {
        return arg;
    }
    if (arg.includes("'")) {
        return `"${arg.replace(/"/g, '\\"')}"`;
    }
    else {
        return `'${arg}'`;
    }
}
function isSimpleArg(arg) {
    return /^[a-zA-Z0-9._/-]+$/.test(arg);
}
function isAlreadyQuoted(arg) {
    const len = arg.length;
    if (len < 2)
        return false;
    const first = arg[0];
    const last = arg[len - 1];
    if ((first === '"' && last === '"') || (first === "'" && last === "'")) {
        return true;
    }
    return false;
}
export function formatCommandForLogging(command) {
    return JSON.stringify(command);
}
export function formatCommandWithContext(command, workdir) {
    const formattedCommand = formatCommandForDisplay(command);
    if (workdir && workdir !== process.cwd()) {
        return `cd ${shellQuote(workdir)} && ${formattedCommand}`;
    }
    return formattedCommand;
}
export function truncateCommand(command, maxLength = 100) {
    if (command.length <= maxLength) {
        return command;
    }
    const truncated = command.substring(0, maxLength - 3);
    return truncated + '...';
}
export function formatCommandResult(command, exitCode, duration, workdir) {
    const formattedCommand = formatCommandWithContext(command, workdir);
    const status = exitCode === 0 ? 'SUCCESS' : 'FAILED';
    const durationMs = Math.round(duration);
    return `[${status}] ${formattedCommand} (${durationMs}ms)`;
}
export function getCommandName(command) {
    if (!command || command.length === 0) {
        return '';
    }
    const unwrapped = unwrapBashCommand(command);
    return unwrapped[0] || '';
}
export function isDangerousCommand(command) {
    const dangerousCommands = [
        'rm', 'rmdir', 'del', 'delete',
        'sudo', 'su',
        'chmod', 'chown',
        'mkfs', 'fdisk',
        'dd', 'shred',
        'kill', 'killall',
        'reboot', 'shutdown', 'halt',
        'format', 'diskpart'
    ];
    const commandName = getCommandName(command).toLowerCase();
    return dangerousCommands.includes(commandName);
}
export function getCommandCategory(command) {
    const commandName = getCommandName(command).toLowerCase();
    const categories = {
        'File Operations': ['ls', 'dir', 'cat', 'type', 'cp', 'copy', 'mv', 'move', 'mkdir', 'touch'],
        'Git Operations': ['git'],
        'Package Management': ['npm', 'yarn', 'pnpm', 'pip', 'cargo', 'go'],
        'Build Tools': ['make', 'cmake', 'ninja', 'msbuild'],
        'Text Processing': ['grep', 'findstr', 'sed', 'awk', 'sort', 'uniq'],
        'System Info': ['ps', 'top', 'htop', 'df', 'du', 'free', 'uname', 'whoami'],
        'Network': ['curl', 'wget', 'ping', 'netstat', 'ss'],
        'Dangerous': ['rm', 'del', 'sudo', 'chmod', 'kill']
    };
    for (const [category, commands] of Object.entries(categories)) {
        if (commands.includes(commandName)) {
            return category;
        }
    }
    return 'Other';
}
