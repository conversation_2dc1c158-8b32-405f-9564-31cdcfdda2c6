export interface Logger {
    log(message: string, details?: any): void;
    error(message: string, error?: Error): void;
    warn(message: string): void;
    info(message: string, details?: any): void;
    debug(message: string): void;
}
export declare function isLoggingEnabled(): boolean;
export declare function getLogger(): Logger;
export declare function log(message: string): void;
export declare function logError(message: string, error?: Error): void;
export declare function logWarn(message: string): void;
export declare function logInfo(message: string, metadata?: Record<string, any>): void;
export declare function logDebug(message: string): void;
export declare class PerformanceMonitor {
    private frameCount;
    private lastTime;
    private fpsHistory;
    logFrame(): void;
    getAverageFPS(): number;
}
export declare const performanceMonitor: PerformanceMonitor;
export declare function logAgentExecution(action: string, details: any, duration?: number): void;
export declare function logNetworkRequest(method: string, url: string, status?: number, duration?: number): void;
export declare function getRecentLogs(count?: number): string[];
//# sourceMappingURL=log.d.ts.map