/**
 * Full-Context Mode System
 * 
 * Provides comprehensive context gathering and analysis
 * Includes file analysis, dependency mapping, and intelligent context selection
 */

import { promises as fs } from 'fs';
import { dirname, extname, relative } from 'path';
import { glob } from 'glob';
import { getGitStatus } from './check-in-git.js';
import { logInfo, logError, logWarn } from './logger/log.js';

export interface ContextFile {
  path: string;
  relativePath: string;
  size: number;
  type: 'source' | 'config' | 'documentation' | 'data' | 'other';
  language?: string;
  importance: number;
  content?: string;
  summary?: string;
  dependencies?: string[];
  exports?: string[];
  imports?: string[];
}

export interface ProjectContext {
  rootPath: string;
  files: ContextFile[];
  structure: ProjectStructure;
  dependencies: DependencyMap;
  gitInfo: any;
  metadata: {
    totalFiles: number;
    totalSize: number;
    languages: string[];
    frameworks: string[];
    buildTools: string[];
  };
}

export interface ProjectStructure {
  directories: string[];
  filesByType: Record<string, string[]>;
  configFiles: string[];
  entryPoints: string[];
}

export interface DependencyMap {
  internal: Record<string, string[]>;
  external: Record<string, string[]>;
  circular: string[][];
}

/**
 * Full context analyzer
 */
export class FullContextAnalyzer {
  private maxFileSize = 1024 * 1024; // 1MB
  private maxTotalSize = 50 * 1024 * 1024; // 50MB
  private excludePatterns = [
    'node_modules/**',
    '.git/**',
    'dist/**',
    'build/**',
    '*.log',
    '*.tmp',
    '.DS_Store',
    'Thumbs.db'
  ];

  /**
   * Analyze project and gather full context
   */
  async analyzeProject(rootPath: string = process.cwd()): Promise<ProjectContext> {
    logInfo(`Starting full context analysis for ${rootPath}`);

    try {
      // Get git information
      const gitInfo = getGitStatus();

      // Discover files
      const files = await this.discoverFiles(rootPath);

      // Analyze file contents
      const analyzedFiles = await this.analyzeFiles(files, rootPath);

      // Build project structure
      const structure = this.buildProjectStructure(analyzedFiles);

      // Map dependencies
      const dependencies = await this.mapDependencies(analyzedFiles);

      // Calculate metadata
      const metadata = this.calculateMetadata(analyzedFiles);

      const context: ProjectContext = {
        rootPath,
        files: analyzedFiles,
        structure,
        dependencies,
        gitInfo,
        metadata
      };

      logInfo(`Context analysis complete: ${analyzedFiles.length} files, ${metadata.totalSize} bytes`);
      return context;

    } catch (error) {
      logError('Failed to analyze project context', error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * Discover files in project
   */
  private async discoverFiles(rootPath: string): Promise<string[]> {
    const patterns = [
      '**/*.{js,jsx,ts,tsx,py,java,cpp,c,h,cs,php,rb,go,rs,swift,kt}',
      '**/*.{json,yaml,yml,xml,toml,ini,cfg,conf}',
      '**/*.{md,txt,rst,adoc}',
      '**/package.json',
      '**/requirements.txt',
      '**/Cargo.toml',
      '**/go.mod',
      '**/pom.xml',
      '**/build.gradle',
      '**/Makefile',
      '**/Dockerfile',
      '**/.env*',
      '**/README*'
    ];

    const allFiles: string[] = [];

    for (const pattern of patterns) {
      try {
        const files = await glob(pattern, {
          cwd: rootPath,
          ignore: this.excludePatterns,
          absolute: true
        });
        allFiles.push(...files);
      } catch (error) {
        logWarn(`Failed to glob pattern ${pattern}: ${error}`);
      }
    }

    // Remove duplicates and sort
    return [...new Set(allFiles)].sort();
  }

  /**
   * Analyze individual files
   */
  private async analyzeFiles(filePaths: string[], rootPath: string): Promise<ContextFile[]> {
    const analyzedFiles: ContextFile[] = [];
    let totalSize = 0;

    for (const filePath of filePaths) {
      try {
        const stats = await fs.stat(filePath);
        
        // Skip files that are too large
        if (stats.size > this.maxFileSize) {
          logWarn(`Skipping large file: ${filePath} (${stats.size} bytes)`);
          continue;
        }

        // Check total size limit
        if (totalSize + stats.size > this.maxTotalSize) {
          logWarn(`Reached total size limit, skipping remaining files`);
          break;
        }

        const relativePath = relative(rootPath, filePath);
        const file: ContextFile = {
          path: filePath,
          relativePath,
          size: stats.size,
          type: this.determineFileType(filePath),
          language: this.detectLanguage(filePath),
          importance: this.calculateImportance(filePath, relativePath)
        };

        // Read and analyze content for important files
        if (file.importance > 0.3 || file.type === 'config') {
          try {
            const content = await fs.readFile(filePath, 'utf-8');
            file.content = content;
            file.summary = this.generateSummary(content, file.language);
            
            if (file.language) {
              const analysis = this.analyzeSourceCode(content, file.language);
              file.dependencies = analysis.dependencies;
              file.exports = analysis.exports;
              file.imports = analysis.imports;
            }
          } catch (error) {
            logWarn(`Failed to read file ${filePath}: ${error}`);
          }
        }

        analyzedFiles.push(file);
        totalSize += stats.size;

      } catch (error) {
        logWarn(`Failed to analyze file ${filePath}: ${error}`);
      }
    }

    return analyzedFiles.sort((a, b) => b.importance - a.importance);
  }

  /**
   * Determine file type
   */
  private determineFileType(filePath: string): ContextFile['type'] {
    const ext = extname(filePath).toLowerCase();
    const basename = filePath.split('/').pop()?.toLowerCase() || '';

    // Configuration files
    if (ext === '.json' || ext === '.yaml' || ext === '.yml' || ext === '.toml' || 
        ext === '.ini' || ext === '.cfg' || ext === '.conf' || basename.startsWith('.env')) {
      return 'config';
    }

    // Documentation
    if (ext === '.md' || ext === '.txt' || ext === '.rst' || ext === '.adoc' || 
        basename.startsWith('readme')) {
      return 'documentation';
    }

    // Source code
    if (['.js', '.jsx', '.ts', '.tsx', '.py', '.java', '.cpp', '.c', '.h', 
         '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt'].includes(ext)) {
      return 'source';
    }

    // Data files
    if (['.csv', '.xml', '.sql'].includes(ext)) {
      return 'data';
    }

    return 'other';
  }

  /**
   * Detect programming language
   */
  private detectLanguage(filePath: string): string | undefined {
    const ext = extname(filePath).toLowerCase();
    const languageMap: Record<string, string> = {
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.py': 'python',
      '.java': 'java',
      '.cpp': 'cpp',
      '.c': 'c',
      '.h': 'c',
      '.cs': 'csharp',
      '.php': 'php',
      '.rb': 'ruby',
      '.go': 'go',
      '.rs': 'rust',
      '.swift': 'swift',
      '.kt': 'kotlin'
    };

    return languageMap[ext];
  }

  /**
   * Calculate file importance
   */
  private calculateImportance(filePath: string, relativePath: string): number {
    let importance = 0.1; // Base importance

    const basename = filePath.split('/').pop()?.toLowerCase() || '';
    const pathParts = relativePath.split('/');

    // Entry points and main files
    if (['index.js', 'index.ts', 'main.py', 'app.py', 'main.java'].includes(basename)) {
      importance += 0.8;
    }

    // Configuration files
    if (['package.json', 'tsconfig.json', 'webpack.config.js', 'babel.config.js'].includes(basename)) {
      importance += 0.7;
    }

    // README and documentation
    if (basename.startsWith('readme')) {
      importance += 0.6;
    }

    // Shorter paths are generally more important
    importance += Math.max(0, 0.5 - pathParts.length * 0.1);

    // Source files in src directory
    if (pathParts.includes('src') || pathParts.includes('lib')) {
      importance += 0.3;
    }

    // Test files are less important
    if (pathParts.includes('test') || pathParts.includes('tests') || basename.includes('test')) {
      importance -= 0.3;
    }

    return Math.max(0, Math.min(1, importance));
  }

  /**
   * Generate file summary
   */
  private generateSummary(content: string, language?: string): string {
    const lines = content.split('\n');
    const maxLines = 5;
    
    // For source files, try to extract key information
    if (language) {
      const summary: string[] = [];
      
      // Look for class/function definitions
      for (const line of lines.slice(0, 50)) {
        const trimmed = line.trim();
        if (trimmed.startsWith('class ') || trimmed.startsWith('function ') || 
            trimmed.startsWith('def ') || trimmed.startsWith('export ')) {
          summary.push(trimmed);
          if (summary.length >= maxLines) break;
        }
      }
      
      if (summary.length > 0) {
        return summary.join('\n');
      }
    }

    // Default: first few non-empty lines
    const nonEmptyLines = lines.filter(line => line.trim().length > 0);
    return nonEmptyLines.slice(0, maxLines).join('\n');
  }

  /**
   * Analyze source code for dependencies and exports
   */
  private analyzeSourceCode(content: string, language: string): {
    dependencies: string[];
    exports: string[];
    imports: string[];
  } {
    const dependencies: string[] = [];
    const exports: string[] = [];
    const imports: string[] = [];

    const lines = content.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();

      // JavaScript/TypeScript imports
      if (language === 'javascript' || language === 'typescript') {
        const importMatch = trimmed.match(/import.*from\s+['"]([^'"]+)['"]/);
        if (importMatch) {
          imports.push(importMatch[1]);
          if (!importMatch[1].startsWith('.')) {
            dependencies.push(importMatch[1]);
          }
        }

        const requireMatch = trimmed.match(/require\(['"]([^'"]+)['"]\)/);
        if (requireMatch) {
          imports.push(requireMatch[1]);
          if (!requireMatch[1].startsWith('.')) {
            dependencies.push(requireMatch[1]);
          }
        }

        if (trimmed.startsWith('export ')) {
          const exportMatch = trimmed.match(/export\s+(?:class|function|const|let|var)\s+(\w+)/);
          if (exportMatch) {
            exports.push(exportMatch[1]);
          }
        }
      }

      // Python imports
      if (language === 'python') {
        const importMatch = trimmed.match(/^(?:from\s+(\S+)\s+)?import\s+(.+)/);
        if (importMatch) {
          const module = importMatch[1] || importMatch[2].split(',')[0].trim();
          imports.push(module);
          if (!module.startsWith('.')) {
            dependencies.push(module);
          }
        }

        if (trimmed.startsWith('def ') || trimmed.startsWith('class ')) {
          const defMatch = trimmed.match(/(?:def|class)\s+(\w+)/);
          if (defMatch) {
            exports.push(defMatch[1]);
          }
        }
      }
    }

    return {
      dependencies: [...new Set(dependencies)],
      exports: [...new Set(exports)],
      imports: [...new Set(imports)]
    };
  }

  /**
   * Build project structure
   */
  private buildProjectStructure(files: ContextFile[]): ProjectStructure {
    const directories = new Set<string>();
    const filesByType: Record<string, string[]> = {};
    const configFiles: string[] = [];
    const entryPoints: string[] = [];

    for (const file of files) {
      // Add directory
      const dir = dirname(file.relativePath);
      if (dir !== '.') {
        directories.add(dir);
      }

      // Group by type
      if (!filesByType[file.type]) {
        filesByType[file.type] = [];
      }
      filesByType[file.type].push(file.relativePath);

      // Identify config files
      if (file.type === 'config') {
        configFiles.push(file.relativePath);
      }

      // Identify entry points
      if (file.importance > 0.8) {
        entryPoints.push(file.relativePath);
      }
    }

    return {
      directories: Array.from(directories).sort(),
      filesByType,
      configFiles,
      entryPoints
    };
  }

  /**
   * Map dependencies between files
   */
  private async mapDependencies(files: ContextFile[]): Promise<DependencyMap> {
    const internal: Record<string, string[]> = {};
    const external: Record<string, string[]> = {};
    const circular: string[][] = [];

    // Build dependency graph
    for (const file of files) {
      if (file.imports) {
        internal[file.relativePath] = [];
        external[file.relativePath] = [];

        for (const imp of file.imports) {
          if (imp.startsWith('.')) {
            // Internal dependency
            internal[file.relativePath].push(imp);
          } else {
            // External dependency
            external[file.relativePath].push(imp);
          }
        }
      }
    }

    // Detect circular dependencies (simplified)
    // This is a basic implementation - could be enhanced with proper cycle detection
    for (const [file, deps] of Object.entries(internal)) {
      for (const dep of deps) {
        if (internal[dep]?.includes(file)) {
          circular.push([file, dep]);
        }
      }
    }

    return { internal, external, circular };
  }

  /**
   * Calculate project metadata
   */
  private calculateMetadata(files: ContextFile[]): ProjectContext['metadata'] {
    const languages = new Set<string>();
    const frameworks = new Set<string>();
    const buildTools = new Set<string>();
    let totalSize = 0;

    for (const file of files) {
      totalSize += file.size;

      if (file.language) {
        languages.add(file.language);
      }

      // Detect frameworks and build tools from dependencies
      if (file.dependencies) {
        for (const dep of file.dependencies) {
          if (['react', 'vue', 'angular', 'express', 'fastapi', 'django'].includes(dep)) {
            frameworks.add(dep);
          }
          if (['webpack', 'vite', 'rollup', 'parcel', 'gulp', 'grunt'].includes(dep)) {
            buildTools.add(dep);
          }
        }
      }
    }

    return {
      totalFiles: files.length,
      totalSize,
      languages: Array.from(languages),
      frameworks: Array.from(frameworks),
      buildTools: Array.from(buildTools)
    };
  }
}

// Global context analyzer instance
export const fullContextAnalyzer = new FullContextAnalyzer();

/**
 * Get project context with caching
 */
let cachedContext: ProjectContext | null = null;
let cacheTimestamp = 0;
const cacheTimeout = 5 * 60 * 1000; // 5 minutes

export async function getProjectContext(
  rootPath: string = process.cwd(),
  forceRefresh: boolean = false
): Promise<ProjectContext> {
  const now = Date.now();
  
  if (!forceRefresh && cachedContext && (now - cacheTimestamp) < cacheTimeout) {
    return cachedContext;
  }

  cachedContext = await fullContextAnalyzer.analyzeProject(rootPath);
  cacheTimestamp = now;
  
  return cachedContext;
}
