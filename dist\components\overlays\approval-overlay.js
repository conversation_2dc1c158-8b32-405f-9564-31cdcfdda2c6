import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
const APPROVAL_MODES = [
    {
        mode: 'suggest',
        title: 'Suggest Mode',
        description: 'Manual approval required for all actions',
        details: [
            '• All commands require explicit user approval',
            '• Maximum security and control',
            '• Best for sensitive environments',
            '• Slower workflow but safest option',
            '• Recommended for production systems'
        ],
        security: 'high',
        recommended: true
    },
    {
        mode: 'auto-edit',
        title: 'Auto-Edit Mode',
        description: 'Automatic file edits, manual command approval',
        details: [
            '• File operations are automatically approved',
            '• System commands require manual approval',
            '• Balanced security and productivity',
            '• Good for development environments',
            '• Safe commands are pre-approved'
        ],
        security: 'medium',
        recommended: false
    },
    {
        mode: 'full-auto',
        title: 'Full-Auto Mode',
        description: 'Automatic approval for all actions (with sandbox)',
        details: [
            '• All operations are automatically approved',
            '• Commands run in sandboxed environment',
            '• Maximum productivity, reduced security',
            '• Best for isolated development',
            '• Use with caution in production'
        ],
        security: 'low',
        recommended: false
    }
];
export function ApprovalOverlay({ currentMode, onModeChange, onClose, visible }) {
    const [selectedIndex, setSelectedIndex] = useState(0);
    useEffect(() => {
        const modeIndex = APPROVAL_MODES.findIndex(mode => mode.mode === currentMode);
        if (modeIndex >= 0) {
            setSelectedIndex(modeIndex);
        }
    }, [currentMode]);
    useInput((input, key) => {
        if (!visible)
            return;
        if (key.escape) {
            onClose();
            return;
        }
        if (key.upArrow) {
            setSelectedIndex(Math.max(0, selectedIndex - 1));
            return;
        }
        if (key.downArrow) {
            setSelectedIndex(Math.min(APPROVAL_MODES.length - 1, selectedIndex + 1));
            return;
        }
        if (key.return) {
            const selectedMode = APPROVAL_MODES[selectedIndex];
            onModeChange(selectedMode.mode);
            onClose();
            return;
        }
        if (input >= '1' && input <= '3') {
            const index = parseInt(input) - 1;
            if (index >= 0 && index < APPROVAL_MODES.length) {
                setSelectedIndex(index);
            }
            return;
        }
    });
    const getSecurityColor = (security) => {
        switch (security) {
            case 'high': return 'green';
            case 'medium': return 'yellow';
            case 'low': return 'red';
        }
    };
    if (!visible) {
        return null;
    }
    const selectedMode = APPROVAL_MODES[selectedIndex];
    return (_jsxs(Box, { borderStyle: "double", borderColor: "cyan", flexDirection: "column", width: "100%", height: "100%", children: [_jsxs(Box, { paddingX: 2, paddingY: 1, borderStyle: "single", borderColor: "gray", children: [_jsx(Text, { color: "cyan", bold: true, children: "Approval Mode Configuration" }), _jsx(Box, { children: _jsxs(Text, { color: "gray", children: ["Current: ", currentMode, " \u2022 Select security level for command execution"] }) })] }), _jsx(Box, { paddingX: 2, paddingY: 1, width: "50%", children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Box, { children: _jsx(Text, { color: "blue", bold: true, children: "Available Modes:" }) }), APPROVAL_MODES.map((mode, index) => {
                            const isSelected = index === selectedIndex;
                            const isCurrent = mode.mode === currentMode;
                            return (_jsxs(Box, { children: [_jsx(Box, { width: 3, children: _jsxs(Text, { color: isSelected ? "black" : "gray", backgroundColor: isSelected ? "cyan" : undefined, children: [index + 1, "."] }) }), _jsxs(Box, { flexDirection: "column", flexGrow: 1, children: [_jsx(Box, { children: _jsxs(Text, { color: isSelected ? "black" : "white", backgroundColor: isSelected ? "cyan" : undefined, bold: isSelected || isCurrent, children: [mode.title, isCurrent && ' (current)', mode.recommended && ' ⭐'] }) }), _jsx(Box, { children: _jsx(Text, { color: isSelected ? "black" : "gray", backgroundColor: isSelected ? "cyan" : undefined, children: mode.description }) }), _jsx(Box, { children: _jsxs(Text, { color: isSelected ? "black" : getSecurityColor(mode.security), backgroundColor: isSelected ? "cyan" : undefined, children: ["Security: ", mode.security.toUpperCase()] }) })] })] }, mode.mode));
                        })] }) }), _jsx(Box, { paddingX: 2, paddingY: 1, flexGrow: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsxs(Text, { color: "blue", bold: true, children: [selectedMode.title, " Details:"] }), _jsx(Text, { children: selectedMode.description }), _jsx(Text, { color: "blue", bold: true, children: "Features:" }), selectedMode.details.map((detail, index) => (_jsx(Text, { children: detail }, index))), _jsxs(Box, { children: [_jsx(Text, { color: "blue", bold: true, children: "Security Level: " }), _jsx(Text, { color: getSecurityColor(selectedMode.security), bold: true, children: selectedMode.security.toUpperCase() }), selectedMode.recommended && (_jsx(Text, { color: "yellow", children: "\u2B50 RECOMMENDED" }))] }), selectedMode.mode === 'suggest' && (_jsx(Box, { paddingX: 1, borderStyle: "single", borderColor: "green", children: _jsx(Text, { color: "green", children: "\uD83D\uDCA1 This is the safest option for production environments" }) })), selectedMode.mode === 'full-auto' && (_jsx(Box, { paddingX: 1, borderStyle: "single", borderColor: "red", children: _jsx(Text, { color: "red", children: "\u26A0\uFE0F  Use with caution - commands execute automatically" }) }))] }) }), _jsx(Box, { paddingX: 2, paddingY: 1, borderStyle: "single", borderColor: "gray", children: _jsx(Text, { color: "gray", children: "\u2191\u2193: Navigate \u2022 1-3: Quick select \u2022 Enter: Apply \u2022 Esc: Cancel" }) })] }));
}
