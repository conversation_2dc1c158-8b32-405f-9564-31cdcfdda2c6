/**
 * Update Checking System
 * 
 * Automatically checks for newer versions and displays update notifications
 * Includes frequency control and package manager detection
 */

import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { homedir } from 'os';
import semver from 'semver';
import chalk from 'chalk';
import { CLI_VERSION } from '../version.js';
import { detectInstallerByPath } from './package-manager-detector.js';

// Update check configuration
const UPDATE_CHECK_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours
const UPDATE_CHECK_FILE = join(homedir(), '.kritrima-ai', 'last-update-check');
const NPM_REGISTRY_URL = 'https://registry.npmjs.org/kritrima-ai-cli';

interface UpdateInfo {
  hasUpdate: boolean;
  currentVersion: string;
  latestVersion?: string;
  updateCommand?: string;
  releaseNotes?: string;
}

interface LastUpdateCheck {
  timestamp: number;
  lastCheckedVersion: string;
  latestVersion?: string;
}

/**
 * Check for updates with frequency control
 */
export async function checkForUpdates(): Promise<UpdateInfo | null> {
  try {
    // Check if we should perform update check
    if (!shouldCheckForUpdates()) {
      return null;
    }

    // Perform the actual update check
    const updateInfo = await performUpdateCheck();
    
    // Save update check timestamp
    saveUpdateCheckInfo(updateInfo);
    
    // Display update notification if available
    if (updateInfo.hasUpdate) {
      displayUpdateNotification(updateInfo);
    }
    
    return updateInfo;
    
  } catch (_error) {
    // Silently fail update checks - don't interrupt user experience
    return null;
  }
}

/**
 * Check if we should perform an update check
 */
function shouldCheckForUpdates(): boolean {
  try {
    if (!existsSync(UPDATE_CHECK_FILE)) {
      return true; // First time check
    }
    
    const content = readFileSync(UPDATE_CHECK_FILE, 'utf-8');
    const lastCheck: LastUpdateCheck = JSON.parse(content);
    
    // Check if enough time has passed
    const timeSinceLastCheck = Date.now() - lastCheck.timestamp;
    if (timeSinceLastCheck < UPDATE_CHECK_INTERVAL) {
      return false;
    }
    
    // Check if version has changed (user updated manually)
    if (lastCheck.lastCheckedVersion !== CLI_VERSION) {
      return true;
    }
    
    return true;
    
  } catch (_error) {
    return true; // Default to checking on error
  }
}

/**
 * Perform the actual update check against npm registry
 */
async function performUpdateCheck(): Promise<UpdateInfo> {
  try {
    // Fetch package info from npm registry
    const response = await fetch(NPM_REGISTRY_URL, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': `kritrima-ai-cli/${CLI_VERSION}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const packageInfo: any = await response.json();
    const latestVersion = packageInfo['dist-tags']?.latest;
    
    if (!latestVersion) {
      throw new Error('Could not determine latest version');
    }
    
    // Compare versions
    const hasUpdate = semver.gt(latestVersion, CLI_VERSION);
    
    // Get update command
    const updateCommand = await getUpdateCommand();
    
    // Get release notes (simplified)
    const releaseNotes = await getReleaseNotes(latestVersion, packageInfo);
    
    return {
      hasUpdate,
      currentVersion: CLI_VERSION,
      latestVersion: hasUpdate ? latestVersion : undefined,
      updateCommand: hasUpdate ? updateCommand : undefined,
      releaseNotes: hasUpdate ? releaseNotes : undefined
    };
    
  } catch (_error) {
    // Return no update on error
    return {
      hasUpdate: false,
      currentVersion: CLI_VERSION
    };
  }
}

/**
 * Get appropriate update command based on package manager
 */
async function getUpdateCommand(): Promise<string> {
  try {
    const installer = await detectInstallerByPath();
    
    switch (installer) {
      case 'npm':
        return 'npm update -g kritrima-ai-cli';
      case 'pnpm':
        return 'pnpm update -g kritrima-ai-cli';
      case 'yarn':
        return 'yarn global upgrade kritrima-ai-cli';
      case 'bun':
        return 'bun update -g kritrima-ai-cli';
      default:
        return 'npm update -g kritrima-ai-cli';
    }
  } catch (_error) {
    return 'npm update -g kritrima-ai-cli';
  }
}

/**
 * Get release notes for version
 */
async function getReleaseNotes(version: string, packageInfo: any): Promise<string> {
  try {
    // Try to get changelog from package info
    const versionInfo = packageInfo.versions?.[version];
    if (versionInfo?.description) {
      return versionInfo.description;
    }
    
    // Fallback to generic message
    return `New version ${version} is available with improvements and bug fixes.`;
    
  } catch (_error) {
    return `New version ${version} is available.`;
  }
}

/**
 * Save update check information
 */
function saveUpdateCheckInfo(updateInfo: UpdateInfo): void {
  try {
    const updateCheckDir = join(homedir(), '.kritrima-ai');
    if (!existsSync(updateCheckDir)) {
      mkdirSync(updateCheckDir, { recursive: true });
    }
    
    const lastCheck: LastUpdateCheck = {
      timestamp: Date.now(),
      lastCheckedVersion: CLI_VERSION,
      latestVersion: updateInfo.latestVersion
    };
    
    writeFileSync(UPDATE_CHECK_FILE, JSON.stringify(lastCheck, null, 2));
    
  } catch (_error) {
    // Silently fail
  }
}

/**
 * Display update notification
 */
function displayUpdateNotification(updateInfo: UpdateInfo): void {
  if (!updateInfo.hasUpdate || !updateInfo.latestVersion) {
    return;
  }
  
  console.log();
  console.log(chalk.yellow('┌' + '─'.repeat(60) + '┐'));
  console.log(chalk.yellow('│') + chalk.bold.blue(' 🚀 Update Available!').padEnd(59) + chalk.yellow('│'));
  console.log(chalk.yellow('│') + ''.padEnd(60) + chalk.yellow('│'));
  console.log(chalk.yellow('│') + ` Current version: ${chalk.red(updateInfo.currentVersion)}`.padEnd(60) + chalk.yellow('│'));
  console.log(chalk.yellow('│') + ` Latest version:  ${chalk.green(updateInfo.latestVersion)}`.padEnd(60) + chalk.yellow('│'));
  console.log(chalk.yellow('│') + ''.padEnd(60) + chalk.yellow('│'));
  
  if (updateInfo.releaseNotes) {
    const notes = updateInfo.releaseNotes.length > 45 
      ? updateInfo.releaseNotes.substring(0, 42) + '...'
      : updateInfo.releaseNotes;
    console.log(chalk.yellow('│') + ` ${notes}`.padEnd(60) + chalk.yellow('│'));
    console.log(chalk.yellow('│') + ''.padEnd(60) + chalk.yellow('│'));
  }
  
  if (updateInfo.updateCommand) {
    console.log(chalk.yellow('│') + ` Run: ${chalk.cyan(updateInfo.updateCommand)}`.padEnd(60) + chalk.yellow('│'));
  }
  
  console.log(chalk.yellow('└' + '─'.repeat(60) + '┘'));
  console.log();
}

/**
 * Force check for updates (ignore frequency control)
 */
export async function forceCheckForUpdates(): Promise<UpdateInfo> {
  const updateInfo = await performUpdateCheck();
  saveUpdateCheckInfo(updateInfo);
  
  if (updateInfo.hasUpdate) {
    displayUpdateNotification(updateInfo);
  } else {
    console.log(chalk.green('✓ You are using the latest version!'));
  }
  
  return updateInfo;
}

/**
 * Get last update check information
 */
export function getLastUpdateCheck(): LastUpdateCheck | null {
  try {
    if (!existsSync(UPDATE_CHECK_FILE)) {
      return null;
    }
    
    const content = readFileSync(UPDATE_CHECK_FILE, 'utf-8');
    return JSON.parse(content);
    
  } catch (_error) {
    return null;
  }
}

/**
 * Clear update check cache
 */
export function clearUpdateCheckCache(): void {
  try {
    if (existsSync(UPDATE_CHECK_FILE)) {
      writeFileSync(UPDATE_CHECK_FILE, JSON.stringify({
        timestamp: 0,
        lastCheckedVersion: CLI_VERSION
      }, null, 2));
    }
  } catch (_error) {
    // Silently fail
  }
}

/**
 * Disable update checks
 */
export function disableUpdateChecks(): void {
  try {
    const updateCheckDir = join(homedir(), '.kritrima-ai');
    if (!existsSync(updateCheckDir)) {
      mkdirSync(updateCheckDir, { recursive: true });
    }
    
    // Set timestamp far in the future to disable checks
    const disabledCheck: LastUpdateCheck = {
      timestamp: Date.now() + (365 * 24 * 60 * 60 * 1000), // 1 year from now
      lastCheckedVersion: CLI_VERSION
    };
    
    writeFileSync(UPDATE_CHECK_FILE, JSON.stringify(disabledCheck, null, 2));
    
  } catch (_error) {
    // Silently fail
  }
}
