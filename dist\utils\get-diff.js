import { spawn } from 'child_process';
import { resolve } from 'path';
import { logInfo, logError } from './logger/log.js';
export async function getGitDiff(workingDirectory = process.cwd(), options = {}) {
    try {
        const resolvedDir = resolve(workingDirectory);
        const args = buildGitDiffArgs(options);
        logInfo('Generating git diff', {
            directory: resolvedDir,
            options,
            command: ['git', ...args]
        });
        const result = await executeGitCommand(['diff', ...args], resolvedDir);
        if (!result.success) {
            return {
                success: false,
                diff: '',
                files: [],
                error: result.error
            };
        }
        const parsedResult = parseDiffOutput(result.stdout, options);
        return {
            success: true,
            diff: result.stdout,
            files: parsedResult.files,
            stats: parsedResult.stats
        };
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logError('Failed to generate git diff', new Error(errorMessage));
        return {
            success: false,
            diff: '',
            files: [],
            error: errorMessage
        };
    }
}
export async function getGitStatus(workingDirectory = process.cwd()) {
    try {
        const resolvedDir = resolve(workingDirectory);
        const result = await executeGitCommand(['status', '--porcelain'], resolvedDir);
        if (!result.success) {
            return {
                success: false,
                status: '',
                files: [],
                error: result.error
            };
        }
        const files = parseGitStatus(result.stdout);
        return {
            success: true,
            status: result.stdout,
            files
        };
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return {
            success: false,
            status: '',
            files: [],
            error: errorMessage
        };
    }
}
export async function getGitBranch(workingDirectory = process.cwd()) {
    try {
        const resolvedDir = resolve(workingDirectory);
        const [branchResult, commitResult] = await Promise.all([
            executeGitCommand(['branch', '--show-current'], resolvedDir),
            executeGitCommand(['rev-parse', 'HEAD'], resolvedDir)
        ]);
        if (!branchResult.success || !commitResult.success) {
            return {
                success: false,
                branch: '',
                commit: '',
                error: branchResult.error || commitResult.error
            };
        }
        const upstreamResult = await executeGitCommand(['rev-parse', '--abbrev-ref', '@{upstream}'], resolvedDir);
        return {
            success: true,
            branch: branchResult.stdout.trim(),
            commit: commitResult.stdout.trim(),
            upstream: upstreamResult.success ? upstreamResult.stdout.trim() : undefined
        };
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return {
            success: false,
            branch: '',
            commit: '',
            error: errorMessage
        };
    }
}
export async function isGitRepository(workingDirectory = process.cwd()) {
    try {
        const resolvedDir = resolve(workingDirectory);
        const result = await executeGitCommand(['rev-parse', '--is-inside-work-tree'], resolvedDir);
        return result.success && result.stdout.trim() === 'true';
    }
    catch {
        return false;
    }
}
function buildGitDiffArgs(options) {
    const args = [];
    if (options.staged || options.cached) {
        args.push('--cached');
    }
    if (options.unified !== undefined) {
        args.push(`--unified=${options.unified}`);
    }
    if (options.nameOnly) {
        args.push('--name-only');
    }
    if (options.statOnly) {
        args.push('--stat');
    }
    if (options.colorWords) {
        args.push('--color-words');
    }
    if (options.ignoreWhitespace) {
        args.push('--ignore-all-space');
    }
    if (options.since) {
        args.push(`--since=${options.since}`);
    }
    if (options.until) {
        args.push(`--until=${options.until}`);
    }
    if (options.files && options.files.length > 0) {
        args.push('--');
        args.push(...options.files);
    }
    return args;
}
function executeGitCommand(args, cwd, timeout = 10000) {
    return new Promise((resolve) => {
        const child = spawn('git', args, {
            cwd,
            stdio: ['pipe', 'pipe', 'pipe'],
            timeout
        });
        let stdout = '';
        let stderr = '';
        child.stdout?.on('data', (data) => {
            stdout += data.toString();
        });
        child.stderr?.on('data', (data) => {
            stderr += data.toString();
        });
        child.on('close', (code) => {
            if (code === 0) {
                resolve({ success: true, stdout, stderr });
            }
            else {
                resolve({
                    success: false,
                    stdout,
                    stderr,
                    error: stderr || `Git command failed with code ${code}`
                });
            }
        });
        child.on('error', (error) => {
            resolve({
                success: false,
                stdout,
                stderr,
                error: error.message
            });
        });
    });
}
function parseDiffOutput(output, options) {
    const files = [];
    let stats;
    if (options.nameOnly) {
        files.push(...output.split('\n').filter(line => line.trim()));
    }
    else if (options.statOnly) {
        const lines = output.split('\n');
        const statLine = lines.find(line => line.includes('insertion') || line.includes('deletion'));
        if (statLine) {
            const insertions = (statLine.match(/(\d+) insertion/)?.[1]) || '0';
            const deletions = (statLine.match(/(\d+) deletion/)?.[1]) || '0';
            const filesChanged = lines.filter(line => line.includes('|')).length;
            stats = {
                insertions: parseInt(insertions, 10),
                deletions: parseInt(deletions, 10),
                filesChanged
            };
        }
    }
    else {
        const diffLines = output.split('\n');
        for (const line of diffLines) {
            if (line.startsWith('diff --git')) {
                const match = line.match(/diff --git a\/(.+) b\/(.+)/);
                if (match) {
                    files.push(match[1]);
                }
            }
        }
    }
    return { files, stats };
}
function parseGitStatus(output) {
    const files = [];
    const lines = output.split('\n').filter(line => line.trim());
    for (const line of lines) {
        if (line.length >= 3) {
            const stagedStatus = line[0];
            const modifiedStatus = line[1];
            const path = line.slice(3);
            files.push({
                path,
                status: `${stagedStatus}${modifiedStatus}`,
                staged: stagedStatus !== ' ' && stagedStatus !== '?',
                modified: modifiedStatus !== ' '
            });
        }
    }
    return files;
}
