/**
 * Terminal Management Utilities
 * 
 * Provides terminal state management, screen clearing, and renderer integration
 * Handles terminal setup, cleanup, and debugging features
 */

import type { Instance } from 'ink';

let inkRenderer: Instance | null = null;
let originalTerminalState: any = null;
let debugMode = false;

/**
 * Set the Ink renderer instance for terminal management
 */
export function setInkRenderer(renderer: Instance): void {
  inkRenderer = renderer;
  
  // Store original terminal state
  if (process.stdin.isTTY) {
    originalTerminalState = {
      isRaw: process.stdin.isRaw,
      setRawMode: process.stdin.setRawMode,
    };
  }
}

/**
 * Get the current Ink renderer instance
 */
export function getInkRenderer(): Instance | null {
  return inkRenderer;
}

/**
 * Clear the terminal screen and scrollback buffer
 */
export function clearTerminal(): void {
  if (process.stdout.isTTY) {
    // Clear screen and move cursor to top-left
    process.stdout.write('\x1b[2J\x1b[H');
    
    // Clear scrollback buffer (if supported)
    process.stdout.write('\x1b[3J');
  }
}

/**
 * Clear only the current screen (preserve scrollback)
 */
export function clearScreen(): void {
  if (process.stdout.isTTY) {
    process.stdout.write('\x1b[2J\x1b[H');
  }
}

/**
 * Move cursor to specific position
 */
export function moveCursor(row: number, col: number): void {
  if (process.stdout.isTTY) {
    process.stdout.write(`\x1b[${row};${col}H`);
  }
}

/**
 * Hide cursor
 */
export function hideCursor(): void {
  if (process.stdout.isTTY) {
    process.stdout.write('\x1b[?25l');
  }
}

/**
 * Show cursor
 */
export function showCursor(): void {
  if (process.stdout.isTTY) {
    process.stdout.write('\x1b[?25h');
  }
}

/**
 * Enable alternative screen buffer
 */
export function enableAlternateScreen(): void {
  if (process.stdout.isTTY) {
    process.stdout.write('\x1b[?1049h');
  }
}

/**
 * Disable alternative screen buffer
 */
export function disableAlternateScreen(): void {
  if (process.stdout.isTTY) {
    process.stdout.write('\x1b[?1049l');
  }
}

/**
 * Set terminal title
 */
export function setTerminalTitle(title: string): void {
  if (process.stdout.isTTY) {
    process.stdout.write(`\x1b]0;${title}\x07`);
  }
}

/**
 * Restore terminal title
 */
export function restoreTerminalTitle(): void {
  if (process.stdout.isTTY) {
    process.stdout.write('\x1b]0;\x07');
  }
}

/**
 * Enable debug mode for terminal operations
 */
export function enableDebugMode(): void {
  debugMode = true;
  
  if (typeof process !== 'undefined' && process.env) {
    process.env.DEBUG_FPS = '1';
  }
}

/**
 * Disable debug mode
 */
export function disableDebugMode(): void {
  debugMode = false;
  
  if (typeof process !== 'undefined' && process.env) {
    delete process.env.DEBUG_FPS;
  }
}

/**
 * Check if debug mode is enabled
 */
export function isDebugMode(): boolean {
  return debugMode || process.env.DEBUG_FPS === '1';
}

/**
 * Log debug information if debug mode is enabled
 */
export function debugLog(message: string, data?: any): void {
  if (isDebugMode()) {
    const timestamp = new Date().toISOString();
    console.error(`[DEBUG ${timestamp}] ${message}`, data || '');
  }
}

/**
 * Measure and log FPS for UI performance debugging
 */
export function measureFPS(): () => void {
  if (!isDebugMode()) {
    return () => {};
  }

  let frameCount = 0;
  let lastTime = Date.now();
  
  const interval = setInterval(() => {
    const currentTime = Date.now();
    const deltaTime = currentTime - lastTime;
    const fps = Math.round((frameCount * 1000) / deltaTime);
    
    debugLog(`FPS: ${fps}, Frames: ${frameCount}, Delta: ${deltaTime}ms`);
    
    frameCount = 0;
    lastTime = currentTime;
  }, 1000);

  return () => {
    frameCount++;
    
    // Cleanup function
    return () => {
      clearInterval(interval);
    };
  };
}

/**
 * Handle graceful terminal cleanup on exit
 */
export function onExit(): void {
  try {
    // Restore cursor
    showCursor();
    
    // Restore terminal title
    restoreTerminalTitle();
    
    // Disable alternate screen if it was enabled
    disableAlternateScreen();
    
    // Restore original terminal state
    if (originalTerminalState && process.stdin.isTTY) {
      if (originalTerminalState.setRawMode) {
        process.stdin.setRawMode(originalTerminalState.isRaw);
      }
    }
    
    // Clear any remaining output
    if (process.stdout.isTTY) {
      process.stdout.write('\n');
    }
    
  } catch (error) {
    // Ignore errors during cleanup
  }
}

/**
 * Set up terminal for application use
 */
export function setupTerminal(): void {
  // Set terminal title
  setTerminalTitle('Kritrima AI CLI');
  
  // Enable raw mode for better input handling
  if (process.stdin.isTTY && process.stdin.setRawMode) {
    process.stdin.setRawMode(true);
  }
  
  // Set up exit handlers
  process.on('exit', onExit);
  process.on('SIGINT', () => {
    onExit();
    process.exit(0);
  });
  process.on('SIGTERM', () => {
    onExit();
    process.exit(0);
  });
  process.on('uncaughtException', (error) => {
    onExit();
    console.error('Uncaught Exception:', error);
    process.exit(1);
  });
}

/**
 * Get terminal capabilities
 */
export function getTerminalCapabilities(): {
  isTTY: boolean;
  hasColors: boolean;
  columns: number;
  rows: number;
  supportsUnicode: boolean;
  platform: string;
} {
  return {
    isTTY: process.stdout.isTTY || false,
    hasColors: process.stdout.hasColors ? process.stdout.hasColors() : false,
    columns: process.stdout.columns || 80,
    rows: process.stdout.rows || 24,
    supportsUnicode: process.env.LANG?.includes('UTF-8') || process.platform !== 'win32',
    platform: process.platform,
  };
}
