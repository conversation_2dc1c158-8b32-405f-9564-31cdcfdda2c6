{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": false, "removeComments": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "jsx": "react-jsx", "allowImportingTsExtensions": false, "noEmit": false, "isolatedModules": true, "verbatimModuleSyntax": false, "noImplicitAny": false, "preserveConstEnums": true, "importsNotUsedAsValues": "remove"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"]}