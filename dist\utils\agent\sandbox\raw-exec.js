import { spawn } from 'child_process';
import { resolve } from 'path';
import { existsSync } from 'fs';
import { logInfo, logError } from '../../logger/log.js';
export async function exec(input, config) {
    const startTime = Date.now();
    logInfo('Executing command (raw)', {
        command: input.command,
        workdir: input.workdir,
        timeout: input.timeout
    });
    try {
        validateCommand(input, config);
        const workdir = input.workdir ? resolve(input.workdir) : process.cwd();
        if (!existsSync(workdir)) {
            throw new Error(`Working directory does not exist: ${workdir}`);
        }
        const result = await executeCommand(input.command, workdir, input.timeout || 30000);
        const duration = Date.now() - startTime;
        logInfo('Command execution completed', {
            command: input.command,
            exitCode: result.exitCode,
            duration,
            success: result.exitCode === 0
        });
        return {
            success: result.exitCode === 0,
            output: result.stdout + (result.stderr ? `\nSTDERR:\n${result.stderr}` : ''),
            error: result.stderr || undefined,
            exitCode: result.exitCode,
            duration,
            command: input.command,
            workdir
        };
    }
    catch (error) {
        const duration = Date.now() - startTime;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logError('Command execution failed', error instanceof Error ? error : new Error(errorMessage));
        return {
            success: false,
            output: '',
            error: errorMessage,
            exitCode: -1,
            duration,
            command: input.command,
            workdir: input.workdir || process.cwd()
        };
    }
}
function validateCommand(input, config) {
    const command = input.command;
    if (!command || command.length === 0) {
        throw new Error('Command is required');
    }
    const commandName = command[0].toLowerCase();
    const dangerousCommands = config.dangerousCommands || [
        'rm', 'del', 'format', 'fdisk', 'mkfs',
        'sudo', 'su', 'chmod', 'chown',
        'shutdown', 'reboot', 'halt',
        'dd', 'shred', 'wipe'
    ];
    if (dangerousCommands.includes(commandName)) {
        throw new Error(`Dangerous command blocked: ${commandName}`);
    }
    const fullCommand = command.join(' ');
    const suspiciousPatterns = [
        /rm\s+-rf\s+\//,
        />\s*\/dev\//,
        /curl.*\|\s*sh/,
        /wget.*\|\s*sh/,
        /eval\s*\$/,
        /exec\s*\$/,
    ];
    for (const pattern of suspiciousPatterns) {
        if (pattern.test(fullCommand)) {
            throw new Error(`Suspicious command pattern detected: ${pattern.source}`);
        }
    }
    if (input.workdir) {
        const workdir = resolve(input.workdir);
        const restrictedPaths = [
            '/etc',
            '/boot',
            '/sys',
            '/proc',
            '/dev',
            'C:\\Windows\\System32',
            'C:\\Windows\\SysWOW64'
        ];
        for (const restricted of restrictedPaths) {
            if (workdir.startsWith(restricted)) {
                throw new Error(`Access to restricted directory blocked: ${workdir}`);
            }
        }
    }
}
function executeCommand(command, workdir, timeout) {
    return new Promise((resolve, reject) => {
        const [cmd, ...args] = command;
        const child = spawn(cmd, args, {
            cwd: workdir,
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: process.platform === 'win32',
            timeout
        });
        let stdout = '';
        let stderr = '';
        child.stdout?.on('data', (data) => {
            stdout += data.toString();
        });
        child.stderr?.on('data', (data) => {
            stderr += data.toString();
        });
        child.on('close', (code) => {
            resolve({
                stdout: stdout.trim(),
                stderr: stderr.trim(),
                exitCode: code || 0
            });
        });
        child.on('error', (error) => {
            reject(new Error(`Process error: ${error.message}`));
        });
        setTimeout(() => {
            if (!child.killed) {
                child.kill('SIGTERM');
                setTimeout(() => {
                    if (!child.killed) {
                        child.kill('SIGKILL');
                    }
                }, 5000);
                reject(new Error(`Command timed out after ${timeout}ms`));
            }
        }, timeout);
    });
}
export function isCommandSafe(command, config) {
    try {
        validateCommand({ command }, config);
        return true;
    }
    catch (error) {
        return false;
    }
}
export function getExecutionEnvironment() {
    return {
        platform: process.platform,
        shell: process.platform === 'win32' ? 'cmd' : 'sh',
        sandboxing: false,
        restrictions: [
            'Basic command validation',
            'Dangerous command blocking',
            'Suspicious pattern detection',
            'Restricted directory protection'
        ]
    };
}
export async function testExecution() {
    const capabilities = [];
    const limitations = [];
    try {
        const testResult = await executeCommand(['echo', 'test'], process.cwd(), 5000);
        if (testResult.exitCode === 0 && testResult.stdout.includes('test')) {
            capabilities.push('Basic command execution');
        }
    }
    catch (error) {
        limitations.push('Basic command execution failed');
    }
    if (process.platform === 'win32') {
        capabilities.push('Windows command execution');
        limitations.push('No Unix-style sandboxing');
    }
    else {
        capabilities.push('Unix-style command execution');
        limitations.push('No advanced sandboxing (raw execution)');
    }
    limitations.push('No filesystem isolation');
    limitations.push('No network isolation');
    limitations.push('No resource limits');
    return {
        success: capabilities.length > 0,
        capabilities,
        limitations
    };
}
