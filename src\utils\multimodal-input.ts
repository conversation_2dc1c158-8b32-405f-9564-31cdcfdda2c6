/**
 * Multi-Modal Input Processing System
 * 
 * Handles various input types including text, files, images, and URLs
 * Provides unified processing for different content types
 */

import { readFileSync, existsSync, statSync } from 'fs';
import { resolve, extname, basename } from 'path';
import { logInfo, logError } from './logger/log.js';
import type { MessageContent } from '../types/index.js';

export interface ProcessedInput {
  type: 'text' | 'file' | 'image' | 'url' | 'mixed';
  content: MessageContent[];
  metadata: {
    originalInput: string;
    processedFiles: string[];
    processedImages: string[];
    processedUrls: string[];
    totalSize: number;
  };
}

export interface FileProcessingOptions {
  maxFileSize: number; // in bytes
  allowedExtensions: string[];
  includeMetadata: boolean;
  truncateContent: boolean;
  maxContentLength: number;
}

export interface ImageProcessingOptions {
  maxImageSize: number; // in bytes
  supportedFormats: string[];
  includeMetadata: boolean;
}

const DEFAULT_FILE_OPTIONS: FileProcessingOptions = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedExtensions: [
    '.txt', '.md', '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c',
    '.h', '.css', '.html', '.xml', '.json', '.yaml', '.yml', '.toml', '.ini',
    '.sh', '.bat', '.ps1', '.sql', '.go', '.rs', '.php', '.rb', '.swift',
    '.kt', '.scala', '.clj', '.hs', '.elm', '.dart', '.vue', '.svelte'
  ],
  includeMetadata: true,
  truncateContent: true,
  maxContentLength: 50000 // 50k characters
};

const DEFAULT_IMAGE_OPTIONS: ImageProcessingOptions = {
  maxImageSize: 20 * 1024 * 1024, // 20MB
  supportedFormats: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
  includeMetadata: true
};

/**
 * Process multi-modal input string
 */
export function processMultiModalInput(
  input: string,
  fileOptions: Partial<FileProcessingOptions> = {},
  imageOptions: Partial<ImageProcessingOptions> = {}
): ProcessedInput {
  const finalFileOptions = { ...DEFAULT_FILE_OPTIONS, ...fileOptions };
  const finalImageOptions = { ...DEFAULT_IMAGE_OPTIONS, ...imageOptions };

  logInfo('Processing multi-modal input', { 
    inputLength: input.length,
    fileOptions: finalFileOptions,
    imageOptions: finalImageOptions
  });

  const result: ProcessedInput = {
    type: 'text',
    content: [],
    metadata: {
      originalInput: input,
      processedFiles: [],
      processedImages: [],
      processedUrls: [],
      totalSize: 0
    }
  };

  try {
    // Parse input for different content types
    const segments = parseInputSegments(input);
    
    for (const segment of segments) {
      switch (segment.type) {
        case 'text':
          result.content.push({
            type: 'input_text',
            text: segment.content
          });
          break;

        case 'file':
          const fileContent = processFileReference(segment.content, finalFileOptions);
          if (fileContent) {
            result.content.push(fileContent);
            result.metadata.processedFiles.push(segment.content);
            result.metadata.totalSize += (fileContent.type === 'input_text' ? fileContent.text?.length : 0) || 0;
          }
          break;

        case 'image':
          const imageContent = processImageReference(segment.content, finalImageOptions);
          if (imageContent) {
            result.content.push(imageContent);
            result.metadata.processedImages.push(segment.content);
            result.metadata.totalSize += (imageContent.type === 'image' && imageContent.source?.data?.length) || 0;
          }
          break;

        case 'url':
          const urlContent = processUrlReference(segment.content);
          if (urlContent) {
            result.content.push(urlContent);
            result.metadata.processedUrls.push(segment.content);
            result.metadata.totalSize += (urlContent.type === 'input_text' ? urlContent.text?.length : 0) || 0;
          }
          break;
      }
    }

    // Determine overall type
    if (result.metadata.processedFiles.length > 0 || 
        result.metadata.processedImages.length > 0 || 
        result.metadata.processedUrls.length > 0) {
      result.type = 'mixed';
    }

    logInfo('Multi-modal input processed', {
      type: result.type,
      contentItems: result.content.length,
      files: result.metadata.processedFiles.length,
      images: result.metadata.processedImages.length,
      urls: result.metadata.processedUrls.length,
      totalSize: result.metadata.totalSize
    });

    return result;

  } catch (error) {
    logError('Failed to process multi-modal input', error instanceof Error ? error : new Error(String(error)));
    
    // Fallback to plain text
    return {
      type: 'text',
      content: [{ type: 'input_text', text: input }],
      metadata: {
        originalInput: input,
        processedFiles: [],
        processedImages: [],
        processedUrls: [],
        totalSize: input.length
      }
    };
  }
}

/**
 * Parse input into segments
 */
interface InputSegment {
  type: 'text' | 'file' | 'image' | 'url';
  content: string;
}

function parseInputSegments(input: string): InputSegment[] {
  const segments: InputSegment[] = [];
  let currentPos = 0;

  // Regex patterns for different content types
  const patterns = [
    { type: 'file' as const, regex: /@([^\s]+)/g },
    { type: 'image' as const, regex: /!\[([^\]]*)\]\(([^)]+)\)/g },
    { type: 'url' as const, regex: /https?:\/\/[^\s]+/g }
  ];

  const matches: Array<{ type: 'file' | 'image' | 'url'; start: number; end: number; content: string }> = [];

  // Find all matches
  for (const pattern of patterns) {
    let match;
    pattern.regex.lastIndex = 0; // Reset regex
    
    while ((match = pattern.regex.exec(input)) !== null) {
      matches.push({
        type: pattern.type,
        start: match.index,
        end: match.index + match[0].length,
        content: pattern.type === 'file' ? match[1] : 
                pattern.type === 'image' ? match[2] : 
                match[0]
      });
    }
  }

  // Sort matches by position
  matches.sort((a, b) => a.start - b.start);

  // Extract segments
  for (const match of matches) {
    // Add text before match
    if (currentPos < match.start) {
      const textContent = input.substring(currentPos, match.start).trim();
      if (textContent) {
        segments.push({ type: 'text', content: textContent });
      }
    }

    // Add the match
    segments.push({ type: match.type, content: match.content });
    currentPos = match.end;
  }

  // Add remaining text
  if (currentPos < input.length) {
    const textContent = input.substring(currentPos).trim();
    if (textContent) {
      segments.push({ type: 'text', content: textContent });
    }
  }

  // If no special content found, return as single text segment
  if (segments.length === 0) {
    segments.push({ type: 'text', content: input });
  }

  return segments;
}

/**
 * Process file reference
 */
function processFileReference(
  filePath: string,
  options: FileProcessingOptions
): MessageContent | null {
  try {
    const resolvedPath = resolve(filePath);
    
    if (!existsSync(resolvedPath)) {
      logError('File not found', new Error(`File does not exist: ${resolvedPath}`));
      return {
        type: 'input_text',
        text: `[File not found: ${filePath}]`
      };
    }

    const stats = statSync(resolvedPath);
    const fileExt = extname(resolvedPath).toLowerCase();
    const fileName = basename(resolvedPath);

    // Check file size
    if (stats.size > options.maxFileSize) {
      return {
        type: 'input_text',
        text: `[File too large: ${fileName} (${Math.round(stats.size / 1024)}KB > ${Math.round(options.maxFileSize / 1024)}KB)]`
      };
    }

    // Check file extension
    if (options.allowedExtensions.length > 0 && !options.allowedExtensions.includes(fileExt)) {
      return {
        type: 'input_text',
        text: `[Unsupported file type: ${fileName} (${fileExt})]`
      };
    }

    // Read file content
    let content = readFileSync(resolvedPath, 'utf-8');
    
    // Truncate if necessary
    if (options.truncateContent && content.length > options.maxContentLength) {
      content = content.substring(0, options.maxContentLength) + '\n\n[Content truncated...]';
    }

    // Add metadata if requested
    let finalContent = content;
    if (options.includeMetadata) {
      const metadata = [
        `File: ${fileName}`,
        `Path: ${resolvedPath}`,
        `Size: ${stats.size} bytes`,
        `Modified: ${stats.mtime.toISOString()}`,
        '---'
      ].join('\n');
      finalContent = metadata + '\n' + content;
    }

    return {
      type: 'input_text',
      text: finalContent
    };

  } catch (error) {
    logError('Failed to process file reference', error instanceof Error ? error : new Error(String(error)));
    return {
      type: 'input_text',
      text: `[Error reading file: ${filePath}]`
    };
  }
}

/**
 * Process image reference
 */
function processImageReference(
  imagePath: string,
  options: ImageProcessingOptions
): MessageContent | null {
  try {
    const resolvedPath = resolve(imagePath);
    
    if (!existsSync(resolvedPath)) {
      return {
        type: 'input_text',
        text: `[Image not found: ${imagePath}]`
      };
    }

    const stats = statSync(resolvedPath);
    const imageExt = extname(resolvedPath).toLowerCase();
    const imageName = basename(resolvedPath);

    // Check file size
    if (stats.size > options.maxImageSize) {
      return {
        type: 'input_text',
        text: `[Image too large: ${imageName} (${Math.round(stats.size / 1024)}KB > ${Math.round(options.maxImageSize / 1024)}KB)]`
      };
    }

    // Check format
    if (!options.supportedFormats.includes(imageExt)) {
      return {
        type: 'input_text',
        text: `[Unsupported image format: ${imageName} (${imageExt})]`
      };
    }

    // Read image as base64
    const imageData = readFileSync(resolvedPath);
    const base64Data = imageData.toString('base64');
    const mimeType = getMimeType(imageExt);

    return {
      type: 'image',
      source: {
        type: 'base64',
        media_type: mimeType,
        data: base64Data
      }
    };

  } catch (error) {
    logError('Failed to process image reference', error instanceof Error ? error : new Error(String(error)));
    return {
      type: 'input_text',
      text: `[Error reading image: ${imagePath}]`
    };
  }
}

/**
 * Process URL reference
 */
function processUrlReference(url: string): MessageContent | null {
  // For now, just return the URL as text
  // In a full implementation, this could fetch and process the URL content
  return {
    type: 'input_text',
    text: `[URL: ${url}]`
  };
}

/**
 * Get MIME type for image extension
 */
function getMimeType(extension: string): string {
  const mimeTypes: Record<string, string> = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.bmp': 'image/bmp',
    '.webp': 'image/webp'
  };

  return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
}
