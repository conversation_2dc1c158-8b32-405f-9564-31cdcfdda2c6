export interface GitDiffOptions {
    staged?: boolean;
    cached?: boolean;
    unified?: number;
    nameOnly?: boolean;
    statOnly?: boolean;
    colorWords?: boolean;
    ignoreWhitespace?: boolean;
    files?: string[];
    since?: string;
    until?: string;
}
export interface GitDiffResult {
    success: boolean;
    diff: string;
    files: string[];
    stats?: {
        insertions: number;
        deletions: number;
        filesChanged: number;
    };
    error?: string;
}
export declare function getGitDiff(workingDirectory?: string, options?: GitDiffOptions): Promise<GitDiffResult>;
export declare function getGitStatus(workingDirectory?: string): Promise<{
    success: boolean;
    status: string;
    files: Array<{
        path: string;
        status: string;
        staged: boolean;
        modified: boolean;
    }>;
    error?: string;
}>;
export declare function getGitBranch(workingDirectory?: string): Promise<{
    success: boolean;
    branch: string;
    commit: string;
    upstream?: string;
    error?: string;
}>;
export declare function isGitRepository(workingDirectory?: string): Promise<boolean>;
//# sourceMappingURL=get-diff.d.ts.map