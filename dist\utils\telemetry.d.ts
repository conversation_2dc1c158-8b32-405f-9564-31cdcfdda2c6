export interface TelemetryEvent {
    id: string;
    type: TelemetryEventType;
    timestamp: number;
    sessionId: string;
    userId?: string;
    data: Record<string, any>;
    metadata: {
        version: string;
        platform: string;
        arch: string;
        nodeVersion: string;
    };
}
export type TelemetryEventType = 'app_start' | 'app_exit' | 'command_executed' | 'model_changed' | 'provider_changed' | 'error_occurred' | 'performance_metric' | 'feature_used' | 'session_created' | 'session_loaded' | 'file_processed' | 'tool_called' | 'approval_requested' | 'approval_granted' | 'approval_denied';
export interface TelemetryConfig {
    enabled: boolean;
    endpoint?: string;
    apiKey?: string;
    batchSize: number;
    flushInterval: number;
    retryAttempts: number;
    anonymize: boolean;
    collectPerformance: boolean;
    collectErrors: boolean;
    collectUsage: boolean;
}
export interface PerformanceMetric {
    name: string;
    value: number;
    unit: string;
    timestamp: number;
    context?: Record<string, any>;
}
export interface UsageStats {
    totalCommands: number;
    totalSessions: number;
    totalErrors: number;
    averageSessionDuration: number;
    mostUsedProvider: string;
    mostUsedModel: string;
    featuresUsed: string[];
    lastActivity: number;
}
declare class TelemetryManager {
    private config;
    private sessionId;
    private userId?;
    private events;
    private flushTimer?;
    private telemetryDir;
    private sessionStartTime;
    constructor(config?: Partial<TelemetryConfig>);
    private initializeTelemetry;
    trackEvent(type: TelemetryEventType, data?: Record<string, any>): void;
    trackPerformance(metric: PerformanceMetric): void;
    trackError(error: Error, context?: Record<string, any>): void;
    trackCommand(command: string, success: boolean, duration: number, context?: Record<string, any>): void;
    trackFeature(feature: string, data?: Record<string, any>): void;
    getUsageStats(): UsageStats;
    updateUsageStats(updates: Partial<UsageStats>): void;
    flush(): Promise<void>;
    shutdown(): Promise<void>;
    private generateSessionId;
    private generateEventId;
    private loadUserId;
    private hashUserId;
    private sanitizeData;
    private sanitizeCommand;
    private startFlushTimer;
    private saveEventsLocally;
    private sendEventsToEndpoint;
}
export declare function initializeTelemetry(config?: Partial<TelemetryConfig>): void;
export declare function getTelemetry(): TelemetryManager | null;
export declare function trackEvent(type: TelemetryEventType, data?: Record<string, any>): void;
export declare function trackPerformance(metric: PerformanceMetric): void;
export declare function trackError(error: Error, context?: Record<string, any>): void;
export declare function trackCommand(command: string, success: boolean, duration: number, context?: Record<string, any>): void;
export declare function trackFeature(feature: string, data?: Record<string, any>): void;
export declare function shutdownTelemetry(): Promise<void>;
export {};
//# sourceMappingURL=telemetry.d.ts.map