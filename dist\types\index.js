export class KritrimaError extends <PERSON>rror {
    code;
    details;
    constructor(message, code, details) {
        super(message);
        this.code = code;
        this.details = details;
        this.name = 'KritrimaError';
    }
}
export class NetworkError extends KritrimaError {
    constructor(message, details) {
        super(message, 'NETWORK_ERROR', details);
    }
}
export class ConfigError extends KritrimaError {
    constructor(message, details) {
        super(message, 'CONFIG_ERROR', details);
    }
}
export class SecurityError extends KritrimaError {
    constructor(message, details) {
        super(message, 'SECURITY_ERROR', details);
    }
}
