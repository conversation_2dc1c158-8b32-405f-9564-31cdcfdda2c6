import { promises as fs } from 'fs';
import { join } from 'path';
import { logInfo, logWarn, logError } from '../logger/log.js';
export class ApprovalManager {
    pendingRequests = new Map();
    auditLog = [];
    policy;
    constructor(policy) {
        this.policy = {
            autoApprove: {
                lowRisk: true,
                mediumRisk: false,
                highRisk: false,
                criticalRisk: false
            },
            requiredApprovals: {
                low: 0,
                medium: 1,
                high: 2,
                critical: 3
            },
            approvalTimeout: 30,
            auditLogging: true,
            restrictedPaths: [
                '/etc',
                '/usr/bin',
                '/usr/sbin',
                '/bin',
                '/sbin',
                'C:\\Windows',
                'C:\\Program Files'
            ],
            allowedCommands: [
                'ls', 'dir', 'cat', 'type', 'echo', 'pwd', 'cd',
                'git', 'npm', 'yarn', 'node', 'python', 'pip'
            ],
            blockedCommands: [
                'rm', 'del', 'format', 'fdisk', 'mkfs',
                'sudo', 'su', 'chmod', 'chown'
            ],
            ...policy
        };
    }
    async requestApproval(action) {
        const requiredApprovals = this.policy.requiredApprovals[action.riskLevel];
        const expiresAt = new Date(Date.now() + this.policy.approvalTimeout * 60 * 1000).toISOString();
        const request = {
            id: this.generateRequestId(),
            action,
            requiredApprovals,
            approvals: [],
            status: 'pending',
            expiresAt,
            createdAt: new Date().toISOString()
        };
        if (this.shouldAutoApprove(action.riskLevel)) {
            request.status = 'approved';
            request.approvals.push({
                userId: 'system',
                decision: 'approve',
                reason: 'Auto-approved based on policy',
                timestamp: new Date().toISOString()
            });
        }
        else {
            this.pendingRequests.set(request.id, request);
        }
        if (this.policy.auditLogging) {
            this.auditLog.push(action);
            await this.saveAuditLog();
        }
        logInfo(`Approval request ${request.id} created for ${action.type}: ${action.description}`);
        return request;
    }
    async provideApproval(requestId, userId, decision, reason) {
        const request = this.pendingRequests.get(requestId);
        if (!request) {
            throw new Error(`Approval request ${requestId} not found`);
        }
        if (request.status !== 'pending') {
            throw new Error(`Request ${requestId} is no longer pending`);
        }
        if (new Date() > new Date(request.expiresAt)) {
            request.status = 'expired';
            this.pendingRequests.delete(requestId);
            throw new Error(`Request ${requestId} has expired`);
        }
        const existingApproval = request.approvals.find(a => a.userId === userId);
        if (existingApproval) {
            throw new Error(`User ${userId} has already provided approval for request ${requestId}`);
        }
        const approval = {
            userId,
            decision,
            reason,
            timestamp: new Date().toISOString()
        };
        request.approvals.push(approval);
        const approvals = request.approvals.filter(a => a.decision === 'approve').length;
        const rejections = request.approvals.filter(a => a.decision === 'reject').length;
        if (rejections > 0) {
            request.status = 'rejected';
            this.pendingRequests.delete(requestId);
        }
        else if (approvals >= request.requiredApprovals) {
            request.status = 'approved';
            this.pendingRequests.delete(requestId);
        }
        logInfo(`Approval provided for request ${requestId}: ${decision} by ${userId}`);
        return request.status === 'approved';
    }
    assessRisk(action) {
        if (action.type === 'file_delete') {
            if (this.isRestrictedPath(action.target)) {
                return 'critical';
            }
            return 'medium';
        }
        if (action.type === 'file_write') {
            if (this.isRestrictedPath(action.target)) {
                return 'high';
            }
            if (action.target.includes('config') || action.target.includes('.env')) {
                return 'medium';
            }
            return 'low';
        }
        if (action.type === 'command_execute') {
            const command = action.target.split(' ')[0];
            if (this.policy.blockedCommands.includes(command)) {
                return 'critical';
            }
            if (this.policy.allowedCommands.includes(command)) {
                return 'low';
            }
            return 'medium';
        }
        if (action.type === 'network_request') {
            if (action.target.includes('localhost') || action.target.includes('127.0.0.1')) {
                return 'low';
            }
            return 'medium';
        }
        if (action.type === 'system_modify') {
            return 'high';
        }
        return 'medium';
    }
    createSecurityAction(type, description, target, metadata = {}) {
        const action = {
            type,
            description,
            target,
            metadata
        };
        const riskLevel = this.assessRisk(action);
        return {
            ...action,
            id: this.generateActionId(),
            riskLevel,
            timestamp: new Date().toISOString()
        };
    }
    isRestrictedPath(path) {
        return this.policy.restrictedPaths.some(restricted => path.startsWith(restricted));
    }
    shouldAutoApprove(riskLevel) {
        switch (riskLevel) {
            case 'low': return this.policy.autoApprove.lowRisk;
            case 'medium': return this.policy.autoApprove.mediumRisk;
            case 'high': return this.policy.autoApprove.highRisk;
            case 'critical': return this.policy.autoApprove.criticalRisk;
            default: return false;
        }
    }
    generateRequestId() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substring(2);
        return `req-${timestamp}-${random}`;
    }
    generateActionId() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substring(2);
        return `act-${timestamp}-${random}`;
    }
    async saveAuditLog() {
        try {
            const logPath = join(process.cwd(), '.security-audit.json');
            await fs.writeFile(logPath, JSON.stringify(this.auditLog, null, 2));
        }
        catch (error) {
            logError('Failed to save audit log', error instanceof Error ? error : new Error(String(error)));
        }
    }
    getPendingRequests() {
        return Array.from(this.pendingRequests.values());
    }
    getAuditLog() {
        return [...this.auditLog];
    }
    updatePolicy(updates) {
        this.policy = { ...this.policy, ...updates };
        logInfo('Security policy updated');
    }
    getPolicy() {
        return { ...this.policy };
    }
    cleanupExpiredRequests() {
        const now = new Date();
        for (const [id, request] of this.pendingRequests) {
            if (new Date(request.expiresAt) < now) {
                request.status = 'expired';
                this.pendingRequests.delete(id);
                logWarn(`Request ${id} expired and was removed`);
            }
        }
    }
}
export const approvalManager = new ApprovalManager();
export async function requestFileApproval(operation, filePath, description) {
    const actionType = operation === 'delete' ? 'file_delete' : 'file_write';
    const actionDescription = description || `${operation} file: ${filePath}`;
    const action = approvalManager.createSecurityAction(actionType, actionDescription, filePath, { operation });
    return approvalManager.requestApproval(action);
}
export async function requestCommandApproval(command, args = [], description) {
    const fullCommand = `${command} ${args.join(' ')}`.trim();
    const actionDescription = description || `Execute command: ${fullCommand}`;
    const action = approvalManager.createSecurityAction('command_execute', actionDescription, fullCommand, { command, args });
    return approvalManager.requestApproval(action);
}
