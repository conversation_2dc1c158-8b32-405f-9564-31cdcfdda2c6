import type { ApprovalPolicy } from '../../types/index.js';
interface ApprovalOverlayProps {
    currentMode: ApprovalPolicy;
    onModeChange: (mode: ApprovalPolicy) => void;
    onClose: () => void;
    visible: boolean;
}
export declare function ApprovalOverlay({ currentMode, onModeChange, onClose, visible }: ApprovalOverlayProps): import("react/jsx-runtime").JSX.Element | null;
export {};
//# sourceMappingURL=approval-overlay.d.ts.map