name: Build and Publish to NPM

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to publish (e.g., 1.0.1)'
        required: true
        default: '1.0.1'

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18, 20, 22]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linting
      run: npm run lint
    
    - name: Build project
      run: npm run build
    
    - name: Test build
      run: npm run test:build

  test-platforms:
    needs: test
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        node-version: [18, 20]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build project
      run: npm run build
    
    - name: Test CLI functionality
      run: |
        node bin/kritrima-ai.js --help
        node bin/kritrima-ai.js --list-providers
        node bin/kritrima-ai.js --version

  publish:
    needs: [test, test-platforms]
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v') || github.event_name == 'workflow_dispatch'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js 20
      uses: actions/setup-node@v4
      with:
        node-version: 20
        cache: 'npm'
        registry-url: 'https://registry.npmjs.org'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Update version if manual trigger
      if: github.event_name == 'workflow_dispatch'
      run: npm version ${{ github.event.inputs.version }} --no-git-tag-version
    
    - name: Build project
      run: npm run build
    
    - name: Publish to NPM
      run: npm publish --access public
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
    
    - name: Create GitHub Release
      if: startsWith(github.ref, 'refs/tags/v')
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        body: |
          ## Changes
          
          See [CHANGELOG.md](CHANGELOG.md) for details.
          
          ## Installation
          
          ```bash
          npm install -g kritrima-ai
          ```
          
          ## Platform Support
          
          - ✅ Windows 11 (including WSL)
          - ✅ macOS (Intel & Apple Silicon)
          - ✅ Linux (Ubuntu, Debian, CentOS, etc.)
        draft: false
        prerelease: false
