import type { MessageContent } from '../../types/index.js';
export interface FullContextOptions {
    includeFiles: boolean;
    includeGitInfo: boolean;
    includeSystemInfo: boolean;
    includeProjectStructure: boolean;
    maxFileSize: number;
    maxTotalSize: number;
    fileExtensions: string[];
    excludePatterns: string[];
    maxDepth: number;
}
export interface ContextAnalysis {
    projectType: string;
    technologies: string[];
    entryPoints: string[];
    configFiles: string[];
    documentation: string[];
    testFiles: string[];
    buildFiles: string[];
}
export interface FullContextResult {
    content: MessageContent[];
    analysis: ContextAnalysis;
    includedFiles: string[];
    totalSize: number;
    warnings: string[];
}
export declare function gatherFullContext(workingDir?: string, userQuery?: string, options?: Partial<FullContextOptions>): Promise<FullContextResult>;
//# sourceMappingURL=full-context-mode.d.ts.map