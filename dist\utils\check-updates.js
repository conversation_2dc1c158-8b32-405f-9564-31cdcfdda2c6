import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { homedir } from 'os';
import semver from 'semver';
import chalk from 'chalk';
import { CLI_VERSION } from '../version.js';
import { detectInstallerByPath } from './package-manager-detector.js';
const UPDATE_CHECK_INTERVAL = 24 * 60 * 60 * 1000;
const UPDATE_CHECK_FILE = join(homedir(), '.kritrima-ai', 'last-update-check');
const NPM_REGISTRY_URL = 'https://registry.npmjs.org/kritrima-ai-cli';
export async function checkForUpdates() {
    try {
        if (!shouldCheckForUpdates()) {
            return null;
        }
        const updateInfo = await performUpdateCheck();
        saveUpdateCheckInfo(updateInfo);
        if (updateInfo.hasUpdate) {
            displayUpdateNotification(updateInfo);
        }
        return updateInfo;
    }
    catch (_error) {
        return null;
    }
}
function shouldCheckForUpdates() {
    try {
        if (!existsSync(UPDATE_CHECK_FILE)) {
            return true;
        }
        const content = readFileSync(UPDATE_CHECK_FILE, 'utf-8');
        const lastCheck = JSON.parse(content);
        const timeSinceLastCheck = Date.now() - lastCheck.timestamp;
        if (timeSinceLastCheck < UPDATE_CHECK_INTERVAL) {
            return false;
        }
        if (lastCheck.lastCheckedVersion !== CLI_VERSION) {
            return true;
        }
        return true;
    }
    catch (_error) {
        return true;
    }
}
async function performUpdateCheck() {
    try {
        const response = await fetch(NPM_REGISTRY_URL, {
            headers: {
                'Accept': 'application/json',
                'User-Agent': `kritrima-ai-cli/${CLI_VERSION}`
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const packageInfo = await response.json();
        const latestVersion = packageInfo['dist-tags']?.latest;
        if (!latestVersion) {
            throw new Error('Could not determine latest version');
        }
        const hasUpdate = semver.gt(latestVersion, CLI_VERSION);
        const updateCommand = await getUpdateCommand();
        const releaseNotes = await getReleaseNotes(latestVersion, packageInfo);
        return {
            hasUpdate,
            currentVersion: CLI_VERSION,
            latestVersion: hasUpdate ? latestVersion : undefined,
            updateCommand: hasUpdate ? updateCommand : undefined,
            releaseNotes: hasUpdate ? releaseNotes : undefined
        };
    }
    catch (_error) {
        return {
            hasUpdate: false,
            currentVersion: CLI_VERSION
        };
    }
}
async function getUpdateCommand() {
    try {
        const installer = await detectInstallerByPath();
        switch (installer) {
            case 'npm':
                return 'npm update -g kritrima-ai-cli';
            case 'pnpm':
                return 'pnpm update -g kritrima-ai-cli';
            case 'yarn':
                return 'yarn global upgrade kritrima-ai-cli';
            case 'bun':
                return 'bun update -g kritrima-ai-cli';
            default:
                return 'npm update -g kritrima-ai-cli';
        }
    }
    catch (_error) {
        return 'npm update -g kritrima-ai-cli';
    }
}
async function getReleaseNotes(version, packageInfo) {
    try {
        const versionInfo = packageInfo.versions?.[version];
        if (versionInfo?.description) {
            return versionInfo.description;
        }
        return `New version ${version} is available with improvements and bug fixes.`;
    }
    catch (_error) {
        return `New version ${version} is available.`;
    }
}
function saveUpdateCheckInfo(updateInfo) {
    try {
        const updateCheckDir = join(homedir(), '.kritrima-ai');
        if (!existsSync(updateCheckDir)) {
            mkdirSync(updateCheckDir, { recursive: true });
        }
        const lastCheck = {
            timestamp: Date.now(),
            lastCheckedVersion: CLI_VERSION,
            latestVersion: updateInfo.latestVersion
        };
        writeFileSync(UPDATE_CHECK_FILE, JSON.stringify(lastCheck, null, 2));
    }
    catch (_error) {
    }
}
function displayUpdateNotification(updateInfo) {
    if (!updateInfo.hasUpdate || !updateInfo.latestVersion) {
        return;
    }
    console.log();
    console.log(chalk.yellow('┌' + '─'.repeat(60) + '┐'));
    console.log(chalk.yellow('│') + chalk.bold.blue(' 🚀 Update Available!').padEnd(59) + chalk.yellow('│'));
    console.log(chalk.yellow('│') + ''.padEnd(60) + chalk.yellow('│'));
    console.log(chalk.yellow('│') + ` Current version: ${chalk.red(updateInfo.currentVersion)}`.padEnd(60) + chalk.yellow('│'));
    console.log(chalk.yellow('│') + ` Latest version:  ${chalk.green(updateInfo.latestVersion)}`.padEnd(60) + chalk.yellow('│'));
    console.log(chalk.yellow('│') + ''.padEnd(60) + chalk.yellow('│'));
    if (updateInfo.releaseNotes) {
        const notes = updateInfo.releaseNotes.length > 45
            ? updateInfo.releaseNotes.substring(0, 42) + '...'
            : updateInfo.releaseNotes;
        console.log(chalk.yellow('│') + ` ${notes}`.padEnd(60) + chalk.yellow('│'));
        console.log(chalk.yellow('│') + ''.padEnd(60) + chalk.yellow('│'));
    }
    if (updateInfo.updateCommand) {
        console.log(chalk.yellow('│') + ` Run: ${chalk.cyan(updateInfo.updateCommand)}`.padEnd(60) + chalk.yellow('│'));
    }
    console.log(chalk.yellow('└' + '─'.repeat(60) + '┘'));
    console.log();
}
export async function forceCheckForUpdates() {
    const updateInfo = await performUpdateCheck();
    saveUpdateCheckInfo(updateInfo);
    if (updateInfo.hasUpdate) {
        displayUpdateNotification(updateInfo);
    }
    else {
        console.log(chalk.green('✓ You are using the latest version!'));
    }
    return updateInfo;
}
export function getLastUpdateCheck() {
    try {
        if (!existsSync(UPDATE_CHECK_FILE)) {
            return null;
        }
        const content = readFileSync(UPDATE_CHECK_FILE, 'utf-8');
        return JSON.parse(content);
    }
    catch (_error) {
        return null;
    }
}
export function clearUpdateCheckCache() {
    try {
        if (existsSync(UPDATE_CHECK_FILE)) {
            writeFileSync(UPDATE_CHECK_FILE, JSON.stringify({
                timestamp: 0,
                lastCheckedVersion: CLI_VERSION
            }, null, 2));
        }
    }
    catch (_error) {
    }
}
export function disableUpdateChecks() {
    try {
        const updateCheckDir = join(homedir(), '.kritrima-ai');
        if (!existsSync(updateCheckDir)) {
            mkdirSync(updateCheckDir, { recursive: true });
        }
        const disabledCheck = {
            timestamp: Date.now() + (365 * 24 * 60 * 60 * 1000),
            lastCheckedVersion: CLI_VERSION
        };
        writeFileSync(UPDATE_CHECK_FILE, JSON.stringify(disabledCheck, null, 2));
    }
    catch (_error) {
    }
}
