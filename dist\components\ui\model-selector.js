import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Text, useInput, useApp } from 'ink';
import chalk from 'chalk';
import { fetchModels } from '../../utils/model-utils.js';
import { getAvailableProviders } from '../../utils/providers.js';
import { getApiKey } from '../../utils/config.js';
export function ModelSelector({ currentProvider, currentModel, onSelect, onCancel }) {
    useApp();
    const [selectedProvider, setSelectedProvider] = useState(currentProvider);
    const [selectedModel, setSelectedModel] = useState(currentModel);
    const [providers, setProviders] = useState({});
    const [mode, setMode] = useState('provider');
    const [providerIndex, setProviderIndex] = useState(0);
    const [modelIndex, setModelIndex] = useState(0);
    useEffect(() => {
        const initProviders = async () => {
            const availableProviders = getAvailableProviders();
            const providerData = {};
            for (const provider of availableProviders) {
                const hasApiKey = !!getApiKey(provider);
                providerData[provider] = {
                    name: provider,
                    hasApiKey,
                    models: [],
                    loading: false
                };
            }
            setProviders(providerData);
            const currentIndex = availableProviders.indexOf(currentProvider);
            if (currentIndex >= 0) {
                setProviderIndex(currentIndex);
            }
        };
        initProviders();
    }, [currentProvider]);
    useEffect(() => {
        const loadModels = async () => {
            if (!selectedProvider || !providers[selectedProvider]?.hasApiKey) {
                return;
            }
            setProviders(prev => ({
                ...prev,
                [selectedProvider]: {
                    ...prev[selectedProvider],
                    loading: true,
                    error: undefined
                }
            }));
            try {
                const models = await fetchModels(selectedProvider);
                setProviders(prev => ({
                    ...prev,
                    [selectedProvider]: {
                        ...prev[selectedProvider],
                        models,
                        loading: false
                    }
                }));
                if (models.length > 0) {
                    const currentIndex = models.indexOf(currentModel);
                    setModelIndex(currentIndex >= 0 ? currentIndex : 0);
                    if (currentIndex < 0) {
                        setSelectedModel(models[0]);
                    }
                }
            }
            catch (error) {
                setProviders(prev => ({
                    ...prev,
                    [selectedProvider]: {
                        ...prev[selectedProvider],
                        loading: false,
                        error: error instanceof Error ? error.message : 'Failed to load models'
                    }
                }));
            }
        };
        loadModels();
    }, [selectedProvider, currentModel]);
    useInput((input, key) => {
        if (key.escape) {
            onCancel();
            return;
        }
        if (key.return) {
            if (mode === 'provider') {
                const providerNames = Object.keys(providers);
                const provider = providerNames[providerIndex];
                if (providers[provider]?.hasApiKey) {
                    setSelectedProvider(provider);
                    setMode('model');
                    setModelIndex(0);
                }
            }
            else {
                onSelect(selectedProvider, selectedModel);
            }
            return;
        }
        if (key.tab) {
            if (mode === 'model') {
                setMode('provider');
            }
            else {
                const provider = Object.keys(providers)[providerIndex];
                if (providers[provider]?.hasApiKey && providers[provider]?.models.length > 0) {
                    setMode('model');
                }
            }
            return;
        }
        if (key.upArrow) {
            if (mode === 'provider') {
                setProviderIndex(prev => Math.max(0, prev - 1));
            }
            else {
                setModelIndex(prev => Math.max(0, prev - 1));
            }
            return;
        }
        if (key.downArrow) {
            if (mode === 'provider') {
                setProviderIndex(prev => Math.min(Object.keys(providers).length - 1, prev + 1));
            }
            else {
                const models = providers[selectedProvider]?.models || [];
                setModelIndex(prev => Math.min(models.length - 1, prev + 1));
            }
            return;
        }
    });
    useEffect(() => {
        const providerNames = Object.keys(providers);
        if (providerNames[providerIndex]) {
            setSelectedProvider(providerNames[providerIndex]);
        }
    }, [providerIndex, providers]);
    useEffect(() => {
        const models = providers[selectedProvider]?.models || [];
        if (models[modelIndex]) {
            setSelectedModel(models[modelIndex]);
        }
    }, [modelIndex, selectedProvider, providers]);
    const renderProviders = () => {
        const providerNames = Object.keys(providers);
        return providerNames.map((provider, index) => {
            const info = providers[provider];
            const isSelected = index === providerIndex;
            const isCurrent = mode === 'provider' && isSelected;
            let status = '';
            if (!info.hasApiKey) {
                status = chalk.red(' (No API Key)');
            }
            else if (info.loading) {
                status = chalk.yellow(' (Loading...)');
            }
            else if (info.error) {
                status = chalk.red(' (Error)');
            }
            else if (info.models.length > 0) {
                status = chalk.green(` (${info.models.length} models)`);
            }
            const prefix = isCurrent ? '▶ ' : '  ';
            const text = isSelected && mode === 'model' ? chalk.bold(provider) : provider;
            return (_jsxs(Text, { color: isCurrent ? 'cyan' : undefined, children: [prefix, text, status] }, provider));
        });
    };
    const renderModels = () => {
        if (mode !== 'model' || !providers[selectedProvider]) {
            return null;
        }
        const info = providers[selectedProvider];
        if (info.loading) {
            return _jsx(Text, { color: "yellow", children: "Loading models..." });
        }
        if (info.error) {
            return _jsxs(Text, { color: "red", children: ["Error: ", info.error] });
        }
        if (info.models.length === 0) {
            return _jsx(Text, { color: "gray", children: "No models available" });
        }
        return info.models.map((model, index) => {
            const isSelected = index === modelIndex;
            const prefix = isSelected ? '▶ ' : '  ';
            return (_jsxs(Text, { color: isSelected ? 'cyan' : undefined, children: [prefix, model] }, model));
        });
    };
    return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsx(Box, { borderStyle: "single", paddingX: 1, children: _jsx(Text, { bold: true, color: "blue", children: "Model & Provider Selection" }) }), _jsx(Box, { children: _jsxs(Text, { children: ["Current: ", _jsxs(Text, { bold: true, color: "green", children: [currentProvider, "/", currentModel] })] }) }), _jsxs(Box, { flexDirection: "row", children: [_jsxs(Box, { flexDirection: "column", width: "50%", paddingRight: 2, children: [_jsx(Text, { bold: true, color: mode === 'provider' ? 'cyan' : 'gray', children: "Providers:" }), _jsx(Box, { flexDirection: "column", children: renderProviders() })] }), _jsxs(Box, { flexDirection: "column", width: "50%", children: [_jsx(Text, { bold: true, color: mode === 'model' ? 'cyan' : 'gray', children: "Models:" }), _jsx(Box, { flexDirection: "column", children: renderModels() })] })] }), _jsx(Box, { borderStyle: "single", paddingX: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: "gray", children: "\u2191/\u2193 Navigate \u2022 Tab Switch columns \u2022 Enter Select \u2022 Esc Cancel" }), mode === 'provider' && (_jsx(Text, { color: "yellow", children: "Select a provider with API key configured" })), mode === 'model' && (_jsxs(Text, { color: "yellow", children: ["Select model: ", selectedProvider, "/", selectedModel] }))] }) })] }));
}
