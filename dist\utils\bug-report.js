import { CLI_VERSION } from '../version.js';
import { loadConfig } from './config.js';
import { getGitStatus } from './check-in-git.js';
import { getHistoryStats } from './storage/command-history.js';
import { getSessionStats } from './storage/save-rollout.js';
export function generateBugReport(error, context) {
    const config = loadConfig();
    const gitStatus = getGitStatus();
    const historyStats = getHistoryStats();
    const sessionStats = getSessionStats();
    let shell;
    try {
        shell = process.env.SHELL || process.env.ComSpec || 'unknown';
    }
    catch (_e) {
        shell = 'unknown';
    }
    const environment = {};
    const safeEnvVars = [
        'NODE_VERSION',
        'NPM_VERSION',
        'TERM',
        'SHELL',
        'PATH',
        'HOME',
        'USER',
        'USERNAME',
        'COMPUTERNAME',
        'HOSTNAME'
    ];
    for (const key of safeEnvVars) {
        if (process.env[key]) {
            environment[key] = process.env[key];
        }
    }
    const report = {
        version: CLI_VERSION,
        timestamp: new Date().toISOString(),
        platform: {
            os: `${process.platform} ${process.arch}`,
            arch: process.arch,
            node: process.version,
            shell
        },
        config: {
            provider: config.provider,
            model: config.model,
            approvalMode: config.approvalMode,
            enableLogging: config.enableLogging || false,
            enableNotifications: config.enableNotifications !== false
        },
        git: gitStatus,
        usage: {
            historyStats,
            sessionStats
        },
        context: {
            workingDirectory: process.cwd(),
            environment
        }
    };
    if (error) {
        report.error = {
            message: error.message,
            stack: error.stack,
            code: error.code
        };
    }
    if (context) {
        report.context = {
            ...report.context,
            ...context
        };
    }
    return report;
}
export function generateGitHubIssueURL(report, title, description) {
    const baseURL = 'https://github.com/kritrima-ai/kritrima-ai-cli/issues/new';
    const issueTitle = title || (report.error
        ? `Bug: ${report.error.message}`
        : 'Bug Report');
    const issueBody = `
## Bug Report

**Description:**
${description || 'Please describe the issue you encountered.'}

## Environment

- **Version:** ${report.version}
- **Platform:** ${report.platform.os}
- **Node.js:** ${report.platform.node}
- **Shell:** ${report.platform.shell}

## Configuration

- **Provider:** ${report.config.provider}
- **Model:** ${report.config.model}
- **Approval Mode:** ${report.config.approvalMode}

## Error Details

${report.error ? `
**Error Message:** ${report.error.message}

**Stack Trace:**
\`\`\`
${report.error.stack || 'No stack trace available'}
\`\`\`
` : 'No error details available.'}

## Context

- **Working Directory:** ${report.context?.workingDirectory}
- **Git Repository:** ${report.git.inGit ? 'Yes' : 'No'}
${report.git.branch ? `- **Git Branch:** ${report.git.branch}` : ''}

## Usage Statistics

- **Command History:** ${report.usage.historyStats.totalCommands} commands
- **Sessions:** ${report.usage.sessionStats.totalSessions} sessions

## Additional Information

Please add any additional context, screenshots, or logs that might help diagnose the issue.

---

*This bug report was automatically generated by Kritrima AI CLI v${report.version}*
`.trim();
    const params = new URLSearchParams({
        title: issueTitle,
        body: issueBody,
        labels: 'bug'
    });
    return `${baseURL}?${params.toString()}`;
}
export function createBugReportURL(error, title, description, context) {
    const report = generateBugReport(error, context);
    return generateGitHubIssueURL(report, title, description);
}
export function saveBugReport(report, filePath) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = filePath || `bug-report-${timestamp}.json`;
    try {
        const fs = require('fs');
        fs.writeFileSync(fileName, JSON.stringify(report, null, 2));
        return fileName;
    }
    catch (error) {
        throw new Error(`Failed to save bug report: ${error}`);
    }
}
export function getSystemDiagnostics() {
    return {
        memory: process.memoryUsage(),
        uptime: process.uptime(),
        loadAverage: process.platform !== 'win32' ? require('os').loadavg() : undefined,
        cpuUsage: process.cpuUsage()
    };
}
export function checkCommonIssues() {
    const issues = [];
    const warnings = [];
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 18) {
        issues.push(`Node.js version ${nodeVersion} is not supported. Please upgrade to Node.js 18 or later.`);
    }
    const config = loadConfig();
    const envKey = `${config.provider.toUpperCase()}_API_KEY`;
    if (!process.env[envKey]) {
        issues.push(`Missing API key: ${envKey} environment variable is not set.`);
    }
    const memory = process.memoryUsage();
    if (memory.heapUsed > 500 * 1024 * 1024) {
        warnings.push('High memory usage detected. Consider restarting the CLI.');
    }
    try {
        const _stats = require('fs').statSync(process.cwd());
    }
    catch (_error) {
        warnings.push('Could not check disk space.');
    }
    return { issues, warnings };
}
export function formatBugReportForConsole(report) {
    const lines = [
        '🐛 Bug Report',
        '='.repeat(50),
        '',
        `Version: ${report.version}`,
        `Platform: ${report.platform.os}`,
        `Node.js: ${report.platform.node}`,
        `Provider: ${report.config.provider}/${report.config.model}`,
        '',
        'Error:',
        report.error ? `  ${report.error.message}` : '  No error information',
        '',
        'Context:',
        `  Working Directory: ${report.context?.workingDirectory}`,
        `  Git Repository: ${report.git.inGit ? 'Yes' : 'No'}`,
        '',
        'Usage:',
        `  Commands: ${report.usage.historyStats.totalCommands}`,
        `  Sessions: ${report.usage.sessionStats.totalSessions}`,
        '',
        '='.repeat(50)
    ];
    return lines.join('\n');
}
