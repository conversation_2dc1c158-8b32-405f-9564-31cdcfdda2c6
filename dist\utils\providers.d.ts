import type { ProviderConfig, ProviderName } from '../types/index.js';
export declare const providers: Record<ProviderName, ProviderConfig>;
export declare function getProviderConfig(providerName: string, customProviders?: Record<string, ProviderConfig>): ProviderConfig | null;
export declare function getAvailableProviders(customProviders?: Record<string, ProviderConfig>): string[];
export declare function validateProviderConfig(config: ProviderConfig): boolean;
export declare function getProviderDisplayName(providerName: string, customProviders?: Record<string, ProviderConfig>): string;
export declare function providerSupportsFeature(providerName: string, feature: 'images' | 'tools' | 'streaming'): boolean;
export declare function getDefaultModel(providerName: string): string;
//# sourceMappingURL=providers.d.ts.map