import { useEffect, useCallback, useRef } from 'react';
export function useKeyboardShortcuts(shortcuts, context, enabled = true) {
    const shortcutsRef = useRef(shortcuts);
    const enabledRef = useRef(enabled);
    useEffect(() => {
        shortcutsRef.current = shortcuts;
        enabledRef.current = enabled;
    }, [shortcuts, enabled]);
    const handleKeyPress = useCallback((ch, key) => {
        if (!enabledRef.current)
            return;
        const activeShortcuts = shortcutsRef.current.filter(shortcut => !shortcut.context || shortcut.context === context);
        for (const shortcut of activeShortcuts) {
            if (matchesShortcut(key, shortcut)) {
                shortcut.action();
                return;
            }
        }
    }, [context]);
    const registerShortcut = useCallback((shortcut) => {
        shortcutsRef.current = [...shortcutsRef.current, shortcut];
    }, []);
    const unregisterShortcut = useCallback((key) => {
        shortcutsRef.current = shortcutsRef.current.filter(s => s.key !== key);
    }, []);
    const getShortcuts = useCallback(() => {
        return shortcutsRef.current.filter(shortcut => !shortcut.context || shortcut.context === context);
    }, [context]);
    useEffect(() => {
        if (typeof process !== 'undefined' && process.stdin) {
            process.stdin.on('keypress', handleKeyPress);
            return () => {
                process.stdin.off('keypress', handleKeyPress);
            };
        }
    }, [handleKeyPress]);
    return {
        registerShortcut,
        unregisterShortcut,
        getShortcuts,
    };
}
function matchesShortcut(key, shortcut) {
    if (key.name !== shortcut.key)
        return false;
    if (shortcut.ctrl && !key.ctrl)
        return false;
    if (shortcut.alt && !key.alt)
        return false;
    if (shortcut.shift && !key.shift)
        return false;
    if (shortcut.meta && !key.meta)
        return false;
    if (!shortcut.ctrl && key.ctrl)
        return false;
    if (!shortcut.alt && key.alt)
        return false;
    if (!shortcut.shift && key.shift)
        return false;
    if (!shortcut.meta && key.meta)
        return false;
    return true;
}
export const DEFAULT_SHORTCUTS = [
    {
        key: 'escape',
        description: 'Close overlay or exit',
        action: () => { },
        context: 'global',
    },
    {
        key: 'h',
        ctrl: true,
        description: 'Show help',
        action: () => { },
        context: 'global',
    },
    {
        key: 'm',
        ctrl: true,
        description: 'Open model selector',
        action: () => { },
        context: 'global',
    },
    {
        key: 'r',
        ctrl: true,
        description: 'Open history',
        action: () => { },
        context: 'global',
    },
    {
        key: 's',
        ctrl: true,
        description: 'Open sessions',
        action: () => { },
        context: 'global',
    },
    {
        key: 'a',
        ctrl: true,
        description: 'Open approval settings',
        action: () => { },
        context: 'global',
    },
    {
        key: 'l',
        ctrl: true,
        description: 'Clear screen',
        action: () => { },
        context: 'global',
    },
];
export function formatShortcut(shortcut) {
    const parts = [];
    if (shortcut.ctrl)
        parts.push('Ctrl');
    if (shortcut.alt)
        parts.push('Alt');
    if (shortcut.shift)
        parts.push('Shift');
    if (shortcut.meta)
        parts.push('Meta');
    parts.push(shortcut.key.toUpperCase());
    return parts.join('+');
}
