export declare function checkInGit(workdir?: string): boolean;
export declare function getGitRoot(workdir?: string): string | null;
export declare function getCurrentBranch(workdir?: string): string | null;
export declare function hasUncommittedChanges(workdir?: string): boolean;
export declare function getGitStatus(workdir?: string): {
    inGit: boolean;
    root?: string;
    branch?: string;
    hasChanges?: boolean;
    isClean?: boolean;
};
export declare function isFileTracked(filePath: string, workdir?: string): boolean;
export declare function getModifiedFiles(workdir?: string): string[];
export declare function getStagedFiles(workdir?: string): string[];
export declare function getUntrackedFiles(workdir?: string): string[];
export declare function getFileStatus(workdir?: string): {
    modified: string[];
    staged: string[];
    untracked: string[];
    total: number;
};
//# sourceMappingURL=check-in-git.d.ts.map