export declare function expandFileTags(text: string, workingDir?: string): Promise<string>;
export declare function collapseXmlBlocks(text: string): string;
export declare function extractFilePaths(text: string): string[];
export declare function validateFilePaths(filePaths: string[], workingDir?: string): {
    valid: Array<{
        path: string;
        size: number;
        type: 'text' | 'binary';
    }>;
    invalid: Array<{
        path: string;
        error: string;
    }>;
};
export declare function getFileInfo(filePath: string, workingDir?: string): {
    exists: boolean;
    isFile?: boolean;
    size?: number;
    type?: 'text' | 'binary';
    error?: string;
};
export declare function previewFileContent(filePath: string, workingDir?: string, maxLines?: number): {
    success: boolean;
    preview?: string;
    totalLines?: number;
    error?: string;
};
//# sourceMappingURL=file-tag-utils.d.ts.map