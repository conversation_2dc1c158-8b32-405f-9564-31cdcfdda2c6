import type { AppConfig } from '../types/index.js';
export declare function loadConfig(workingDir?: string): AppConfig;
export declare function discoverProjectDocPath(startDir: string): string | null;
export declare function getConfig(workingDir?: string): AppConfig;
export declare function getApiKey(provider?: string): string | undefined;
export declare function getBaseUrl(provider?: string): string;
export declare function saveUserConfig(config: Partial<AppConfig>): void;
export declare function clearConfigCache(): void;
//# sourceMappingURL=config.d.ts.map