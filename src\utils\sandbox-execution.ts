/**
 * Sandbox Execution System
 * 
 * Provides secure execution environment for code and commands
 * Includes timeout handling, resource limits, and security controls
 */

import { spawn, exec, ChildProcess } from 'child_process';
import { promises as fs } from 'fs';
import { join, dirname } from 'path';
import { tmpdir } from 'os';
import { randomBytes } from 'crypto';
import { logInfo, logError, logWarn } from './logger/log.js';

export interface SandboxOptions {
  timeout?: number;
  maxMemory?: number;
  maxCpu?: number;
  workingDirectory?: string;
  environment?: Record<string, string>;
  allowNetworking?: boolean;
  allowFileSystem?: boolean;
  restrictedPaths?: string[];
}

export interface ExecutionResult {
  success: boolean;
  stdout: string;
  stderr: string;
  exitCode: number | null;
  signal: string | null;
  duration: number;
  memoryUsage?: number;
  error?: string;
}

export interface SandboxEnvironment {
  id: string;
  workingDirectory: string;
  cleanup: () => Promise<void>;
}

/**
 * Sandbox execution manager
 */
export class SandboxExecutor {
  private activeProcesses = new Map<string, ChildProcess>();
  private environments = new Map<string, SandboxEnvironment>();

  /**
   * Create a new sandbox environment
   */
  async createEnvironment(options: SandboxOptions = {}): Promise<SandboxEnvironment> {
    const id = randomBytes(16).toString('hex');
    const sandboxDir = join(tmpdir(), `sandbox-${id}`);

    try {
      // Create sandbox directory
      await fs.mkdir(sandboxDir, { recursive: true });

      // Set up environment
      const environment: SandboxEnvironment = {
        id,
        workingDirectory: sandboxDir,
        cleanup: async () => {
          try {
            await fs.rm(sandboxDir, { recursive: true, force: true });
            this.environments.delete(id);
            logInfo(`Sandbox environment ${id} cleaned up`);
          } catch (error) {
            logError(`Failed to cleanup sandbox ${id}`, error instanceof Error ? error : new Error(String(error)));
          }
        }
      };

      this.environments.set(id, environment);
      logInfo(`Created sandbox environment ${id} at ${sandboxDir}`);

      return environment;

    } catch (error) {
      logError(`Failed to create sandbox environment`, error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * Execute command in sandbox
   */
  async executeCommand(
    command: string,
    args: string[] = [],
    options: SandboxOptions = {}
  ): Promise<ExecutionResult> {
    const startTime = Date.now();
    const timeout = options.timeout || 30000; // 30 seconds default
    const workingDirectory = options.workingDirectory || process.cwd();

    logInfo(`Executing command in sandbox: ${command} ${args.join(' ')}`);

    return new Promise((resolve) => {
      const child = spawn(command, args, {
        cwd: workingDirectory,
        env: {
          ...process.env,
          ...options.environment
        },
        stdio: ['pipe', 'pipe', 'pipe']
      });

      const processId = randomBytes(8).toString('hex');
      this.activeProcesses.set(processId, child);

      let stdout = '';
      let stderr = '';
      let killed = false;

      // Set up timeout
      const timeoutHandle = setTimeout(() => {
        if (!killed) {
          killed = true;
          child.kill('SIGTERM');
          
          // Force kill after 5 seconds
          setTimeout(() => {
            if (!child.killed) {
              child.kill('SIGKILL');
            }
          }, 5000);
        }
      }, timeout);

      // Collect output
      child.stdout?.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr?.on('data', (data) => {
        stderr += data.toString();
      });

      // Handle completion
      child.on('close', (code, signal) => {
        clearTimeout(timeoutHandle);
        this.activeProcesses.delete(processId);

        const duration = Date.now() - startTime;
        const result: ExecutionResult = {
          success: code === 0 && !killed,
          stdout,
          stderr,
          exitCode: code,
          signal,
          duration
        };

        if (killed) {
          result.error = 'Command timed out';
          result.success = false;
        }

        logInfo(`Command completed in ${duration}ms with exit code ${code}`);
        resolve(result);
      });

      // Handle errors
      child.on('error', (error) => {
        clearTimeout(timeoutHandle);
        this.activeProcesses.delete(processId);

        const duration = Date.now() - startTime;
        const result: ExecutionResult = {
          success: false,
          stdout,
          stderr,
          exitCode: null,
          signal: null,
          duration,
          error: error.message
        };

        logError(`Command failed: ${error.message}`);
        resolve(result);
      });
    });
  }

  /**
   * Execute JavaScript code in sandbox
   */
  async executeJavaScript(
    code: string,
    options: SandboxOptions = {}
  ): Promise<ExecutionResult> {
    const environment = await this.createEnvironment(options);
    
    try {
      // Write code to temporary file
      const scriptPath = join(environment.workingDirectory, 'script.js');
      await fs.writeFile(scriptPath, code);

      // Execute with Node.js
      const result = await this.executeCommand('node', [scriptPath], {
        ...options,
        workingDirectory: environment.workingDirectory
      });

      return result;

    } finally {
      await environment.cleanup();
    }
  }

  /**
   * Execute Python code in sandbox
   */
  async executePython(
    code: string,
    options: SandboxOptions = {}
  ): Promise<ExecutionResult> {
    const environment = await this.createEnvironment(options);
    
    try {
      // Write code to temporary file
      const scriptPath = join(environment.workingDirectory, 'script.py');
      await fs.writeFile(scriptPath, code);

      // Execute with Python
      const result = await this.executeCommand('python', [scriptPath], {
        ...options,
        workingDirectory: environment.workingDirectory
      });

      return result;

    } finally {
      await environment.cleanup();
    }
  }

  /**
   * Execute shell script in sandbox
   */
  async executeShell(
    script: string,
    options: SandboxOptions = {}
  ): Promise<ExecutionResult> {
    const environment = await this.createEnvironment(options);
    
    try {
      // Write script to temporary file
      const scriptPath = join(environment.workingDirectory, 'script.sh');
      await fs.writeFile(scriptPath, script);
      await fs.chmod(scriptPath, 0o755);

      // Execute with shell
      const shell = process.platform === 'win32' ? 'cmd' : 'bash';
      const args = process.platform === 'win32' ? ['/c', scriptPath] : [scriptPath];
      
      const result = await this.executeCommand(shell, args, {
        ...options,
        workingDirectory: environment.workingDirectory
      });

      return result;

    } finally {
      await environment.cleanup();
    }
  }

  /**
   * Kill all active processes
   */
  killAllProcesses(): void {
    for (const [id, process] of this.activeProcesses) {
      try {
        process.kill('SIGTERM');
        logWarn(`Killed process ${id}`);
      } catch (error) {
        logError(`Failed to kill process ${id}`, error instanceof Error ? error : new Error(String(error)));
      }
    }
    this.activeProcesses.clear();
  }

  /**
   * Cleanup all environments
   */
  async cleanup(): Promise<void> {
    // Kill all processes first
    this.killAllProcesses();

    // Cleanup all environments
    const cleanupPromises = Array.from(this.environments.values()).map(env => env.cleanup());
    await Promise.allSettled(cleanupPromises);

    this.environments.clear();
    logInfo('All sandbox environments cleaned up');
  }

  /**
   * Get active process count
   */
  getActiveProcessCount(): number {
    return this.activeProcesses.size;
  }

  /**
   * Get environment count
   */
  getEnvironmentCount(): number {
    return this.environments.size;
  }
}

// Global sandbox executor instance
export const sandboxExecutor = new SandboxExecutor();

/**
 * Execute code with automatic language detection
 */
export async function executeCode(
  code: string,
  language?: string,
  options: SandboxOptions = {}
): Promise<ExecutionResult> {
  // Auto-detect language if not provided
  if (!language) {
    language = detectLanguage(code);
  }

  switch (language.toLowerCase()) {
    case 'javascript':
    case 'js':
      return sandboxExecutor.executeJavaScript(code, options);
    
    case 'python':
    case 'py':
      return sandboxExecutor.executePython(code, options);
    
    case 'shell':
    case 'bash':
    case 'sh':
      return sandboxExecutor.executeShell(code, options);
    
    default:
      throw new Error(`Unsupported language: ${language}`);
  }
}

/**
 * Detect programming language from code
 */
function detectLanguage(code: string): string {
  const trimmed = code.trim();

  // Check for shebang
  if (trimmed.startsWith('#!')) {
    if (trimmed.includes('node')) return 'javascript';
    if (trimmed.includes('python')) return 'python';
    if (trimmed.includes('bash') || trimmed.includes('sh')) return 'shell';
  }

  // Check for language-specific patterns
  if (trimmed.includes('console.log') || trimmed.includes('require(') || trimmed.includes('import ')) {
    return 'javascript';
  }

  if (trimmed.includes('print(') || trimmed.includes('import ') || trimmed.includes('def ')) {
    return 'python';
  }

  if (trimmed.includes('echo ') || trimmed.includes('#!/bin/bash')) {
    return 'shell';
  }

  // Default to shell for simple commands
  return 'shell';
}

/**
 * Validate sandbox options
 */
export function validateSandboxOptions(options: SandboxOptions): string[] {
  const errors: string[] = [];

  if (options.timeout && options.timeout < 1000) {
    errors.push('Timeout must be at least 1000ms');
  }

  if (options.maxMemory && options.maxMemory < 1024 * 1024) {
    errors.push('Max memory must be at least 1MB');
  }

  if (options.workingDirectory && !options.workingDirectory.startsWith('/')) {
    if (process.platform !== 'win32') {
      errors.push('Working directory must be an absolute path');
    }
  }

  return errors;
}

/**
 * Create secure sandbox with resource limits
 */
export async function createSecureSandbox(options: SandboxOptions = {}): Promise<SandboxEnvironment> {
  const validationErrors = validateSandboxOptions(options);
  if (validationErrors.length > 0) {
    throw new Error(`Invalid sandbox options: ${validationErrors.join(', ')}`);
  }

  return sandboxExecutor.createEnvironment(options);
}

/**
 * Execute with resource monitoring
 */
export async function executeWithMonitoring(
  command: string,
  args: string[] = [],
  options: SandboxOptions = {}
): Promise<ExecutionResult & { resourceUsage: any }> {
  const startTime = Date.now();
  const startMemory = process.memoryUsage();

  const result = await sandboxExecutor.executeCommand(command, args, options);

  const endTime = Date.now();
  const endMemory = process.memoryUsage();

  return {
    ...result,
    resourceUsage: {
      duration: endTime - startTime,
      memoryDelta: {
        heapUsed: endMemory.heapUsed - startMemory.heapUsed,
        heapTotal: endMemory.heapTotal - startMemory.heapTotal,
        external: endMemory.external - startMemory.external
      }
    }
  };
}
