import type { ModelInfo, ProviderName } from '../types/index.js';
export declare const MODEL_DATABASE: Record<string, ModelInfo>;
export declare function getModelInfo(modelId: string): ModelInfo | null;
export declare function getModelsForProvider(provider: ProviderName): ModelInfo[];
export declare function getImageSupportedModels(): ModelInfo[];
export declare function getToolSupportedModels(): ModelInfo[];
export declare function getHighContextModels(): ModelInfo[];
export declare function searchModels(query: string): ModelInfo[];
export declare function getModelCapabilities(modelId: string): {
    hasImages: boolean;
    hasTools: boolean;
    contextSize: 'small' | 'medium' | 'large' | 'xlarge';
    provider: string;
} | null;
//# sourceMappingURL=model-info.d.ts.map