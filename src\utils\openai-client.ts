/**
 * OpenAI Client Factory
 * 
 * Creates provider-specific client instances with proper configuration
 * Handles both standard OpenAI and Azure OpenAI configurations
 */

import { OpenAI } from 'openai';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { getApiKey, getBaseUrl, loadConfig } from './config.js';
import { getProviderConfig } from './providers.js';
// Removed unused import - will be used in future provider type checking

interface ClientOptions {
  provider?: string;
  apiKey?: string;
  baseURL?: string;
  timeout?: number;
  organization?: string;
  project?: string;
}

/**
 * Create OpenAI client instance for specified provider
 */
export function createOpenAIClient(options: ClientOptions = {}): OpenAI {
  const config = loadConfig();
  const provider = options.provider || config.provider;
  
  // Get API key
  const apiKey = options.apiKey || getApiKey(provider);
  if (!apiKey) {
    throw new Error(`No API key found for provider: ${provider}. Please set the appropriate environment variable.`);
  }
  
  // Get base URL
  const baseURL = options.baseURL || getBaseUrl(provider);
  
  // Configure proxy if available
  const proxyUrl = process.env.HTTPS_PROXY || process.env.https_proxy;
  const httpAgent = proxyUrl ? new HttpsProxyAgent(proxyUrl) : undefined;
  
  // Create client configuration
  const clientConfig: ConstructorParameters<typeof OpenAI>[0] = {
    apiKey,
    baseURL,
    timeout: options.timeout || config.timeout || 30000,
    httpAgent,
  };
  
  // Add organization and project for OpenAI
  if (provider === 'openai') {
    if (options.organization || process.env.OPENAI_ORG_ID) {
      clientConfig.organization = options.organization || process.env.OPENAI_ORG_ID;
    }
    
    if (options.project || process.env.OPENAI_PROJECT_ID) {
      clientConfig.project = options.project || process.env.OPENAI_PROJECT_ID;
    }
  }
  
  // Handle Azure OpenAI specific configuration
  if (provider === 'azure') {
    const apiVersion = process.env.AZURE_OPENAI_API_VERSION || '2024-02-15-preview';
    clientConfig.defaultQuery = { 'api-version': apiVersion };
    clientConfig.defaultHeaders = {
      'api-key': apiKey,
    };
  }
  
  return new OpenAI(clientConfig);
}

/**
 * Test client connection
 */
export async function testClientConnection(
  provider: string = 'openai'
): Promise<{ success: boolean; error?: string }> {
  try {
    const client = createOpenAIClient({ provider });
    
    // Try to list models as a connection test
    await client.models.list();
    
    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get client with retry logic
 */
export async function createClientWithRetry(
  options: ClientOptions = {},
  maxRetries: number = 3
): Promise<OpenAI> {
  let lastError: Error | null = null;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const client = createOpenAIClient(options);
      
      // Test the connection
      await client.models.list();
      
      return client;
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      
      if (attempt < maxRetries) {
        // Wait before retry with exponential backoff
        const delay = Math.pow(2, attempt - 1) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw lastError || new Error('Failed to create client after retries');
}

/**
 * Create multiple clients for different providers
 */
export function createMultiProviderClients(
  providers: string[]
): Record<string, OpenAI> {
  const clients: Record<string, OpenAI> = {};
  
  for (const provider of providers) {
    try {
      clients[provider] = createOpenAIClient({ provider });
    } catch (error) {
      console.warn(`Warning: Could not create client for provider ${provider}:`, error);
    }
  }
  
  return clients;
}

/**
 * Get client configuration for debugging
 */
export function getClientConfig(provider: string = 'openai'): {
  provider: string;
  baseURL: string;
  hasApiKey: boolean;
  timeout: number;
} {
  const config = loadConfig();
  const baseURL = getBaseUrl(provider);
  const apiKey = getApiKey(provider);
  
  return {
    provider,
    baseURL,
    hasApiKey: !!apiKey,
    timeout: config.timeout || 30000
  };
}

/**
 * Validate client configuration
 */
export function validateClientConfig(provider: string = 'openai'): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  // Check if provider is supported
  const providerConfig = getProviderConfig(provider);
  if (!providerConfig) {
    errors.push(`Unsupported provider: ${provider}`);
  }
  
  // Check API key
  const apiKey = getApiKey(provider);
  if (!apiKey) {
    errors.push(`Missing API key for provider: ${provider}`);
  }
  
  // Check base URL
  const baseURL = getBaseUrl(provider);
  if (!baseURL) {
    errors.push(`Missing base URL for provider: ${provider}`);
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}
