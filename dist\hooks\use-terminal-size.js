import { useState, useEffect } from 'react';
const TERMINAL_PADDING_X = 4;
const TERMINAL_PADDING_Y = 2;
export function useTerminalSize() {
    const [size, setSize] = useState(() => {
        const columns = (process.stdout.columns || 80) - TERMINAL_PADDING_X;
        const rows = (process.stdout.rows || 24) - TERMINAL_PADDING_Y;
        return {
            columns: Math.max(columns, 40),
            rows: Math.max(rows, 10),
            width: columns,
            height: rows,
        };
    });
    useEffect(() => {
        const handleResize = () => {
            const columns = (process.stdout.columns || 80) - TERMINAL_PADDING_X;
            const rows = (process.stdout.rows || 24) - TERMINAL_PADDING_Y;
            setSize({
                columns: Math.max(columns, 40),
                rows: Math.max(rows, 10),
                width: columns,
                height: rows,
            });
        };
        process.stdout.on('resize', handleResize);
        return () => {
            process.stdout.off('resize', handleResize);
        };
    }, []);
    return size;
}
export function getTerminalSize() {
    const columns = (process.stdout.columns || 80) - TERMINAL_PADDING_X;
    const rows = (process.stdout.rows || 24) - TERMINAL_PADDING_Y;
    return {
        columns: Math.max(columns, 40),
        rows: Math.max(rows, 10),
        width: columns,
        height: rows,
    };
}
export function isTerminalSizeAdequate(minColumns = 60, minRows = 15) {
    const { columns, rows } = getTerminalSize();
    return columns >= minColumns && rows >= minRows;
}
export function getResponsiveLayout() {
    const { columns, rows } = getTerminalSize();
    const isSmall = columns < 80 || rows < 20;
    const isMedium = columns >= 80 && columns < 120 && rows >= 20;
    const isLarge = columns >= 120 && rows >= 30;
    let layout = 'normal';
    if (isSmall)
        layout = 'compact';
    else if (isLarge)
        layout = 'expanded';
    return {
        isSmall,
        isMedium,
        isLarge,
        layout,
    };
}
