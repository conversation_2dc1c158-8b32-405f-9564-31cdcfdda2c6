import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import { join } from 'path';
import { tmpdir } from 'os';
import { randomBytes } from 'crypto';
import { logInfo, logError, logWarn } from './logger/log.js';
export class SandboxExecutor {
    activeProcesses = new Map();
    environments = new Map();
    async createEnvironment(options = {}) {
        const id = randomBytes(16).toString('hex');
        const sandboxDir = join(tmpdir(), `sandbox-${id}`);
        try {
            await fs.mkdir(sandboxDir, { recursive: true });
            const environment = {
                id,
                workingDirectory: sandboxDir,
                cleanup: async () => {
                    try {
                        await fs.rm(sandboxDir, { recursive: true, force: true });
                        this.environments.delete(id);
                        logInfo(`Sandbox environment ${id} cleaned up`);
                    }
                    catch (error) {
                        logError(`Failed to cleanup sandbox ${id}`, error instanceof Error ? error : new Error(String(error)));
                    }
                }
            };
            this.environments.set(id, environment);
            logInfo(`Created sandbox environment ${id} at ${sandboxDir}`);
            return environment;
        }
        catch (error) {
            logError(`Failed to create sandbox environment`, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    async executeCommand(command, args = [], options = {}) {
        const startTime = Date.now();
        const timeout = options.timeout || 30000;
        const workingDirectory = options.workingDirectory || process.cwd();
        logInfo(`Executing command in sandbox: ${command} ${args.join(' ')}`);
        return new Promise((resolve) => {
            const child = spawn(command, args, {
                cwd: workingDirectory,
                env: {
                    ...process.env,
                    ...options.environment
                },
                stdio: ['pipe', 'pipe', 'pipe']
            });
            const processId = randomBytes(8).toString('hex');
            this.activeProcesses.set(processId, child);
            let stdout = '';
            let stderr = '';
            let killed = false;
            const timeoutHandle = setTimeout(() => {
                if (!killed) {
                    killed = true;
                    child.kill('SIGTERM');
                    setTimeout(() => {
                        if (!child.killed) {
                            child.kill('SIGKILL');
                        }
                    }, 5000);
                }
            }, timeout);
            child.stdout?.on('data', (data) => {
                stdout += data.toString();
            });
            child.stderr?.on('data', (data) => {
                stderr += data.toString();
            });
            child.on('close', (code, signal) => {
                clearTimeout(timeoutHandle);
                this.activeProcesses.delete(processId);
                const duration = Date.now() - startTime;
                const result = {
                    success: code === 0 && !killed,
                    stdout,
                    stderr,
                    exitCode: code,
                    signal,
                    duration
                };
                if (killed) {
                    result.error = 'Command timed out';
                    result.success = false;
                }
                logInfo(`Command completed in ${duration}ms with exit code ${code}`);
                resolve(result);
            });
            child.on('error', (error) => {
                clearTimeout(timeoutHandle);
                this.activeProcesses.delete(processId);
                const duration = Date.now() - startTime;
                const result = {
                    success: false,
                    stdout,
                    stderr,
                    exitCode: null,
                    signal: null,
                    duration,
                    error: error.message
                };
                logError(`Command failed: ${error.message}`);
                resolve(result);
            });
        });
    }
    async executeJavaScript(code, options = {}) {
        const environment = await this.createEnvironment(options);
        try {
            const scriptPath = join(environment.workingDirectory, 'script.js');
            await fs.writeFile(scriptPath, code);
            const result = await this.executeCommand('node', [scriptPath], {
                ...options,
                workingDirectory: environment.workingDirectory
            });
            return result;
        }
        finally {
            await environment.cleanup();
        }
    }
    async executePython(code, options = {}) {
        const environment = await this.createEnvironment(options);
        try {
            const scriptPath = join(environment.workingDirectory, 'script.py');
            await fs.writeFile(scriptPath, code);
            const result = await this.executeCommand('python', [scriptPath], {
                ...options,
                workingDirectory: environment.workingDirectory
            });
            return result;
        }
        finally {
            await environment.cleanup();
        }
    }
    async executeShell(script, options = {}) {
        const environment = await this.createEnvironment(options);
        try {
            const scriptPath = join(environment.workingDirectory, 'script.sh');
            await fs.writeFile(scriptPath, script);
            await fs.chmod(scriptPath, 0o755);
            const shell = process.platform === 'win32' ? 'cmd' : 'bash';
            const args = process.platform === 'win32' ? ['/c', scriptPath] : [scriptPath];
            const result = await this.executeCommand(shell, args, {
                ...options,
                workingDirectory: environment.workingDirectory
            });
            return result;
        }
        finally {
            await environment.cleanup();
        }
    }
    killAllProcesses() {
        for (const [id, process] of this.activeProcesses) {
            try {
                process.kill('SIGTERM');
                logWarn(`Killed process ${id}`);
            }
            catch (error) {
                logError(`Failed to kill process ${id}`, error instanceof Error ? error : new Error(String(error)));
            }
        }
        this.activeProcesses.clear();
    }
    async cleanup() {
        this.killAllProcesses();
        const cleanupPromises = Array.from(this.environments.values()).map(env => env.cleanup());
        await Promise.allSettled(cleanupPromises);
        this.environments.clear();
        logInfo('All sandbox environments cleaned up');
    }
    getActiveProcessCount() {
        return this.activeProcesses.size;
    }
    getEnvironmentCount() {
        return this.environments.size;
    }
}
export const sandboxExecutor = new SandboxExecutor();
export async function executeCode(code, language, options = {}) {
    if (!language) {
        language = detectLanguage(code);
    }
    switch (language.toLowerCase()) {
        case 'javascript':
        case 'js':
            return sandboxExecutor.executeJavaScript(code, options);
        case 'python':
        case 'py':
            return sandboxExecutor.executePython(code, options);
        case 'shell':
        case 'bash':
        case 'sh':
            return sandboxExecutor.executeShell(code, options);
        default:
            throw new Error(`Unsupported language: ${language}`);
    }
}
function detectLanguage(code) {
    const trimmed = code.trim();
    if (trimmed.startsWith('#!')) {
        if (trimmed.includes('node'))
            return 'javascript';
        if (trimmed.includes('python'))
            return 'python';
        if (trimmed.includes('bash') || trimmed.includes('sh'))
            return 'shell';
    }
    if (trimmed.includes('console.log') || trimmed.includes('require(') || trimmed.includes('import ')) {
        return 'javascript';
    }
    if (trimmed.includes('print(') || trimmed.includes('import ') || trimmed.includes('def ')) {
        return 'python';
    }
    if (trimmed.includes('echo ') || trimmed.includes('#!/bin/bash')) {
        return 'shell';
    }
    return 'shell';
}
export function validateSandboxOptions(options) {
    const errors = [];
    if (options.timeout && options.timeout < 1000) {
        errors.push('Timeout must be at least 1000ms');
    }
    if (options.maxMemory && options.maxMemory < 1024 * 1024) {
        errors.push('Max memory must be at least 1MB');
    }
    if (options.workingDirectory && !options.workingDirectory.startsWith('/')) {
        if (process.platform !== 'win32') {
            errors.push('Working directory must be an absolute path');
        }
    }
    return errors;
}
export async function createSecureSandbox(options = {}) {
    const validationErrors = validateSandboxOptions(options);
    if (validationErrors.length > 0) {
        throw new Error(`Invalid sandbox options: ${validationErrors.join(', ')}`);
    }
    return sandboxExecutor.createEnvironment(options);
}
export async function executeWithMonitoring(command, args = [], options = {}) {
    const startTime = Date.now();
    const startMemory = process.memoryUsage();
    const result = await sandboxExecutor.executeCommand(command, args, options);
    const endTime = Date.now();
    const endMemory = process.memoryUsage();
    return {
        ...result,
        resourceUsage: {
            duration: endTime - startTime,
            memoryDelta: {
                heapUsed: endMemory.heapUsed - startMemory.heapUsed,
                heapTotal: endMemory.heapTotal - startMemory.heapTotal,
                external: endMemory.external - startMemory.external
            }
        }
    };
}
