/**
 * Select Input Component
 * 
 * Provides a keyboard-navigable selection interface with visual indicators
 * Supports single and multi-select modes with customizable styling
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Text } from 'ink';

export interface SelectOption {
  label: string;
  value: string;
  description?: string;
  disabled?: boolean;
}

export interface SelectInputProps {
  options: SelectOption[];
  value?: string | string[];
  onChange: (value: string | string[]) => void;
  onSubmit?: (value: string | string[]) => void;
  multiple?: boolean;
  placeholder?: string;
  disabled?: boolean;
  focus?: boolean;
  maxHeight?: number;
  showIndicators?: boolean;
  indicatorSelected?: string;
  indicatorUnselected?: string;
  indicatorFocus?: string;
}

/**
 * Select input component with keyboard navigation
 */
export function SelectInput({
  options,
  value,
  onChange,
  onSubmit,
  multiple = false,
  placeholder = 'Select an option...',
  disabled = false,
  focus = false,
  maxHeight = 10,
  showIndicators = true,
  indicatorSelected = '●',
  indicatorUnselected = '○',
  indicatorFocus = '▶',
}: SelectInputProps) {
  const [focusedIndex, setFocusedIndex] = useState(0);
  const [isFocused, setIsFocused] = useState(focus);
  const [selectedValues, setSelectedValues] = useState<string[]>(() => {
    if (multiple) {
      return Array.isArray(value) ? value : value ? [value] : [];
    }
    return value ? [value as string] : [];
  });

  // Update selected values when value prop changes
  useEffect(() => {
    if (multiple) {
      setSelectedValues(Array.isArray(value) ? value : value ? [value] : []);
    } else {
      setSelectedValues(value ? [value as string] : []);
    }
  }, [value, multiple]);

  // Update focus state
  useEffect(() => {
    setIsFocused(focus);
  }, [focus]);

  /**
   * Handle key press events
   */
  const handleKeyPress = useCallback((ch: string, key: any) => {
    if (disabled || !isFocused) return;

    // Navigation
    if (key.name === 'up' || key.name === 'k') {
      setFocusedIndex(prev => Math.max(0, prev - 1));
      return;
    }

    if (key.name === 'down' || key.name === 'j') {
      setFocusedIndex(prev => Math.min(options.length - 1, prev + 1));
      return;
    }

    // Selection
    if (key.name === 'space') {
      const option = options[focusedIndex];
      if (option && !option.disabled) {
        if (multiple) {
          const newSelected = selectedValues.includes(option.value)
            ? selectedValues.filter(v => v !== option.value)
            : [...selectedValues, option.value];
          setSelectedValues(newSelected);
          onChange(newSelected);
        } else {
          const newSelected = [option.value];
          setSelectedValues(newSelected);
          onChange(option.value);
        }
      }
      return;
    }

    // Submit
    if (key.name === 'return' || key.name === 'enter') {
      if (onSubmit) {
        onSubmit(multiple ? selectedValues : selectedValues[0] || '');
      }
      return;
    }

    // Quick selection by typing
    if (ch && ch.match(/[a-zA-Z0-9]/)) {
      const matchingIndex = options.findIndex(option => 
        option.label.toLowerCase().startsWith(ch.toLowerCase())
      );
      if (matchingIndex !== -1) {
        setFocusedIndex(matchingIndex);
      }
    }
  }, [disabled, isFocused, focusedIndex, options, selectedValues, multiple, onChange, onSubmit]);

  /**
   * Get display options with pagination
   */
  const getDisplayOptions = () => {
    if (options.length <= maxHeight) {
      return { options, startIndex: 0 };
    }

    const halfHeight = Math.floor(maxHeight / 2);
    let startIndex = Math.max(0, focusedIndex - halfHeight);
    let endIndex = startIndex + maxHeight;

    if (endIndex > options.length) {
      endIndex = options.length;
      startIndex = Math.max(0, endIndex - maxHeight);
    }

    return {
      options: options.slice(startIndex, endIndex),
      startIndex,
    };
  };

  /**
   * Render option with indicators
   */
  const renderOption = (option: SelectOption, index: number, globalIndex: number) => {
    const isSelected = selectedValues.includes(option.value);
    const isFocusedOption = globalIndex === focusedIndex;
    const isDisabled = option.disabled;

    let indicator = '';
    if (showIndicators) {
      if (isFocusedOption) {
        indicator = indicatorFocus + ' ';
      } else {
        indicator = '  ';
      }

      if (multiple) {
        indicator += isSelected ? indicatorSelected : indicatorUnselected;
        indicator += ' ';
      }
    }

    const textColor = isDisabled ? 'gray' : 
                     isFocusedOption ? 'cyan' : 
                     isSelected ? 'green' : 'white';

    const backgroundColor = isFocusedOption ? 'blue' : undefined;

    return (
      <Box key={option.value}>
        <Text color={textColor} backgroundColor={backgroundColor}>
          {indicator}{option.label}
        </Text>
        {option.description && (
          <Text color="gray" dimColor> - {option.description}</Text>
        )}
      </Box>
    );
  };

  // Set up keyboard handling
  useEffect(() => {
    if (isFocused && typeof process !== 'undefined' && process.stdin) {
      process.stdin.on('keypress', handleKeyPress);
      
      return () => {
        process.stdin.off('keypress', handleKeyPress);
      };
    }
  }, [isFocused, handleKeyPress]);

  const { options: displayOptions, startIndex } = getDisplayOptions();

  if (options.length === 0) {
    return (
      <Box>
        <Text dimColor>{placeholder}</Text>
      </Box>
    );
  }

  return (
    <Box flexDirection="column">
      {displayOptions.map((option, index) => 
        renderOption(option, index, startIndex + index)
      )}
      
      {options.length > maxHeight && (
        <Box marginTop={1}>
          <Text dimColor>
            Showing {startIndex + 1}-{startIndex + displayOptions.length} of {options.length}
            {focusedIndex < startIndex && ' (↑ more above)'}
            {focusedIndex >= startIndex + displayOptions.length && ' (↓ more below)'}
          </Text>
        </Box>
      )}
      
      {multiple && selectedValues.length > 0 && (
        <Box marginTop={1}>
          <Text color="green">
            Selected: {selectedValues.length} item{selectedValues.length !== 1 ? 's' : ''}
          </Text>
        </Box>
      )}
    </Box>
  );
}

/**
 * Simple option creator helper
 */
export function createOption(
  label: string, 
  value?: string, 
  description?: string,
  disabled = false
): SelectOption {
  return {
    label,
    value: value || label,
    description,
    disabled,
  };
}

/**
 * Create options from array of strings
 */
export function createOptionsFromStrings(items: string[]): SelectOption[] {
  return items.map(item => createOption(item));
}
