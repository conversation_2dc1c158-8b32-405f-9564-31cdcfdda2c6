/**
 * Session Browser Component
 * 
 * Interactive browser for saved conversation sessions
 * Supports session loading, deletion, and export
 */

import React, { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
// Removed unused chalk import
import {
  listSessions,
  deleteSession,
  getSessionStats,
  formatFileSize
} from '../../utils/storage/save-rollout.js';
import type { SessionMetadata } from '../../types/index.js';

interface SessionBrowserProps {
  onLoad: (sessionId: string) => void;
  onCancel: () => void;
}

export function SessionBrowser({ onLoad, onCancel }: SessionBrowserProps) {
  const [sessions, setSessions] = useState<SessionMetadata[]>([]);
  const [selectedIndex, setSelectedIndex] = useState<number>(0);
  const [mode, setMode] = useState<'browse' | 'confirm-delete'>('browse');
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Load sessions on mount
  useEffect(() => {
    const loadSessions = async () => {
      try {
        setLoading(true);
        const sessionList = listSessions();
        const sessionStats = getSessionStats();
        
        setSessions(sessionList);
        setStats(sessionStats);
      } catch (error) {
        console.error('Failed to load sessions:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSessions();
  }, []);

  // Handle keyboard input
  useInput((input, key) => {
    if (key.escape) {
      if (mode === 'confirm-delete') {
        setMode('browse');
      } else {
        onCancel();
      }
      return;
    }

    if (key.return) {
      const selectedSession = sessions[selectedIndex];
      if (!selectedSession) return;

      if (mode === 'confirm-delete') {
        // Confirm deletion
        const success = deleteSession(selectedSession.id);
        if (success) {
          // Remove from list
          setSessions(prev => prev.filter((_, index) => index !== selectedIndex));
          // Adjust selected index
          setSelectedIndex(prev => Math.min(prev, sessions.length - 2));
        }
        setMode('browse');
      } else {
        // Load session
        onLoad(selectedSession.id);
      }
      return;
    }

    if (key.upArrow) {
      setSelectedIndex(prev => Math.max(0, prev - 1));
      return;
    }

    if (key.downArrow) {
      setSelectedIndex(prev => Math.min(sessions.length - 1, prev + 1));
      return;
    }

    if (input === 'd' && mode === 'browse') {
      setMode('confirm-delete');
      return;
    }

    if (input === 'r' && mode === 'browse') {
      // Refresh sessions
      const sessionList = listSessions();
      setSessions(sessionList);
      return;
    }
  });

  const formatTimestamp = (timestamp: number): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffHours < 1) return 'less than 1h ago';
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return date.toLocaleDateString();
  };

  const getProviderIcon = (provider: string): string => {
    const icons: Record<string, string> = {
      'openai': '🤖',
      'anthropic': '🧠',
      'gemini': '💎',
      'mistral': '🌪️',
      'deepseek': '🔍',
      'xai': '🚀',
      'groq': '⚡',
      'ollama': '🦙'
    };
    return icons[provider.toLowerCase()] || '🤖';
  };

  if (loading) {
    return (
      <Box justifyContent="center" alignItems="center" height={10}>
        <Text color="yellow">Loading sessions...</Text>
      </Box>
    );
  }

  return (
    <Box flexDirection="column" padding={1}>
      {/* Header */}
      <Box borderStyle="single" paddingX={1}>
        <Text bold color="blue">Session Browser</Text>
      </Box>

      {/* Stats */}
      {stats && (
        <Box>
          <Text>
            Total Sessions: <Text color="cyan">{stats.totalSessions}</Text>
            {stats.totalSize > 0 && (
              <>
                {' • '}
                Size: <Text color="yellow">{formatFileSize(stats.totalSize)}</Text>
              </>
            )}
          </Text>
        </Box>
      )}

      {/* Session list */}
      <Box flexDirection="column" height={12}>
        {sessions.length === 0 ? (
          <Box justifyContent="center" alignItems="center" height="100%">
            <Text color="gray">No saved sessions found</Text>
          </Box>
        ) : (
          sessions.slice(0, 12).map((session, index) => {
            const isSelected = index === selectedIndex;
            const providerIcon = getProviderIcon(session.provider);
            const timestamp = formatTimestamp(session.timestamp);

            return (
              <Box key={session.id} flexDirection="column">
                <Box>
                  <Text color={isSelected ? 'cyan' : undefined}>
                    {isSelected ? '▶ ' : '  '}
                    {providerIcon}
                    {' '}
                    <Text bold={isSelected}>
                      {session.provider}/{session.model}
                    </Text>
                    <Text color="gray"> • {session.itemCount} items</Text>
                  </Text>
                </Box>
                {isSelected && (
                  <Box flexDirection="column">
                    <Text color="gray">
                      ID: {session.id}
                    </Text>
                    <Text color="gray">
                      Created: {timestamp}
                    </Text>
                    <Text color="gray">
                      Last Activity: {formatTimestamp(session.lastActivity)}
                    </Text>
                  </Box>
                )}
              </Box>
            );
          })
        )}
      </Box>

      {/* Show more indicator */}
      {sessions.length > 12 && (
        <Box justifyContent="center">
          <Text color="gray">
            ... and {sessions.length - 12} more sessions
          </Text>
        </Box>
      )}

      {/* Selected session details */}
      {sessions[selectedIndex] && (
        <Box borderStyle="single" paddingX={1}>
          <Box flexDirection="column">
            <Text bold color="cyan">Selected Session:</Text>
            <Text>
              <Text color="yellow">{sessions[selectedIndex].provider}</Text>
              /
              <Text color="green">{sessions[selectedIndex].model}</Text>
              {' • '}
              <Text color="gray">{sessions[selectedIndex].itemCount} items</Text>
            </Text>
          </Box>
        </Box>
      )}

      {/* Confirmation dialog */}
      {mode === 'confirm-delete' && sessions[selectedIndex] && (
        <Box borderStyle="single" paddingX={1}>
          <Box flexDirection="column">
            <Text bold color="red">⚠️ Confirm Deletion</Text>
            <Text color="yellow">
              Delete session: {sessions[selectedIndex].id}?
            </Text>
            <Text color="gray">
              This action cannot be undone.
            </Text>
          </Box>
        </Box>
      )}

      {/* Instructions */}
      <Box borderStyle="single" paddingX={1}>
        <Box flexDirection="column">
          {mode === 'confirm-delete' ? (
            <>
              <Text color="red">
                Enter Confirm deletion • Esc Cancel
              </Text>
            </>
          ) : (
            <>
              <Text color="gray">
                ↑/↓ Navigate • Enter Load • D Delete • R Refresh • Esc Cancel
              </Text>
              {sessions[selectedIndex] && (
                <Text color="cyan">
                  Press Enter to load session: {sessions[selectedIndex].id}
                </Text>
              )}
            </>
          )}
        </Box>
      </Box>
    </Box>
  );
}
