# Publishing Guide for Kritrima AI CLI

This guide covers how to build and publish the Kritrima AI CLI to npm for cross-platform compatibility.

## 🚀 Quick Publish

For immediate publishing:

```bash
# 1. Login to npm
npm login

# 2. Run comprehensive tests and build
npm run publish:test

# 3. Publish to npm
npm publish --access public
```

## 🔧 Build Process

### Local Development Build

```bash
# Clean build
npm run build

# Test the build
npm run test:build

# Cross-platform testing
npm run test:platforms
```

### Production Build

```bash
# Full production build with all checks
npm run prepublishOnly
```

## 🌍 Platform Support

The CLI is tested and supports:

- ✅ **Windows 11** (including WSL)
- ✅ **macOS** (Intel & Apple Silicon)
- ✅ **Linux** (Ubuntu, Debian, CentOS, etc.)

### Platform-Specific Features

#### Windows
- Automatic path conversion for ESM compatibility
- WSL support with proper binary handling
- PowerShell and Command Prompt compatibility

#### macOS
- Executable permissions automatically set
- Support for both Intel and Apple Silicon
- Native terminal integration

#### Linux
- Full POSIX compliance
- Executable permissions handled correctly
- Distribution-agnostic compatibility

## 📦 Package Structure

The published package includes:

```
kritrima-ai/
├── bin/
│   └── kritrima-ai.js          # Cross-platform binary entry point
├── dist/                       # Compiled TypeScript output
│   ├── cli.js                  # Main CLI entry
│   ├── components/             # React UI components
│   ├── utils/                  # Core utilities
│   └── types/                  # Type definitions
├── package.json                # Package metadata
├── README.md                   # Documentation
└── LICENSE                     # MIT License
```

## 🔄 CI/CD Pipeline

### GitHub Actions

The project includes automated CI/CD:

```yaml
# .github/workflows/publish.yml
- Cross-platform testing (Windows, macOS, Linux)
- Node.js version matrix (18, 20, 22)
- Automated npm publishing on tag push
- GitHub release creation
```

### Manual Workflow Trigger

You can manually trigger publishing:

1. Go to GitHub Actions
2. Select "Build and Publish to NPM"
3. Click "Run workflow"
4. Enter version number (e.g., 1.0.1)

## 📋 Pre-Publish Checklist

Before publishing, ensure:

- [ ] All tests pass: `npm test`
- [ ] Linting passes: `npm run lint`
- [ ] Build succeeds: `npm run build`
- [ ] CLI works: `npm run test:build`
- [ ] Cross-platform tests pass: `npm run test:platforms`
- [ ] Version updated in package.json
- [ ] CHANGELOG.md updated
- [ ] README.md is current

## 🔐 NPM Configuration

### Required NPM Token

Set up npm authentication:

```bash
# Login to npm
npm login

# Or set token directly
npm config set //registry.npmjs.org/:_authToken YOUR_TOKEN
```

### Package Access

The package is published with public access:

```bash
npm publish --access public
```

## 🏷️ Version Management

### Semantic Versioning

Follow semantic versioning:

- **Patch** (1.0.1): Bug fixes
- **Minor** (1.1.0): New features, backward compatible
- **Major** (2.0.0): Breaking changes

### Version Update Commands

```bash
# Patch version
npm version patch

# Minor version
npm version minor

# Major version
npm version major

# Specific version
npm version 1.2.3
```

## 🧪 Testing Installation

### Local Testing

```bash
# Create test package
npm pack

# Install globally from package
npm install -g kritrima-ai-1.0.0.tgz

# Test installation
kritrima-ai --help
```

### Clean Test Environment

```bash
# Test in Docker container
docker run -it node:18 bash
npm install -g kritrima-ai
kritrima-ai --version
```

## 🚨 Troubleshooting

### Common Issues

#### Windows Path Issues
- Ensure proper file:// URL conversion in bin/kritrima-ai.js
- Check executable permissions handling

#### Permission Errors
- Run postinstall script to set permissions
- Verify chmod operations work cross-platform

#### Module Resolution
- Ensure TypeScript moduleResolution is set to "bundler"
- Check ESM imports are properly handled

### Debug Commands

```bash
# Verbose npm install
npm install -g kritrima-ai --verbose

# Check package contents
npm pack --dry-run

# Verify binary works
node bin/kritrima-ai.js --help
```

## 📊 Package Analytics

After publishing, monitor:

- Download statistics on npmjs.com
- GitHub repository insights
- User feedback and issues
- Platform-specific usage patterns

## 🔄 Update Process

For updates:

1. Make changes
2. Update version: `npm version patch`
3. Run tests: `npm run test:platforms`
4. Publish: `npm publish`
5. Create GitHub release
6. Update documentation

## 📞 Support

For publishing issues:

- Check GitHub Actions logs
- Review npm publish output
- Verify package.json configuration
- Test locally before publishing
