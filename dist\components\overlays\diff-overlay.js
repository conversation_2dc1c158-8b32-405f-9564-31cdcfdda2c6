import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput } from 'ink';
import { getGitDiff } from '../../utils/get-diff.js';
import { logInfo, logError } from '../../utils/logger/log.js';
export function DiffOverlay({ onClose, visible, workingDirectory = process.cwd() }) {
    const [diffContent, setDiffContent] = useState('');
    const [diffFiles, setDiffFiles] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [scrollOffset, setScrollOffset] = useState(0);
    const [selectedFileIndex, setSelectedFileIndex] = useState(0);
    const [viewMode, setViewMode] = useState('summary');
    useEffect(() => {
        if (visible) {
            loadDiff();
        }
    }, [visible, workingDirectory]);
    useInput((input, key) => {
        if (!visible)
            return;
        if (key.escape) {
            onClose();
            return;
        }
        if (key.upArrow) {
            if (viewMode === 'summary') {
                setSelectedFileIndex(Math.max(0, selectedFileIndex - 1));
            }
            else {
                setScrollOffset(Math.max(0, scrollOffset - 1));
            }
            return;
        }
        if (key.downArrow) {
            if (viewMode === 'summary') {
                setSelectedFileIndex(Math.min(diffFiles.length - 1, selectedFileIndex + 1));
            }
            else {
                setScrollOffset(scrollOffset + 1);
            }
            return;
        }
        if (key.pageUp) {
            setScrollOffset(Math.max(0, scrollOffset - 10));
            return;
        }
        if (key.pageDown) {
            setScrollOffset(scrollOffset + 10);
            return;
        }
        if (key.tab) {
            setViewMode(viewMode === 'summary' ? 'full' : 'summary');
            setScrollOffset(0);
            return;
        }
        if (input === 'r' || input === 'R') {
            loadDiff();
            return;
        }
        if (key.return && viewMode === 'summary' && diffFiles.length > 0) {
            setViewMode('full');
            setScrollOffset(0);
            return;
        }
    });
    const loadDiff = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            logInfo('Loading git diff', { workingDirectory });
            const diffResult = await getGitDiff(workingDirectory);
            if (!diffResult.success) {
                setError(diffResult.error || 'Failed to get git diff');
                return;
            }
            setDiffContent(diffResult.diff);
            const files = parseDiffContent(diffResult.diff);
            setDiffFiles(files);
            setSelectedFileIndex(0);
            logInfo(`Git diff loaded - ${files.length} files changed`);
        }
        catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to load git diff';
            setError(errorMessage);
            logError('Failed to load git diff', err instanceof Error ? err : new Error(errorMessage));
        }
        finally {
            setLoading(false);
        }
    }, [workingDirectory]);
    const parseDiffContent = (diff) => {
        const files = [];
        const lines = diff.split('\n');
        let currentFile = null;
        let currentContent = [];
        for (const line of lines) {
            if (line.startsWith('diff --git')) {
                if (currentFile) {
                    files.push({
                        ...currentFile,
                        content: currentContent.join('\n')
                    });
                }
                const match = line.match(/diff --git a\/(.+) b\/(.+)/);
                if (match) {
                    currentFile = {
                        path: match[1],
                        status: 'modified',
                        additions: 0,
                        deletions: 0
                    };
                    currentContent = [];
                }
            }
            else if (line.startsWith('new file mode')) {
                if (currentFile)
                    currentFile.status = 'added';
            }
            else if (line.startsWith('deleted file mode')) {
                if (currentFile)
                    currentFile.status = 'deleted';
            }
            else if (line.startsWith('rename from')) {
                if (currentFile)
                    currentFile.status = 'renamed';
            }
            else if (line.startsWith('+') && !line.startsWith('+++')) {
                if (currentFile)
                    currentFile.additions = (currentFile.additions || 0) + 1;
            }
            else if (line.startsWith('-') && !line.startsWith('---')) {
                if (currentFile)
                    currentFile.deletions = (currentFile.deletions || 0) + 1;
            }
            currentContent.push(line);
        }
        if (currentFile) {
            files.push({
                ...currentFile,
                content: currentContent.join('\n')
            });
        }
        return files;
    };
    const renderSummary = () => {
        if (diffFiles.length === 0) {
            return (_jsx(Box, { flexDirection: "column", children: _jsx(Text, { color: "gray", children: "No changes detected" }) }));
        }
        return (_jsxs(Box, { flexDirection: "column", children: [_jsxs(Text, { color: "blue", bold: true, children: ["Files Changed (", diffFiles.length, "):"] }), _jsx(Text, {}), diffFiles.map((file, index) => {
                    const isSelected = index === selectedFileIndex;
                    const statusColor = getStatusColor(file.status);
                    const statusIcon = getStatusIcon(file.status);
                    return (_jsx(Box, { children: _jsxs(Text, { color: isSelected ? 'black' : statusColor, backgroundColor: isSelected ? 'cyan' : undefined, bold: isSelected, children: [isSelected ? '► ' : '  ', statusIcon, " ", file.path, file.additions > 0 && (_jsxs(Text, { color: "green", children: [" +", file.additions] })), file.deletions > 0 && (_jsxs(Text, { color: "red", children: [" -", file.deletions] }))] }) }, file.path));
                })] }));
    };
    const renderFullDiff = () => {
        if (!diffContent) {
            return (_jsx(Box, { flexDirection: "column", children: _jsx(Text, { color: "gray", children: "No diff content available" }) }));
        }
        const lines = diffContent.split('\n');
        const visibleLines = lines.slice(scrollOffset, scrollOffset + 20);
        return (_jsxs(Box, { flexDirection: "column", children: [visibleLines.map((line, index) => {
                    let color = 'white';
                    if (line.startsWith('+')) {
                        color = 'green';
                    }
                    else if (line.startsWith('-')) {
                        color = 'red';
                    }
                    else if (line.startsWith('@@')) {
                        color = 'cyan';
                    }
                    else if (line.startsWith('diff --git')) {
                        color = 'yellow';
                    }
                    return (_jsx(Text, { color: color, children: line }, index));
                }), scrollOffset + 20 < lines.length && (_jsxs(Text, { color: "gray", dimColor: true, children: ["... ", lines.length - scrollOffset - 20, " more lines"] }))] }));
    };
    const getStatusColor = (status) => {
        switch (status) {
            case 'added': return 'green';
            case 'deleted': return 'red';
            case 'modified': return 'yellow';
            case 'renamed': return 'blue';
            default: return 'white';
        }
    };
    const getStatusIcon = (status) => {
        switch (status) {
            case 'added': return '+';
            case 'deleted': return '-';
            case 'modified': return '~';
            case 'renamed': return '→';
            default: return '•';
        }
    };
    if (!visible) {
        return null;
    }
    return (_jsxs(Box, { position: "absolute", borderStyle: "double", borderColor: "cyan", flexDirection: "column", width: "90%", height: "80%", children: [_jsxs(Box, { paddingX: 2, paddingY: 1, borderStyle: "single", borderColor: "gray", children: [_jsxs(Text, { color: "cyan", bold: true, children: ["Git Diff - ", viewMode === 'summary' ? 'Summary' : 'Full Diff'] }), _jsx(Box, { children: _jsx(Text, { color: "gray", children: "Tab: Toggle view \u2022 \u2191\u2193: Navigate \u2022 R: Refresh \u2022 Esc: Close" }) })] }), _jsx(Box, { flexGrow: 1, paddingX: 2, paddingY: 1, children: loading ? (_jsx(Text, { color: "yellow", children: "Loading git diff..." })) : error ? (_jsxs(Box, { flexDirection: "column", children: [_jsxs(Text, { color: "red", children: ["Error: ", error] }), _jsx(Text, { color: "gray", children: "Press R to retry" })] })) : viewMode === 'summary' ? (renderSummary()) : (renderFullDiff()) }), _jsx(Box, { paddingX: 2, paddingY: 1, borderStyle: "single", borderColor: "gray", children: _jsxs(Text, { color: "gray", children: ["Working Directory: ", workingDirectory] }) })] }));
}
