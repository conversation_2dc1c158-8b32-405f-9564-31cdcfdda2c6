{"name": "kritrima-ai", "version": "1.0.0", "description": "Sophisticated AI-powered CLI assistant with multi-provider support, autonomous agent loop, and advanced code assistance", "main": "dist/cli.js", "type": "module", "bin": {"kritrima-ai": "./bin/kritrima-ai.js"}, "files": ["dist/**/*", "bin/**/*", "README.md", "LICENSE"], "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "npm run clean && tsc && npm run build:fix-permissions", "build:fix-permissions": "node -e \"const fs = require('fs'); const path = require('path'); const binPath = path.join('bin', 'kritrima-ai.js'); if (fs.existsSync(binPath)) { fs.chmodSync(binPath, '755'); }\"", "dev": "tsx src/cli.tsx", "start": "node dist/cli.js", "test": "vitest", "test:ui": "vitest --ui", "test:build": "npm run build && node bin/kritrima-ai.js --help", "test:platforms": "node scripts/build-and-test.js", "lint": "eslint src/**/*.{ts,tsx}", "format": "prettier --write src/**/*.{ts,tsx}", "prepublishOnly": "npm run lint && npm run build && npm run test:build", "publish:test": "npm run test:platforms && npm pack", "install:fix": "node scripts/install-fix.js", "install:auto": "node scripts/install-fix.js --auto-install", "postinstall": "node -e \"const fs = require('fs'); const path = require('path'); const binPath = path.join(__dirname, 'bin', 'kritrima-ai.js'); if (fs.existsSync(binPath)) { try { fs.chmodSync(binPath, '755'); console.log('✅ Kritrima AI CLI installed successfully!'); console.log('Run: kritrima-ai --help to get started'); } catch (e) { /* ignore chmod errors on Windows */ } }\""}, "keywords": ["ai", "cli", "assistant", "openai", "gpt", "coding", "automation", "terminal", "agent", "typescript", "react", "ink"], "author": "Kritrima AI", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/kritrima-ai/kritrima-ai-cli.git"}, "bugs": {"url": "https://github.com/kritrima-ai/kritrima-ai-cli/issues"}, "homepage": "https://github.com/kritrima-ai/kritrima-ai-cli#readme", "os": ["darwin", "linux", "win32"], "dependencies": {"blessed": "^0.1.81", "chalk": "^5.3.0", "chokidar": "^4.0.1", "commander": "^12.1.0", "diff": "^7.0.0", "dotenv": "^16.4.5", "fs-extra": "^11.2.0", "glob": "^11.0.0", "highlight.js": "^11.10.0", "https-proxy-agent": "^7.0.5", "ink": "^5.0.1", "inquirer": "^12.0.0", "marked": "^14.1.2", "node-notifier": "^10.0.1", "openai": "^4.67.3", "ora": "^8.1.0", "react": "^18.3.1", "semver": "^7.6.3", "yaml": "^2.6.0", "zod": "^3.23.8"}, "devDependencies": {"@types/blessed": "^0.1.25", "@types/diff": "^5.2.3", "@types/fs-extra": "^11.0.4", "@types/node": "^22.7.4", "@types/react": "^18.3.11", "@types/semver": "^7.5.8", "@typescript-eslint/eslint-plugin": "^8.7.0", "@typescript-eslint/parser": "^8.7.0", "@vitest/ui": "^2.1.1", "eslint": "^9.11.1", "prettier": "^3.3.3", "rimraf": "^6.0.1", "tsx": "^4.19.1", "typescript": "^5.6.2", "vitest": "^2.1.1"}, "engines": {"node": ">=18.0.0"}}