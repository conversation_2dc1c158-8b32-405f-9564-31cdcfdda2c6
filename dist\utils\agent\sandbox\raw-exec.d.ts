import type { ExecInput, ExecResult, AppConfig } from '../../../types/index.js';
export declare function exec(input: ExecInput, config: AppConfig): Promise<ExecResult>;
export declare function isCommandSafe(command: string[], config: AppConfig): boolean;
export declare function getExecutionEnvironment(): {
    platform: string;
    shell: string;
    sandboxing: boolean;
    restrictions: string[];
};
export declare function testExecution(): Promise<{
    success: boolean;
    capabilities: string[];
    limitations: string[];
}>;
//# sourceMappingURL=raw-exec.d.ts.map