import { readFileSync, existsSync, statSync } from 'fs';
import { extname } from 'path';
const SUPPORTED_IMAGE_FORMATS = ['.png', '.jpg', '.jpeg', '.webp', '.gif'];
const MAX_IMAGE_SIZE = 5 * 1024 * 1024;
export async function createInputItem(text, contentOrImagePaths = []) {
    const content = [];
    if (text.trim()) {
        content.push({
            type: 'input_text',
            text: text.trim()
        });
    }
    if (Array.isArray(contentOrImagePaths)) {
        if (contentOrImagePaths.length > 0) {
            const firstItem = contentOrImagePaths[0];
            if (typeof firstItem === 'string') {
                const imagePaths = contentOrImagePaths;
                for (const imagePath of imagePaths) {
                    await processImagePath(imagePath, content);
                }
            }
            else {
                const messageContent = contentOrImagePaths;
                for (const item of messageContent) {
                    if (item.type === 'input_text') {
                        content.push({
                            type: 'input_text',
                            text: item.text
                        });
                    }
                    else if (item.type === 'image') {
                        content.push({
                            type: 'image',
                        });
                    }
                }
            }
        }
    }
    return {
        type: 'input',
        role: 'user',
        content,
        timestamp: Date.now()
    };
}
async function processImagePath(imagePath, content) {
    try {
        const imageData = await processImage(imagePath);
        content.push({
            type: 'input_image',
            image: imageData
        });
    }
    catch (error) {
        console.warn(`Warning: Could not process image ${imagePath}:`, error);
        content.push({
            type: 'input_text',
            text: `[Error: Could not load image ${imagePath}]`
        });
    }
}
async function processImage(imagePath) {
    if (!existsSync(imagePath)) {
        throw new Error(`Image file not found: ${imagePath}`);
    }
    const ext = extname(imagePath).toLowerCase();
    if (!SUPPORTED_IMAGE_FORMATS.includes(ext)) {
        throw new Error(`Unsupported image format: ${ext}. Supported formats: ${SUPPORTED_IMAGE_FORMATS.join(', ')}`);
    }
    const stats = statSync(imagePath);
    if (stats.size > MAX_IMAGE_SIZE) {
        throw new Error(`Image file too large: ${stats.size} bytes. Maximum size: ${MAX_IMAGE_SIZE} bytes`);
    }
    const imageBuffer = readFileSync(imagePath);
    const base64Data = imageBuffer.toString('base64');
    const mimeType = getMimeType(ext);
    const dataUrl = `data:${mimeType};base64,${base64Data}`;
    let detail = 'auto';
    if (stats.size < 512 * 1024) {
        detail = 'low';
    }
    else if (stats.size > 2 * 1024 * 1024) {
        detail = 'high';
    }
    return {
        url: dataUrl,
        detail
    };
}
function getMimeType(ext) {
    const mimeTypes = {
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.webp': 'image/webp',
        '.gif': 'image/gif'
    };
    return mimeTypes[ext.toLowerCase()] || 'image/jpeg';
}
export function extractImagePaths(text) {
    const imagePattern = /@([^\s]+\.(png|jpg|jpeg|webp|gif))/gi;
    const imagePaths = [];
    const cleanText = text.replace(imagePattern, (match, path) => {
        imagePaths.push(path);
        return `[Image: ${path}]`;
    });
    return { cleanText, imagePaths };
}
export function validateImagePaths(imagePaths) {
    const valid = [];
    const invalid = [];
    for (const path of imagePaths) {
        try {
            if (existsSync(path)) {
                const stats = statSync(path);
                if (stats.isFile()) {
                    const ext = extname(path).toLowerCase();
                    if (SUPPORTED_IMAGE_FORMATS.includes(ext)) {
                        if (stats.size <= MAX_IMAGE_SIZE) {
                            valid.push(path);
                        }
                        else {
                            invalid.push(`${path} (too large: ${stats.size} bytes)`);
                        }
                    }
                    else {
                        invalid.push(`${path} (unsupported format: ${ext})`);
                    }
                }
                else {
                    invalid.push(`${path} (not a file)`);
                }
            }
            else {
                invalid.push(`${path} (not found)`);
            }
        }
        catch (error) {
            invalid.push(`${path} (access error: ${error})`);
        }
    }
    return { valid, invalid };
}
export function getImageInfo(imagePath) {
    try {
        if (!existsSync(imagePath)) {
            return { exists: false };
        }
        const stats = statSync(imagePath);
        const ext = extname(imagePath).toLowerCase();
        const supported = SUPPORTED_IMAGE_FORMATS.includes(ext);
        return {
            exists: true,
            size: stats.size,
            format: ext,
            supported
        };
    }
    catch (error) {
        return { exists: false };
    }
}
export function formatFileSize(bytes) {
    if (bytes === 0)
        return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
export function createSystemMessage(content) {
    return {
        role: 'system',
        content: [{
                type: 'input_text',
                text: content
            }],
        type: 'message',
        timestamp: Date.now()
    };
}
export function createAssistantMessage(content) {
    return {
        role: 'assistant',
        content: [{
                type: 'input_text',
                text: content
            }],
        type: 'message',
        timestamp: Date.now()
    };
}
