import { z } from 'zod';
import { writeFileSync, readFileSync, unlinkSync, renameSync, existsSync } from 'fs';
import { dirname, resolve, join } from 'path';
import { mkdirSync } from 'fs';
import { logInfo, logError } from '../logger/log.js';
export const FileOperationSchema = z.object({
    path: z.string(),
    updated_full_content: z.string().nullable().optional(),
    delete: z.boolean().nullable().optional(),
    move_to: z.string().nullable().optional(),
});
export async function executeFileOperations(operations, context) {
    const results = [];
    const backups = [];
    try {
        for (const operation of operations) {
            const validation = FileOperationSchema.safeParse(operation);
            if (!validation.success) {
                throw new Error(`Invalid operation for ${operation.path}: ${validation.error.message}`);
            }
        }
        for (const operation of operations) {
            const result = await executeFileOperation(operation, context);
            results.push(result);
            if (!result.success) {
                throw new Error(`Operation failed for ${operation.path}: ${result.error}`);
            }
            if (result.backup) {
                backups.push({ original: operation.path, backup: result.backup });
            }
        }
        logInfo('File operations completed successfully', {
            operationCount: operations.length,
            backupCount: backups.length
        });
        return { success: true, results };
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logError('File operations failed, rolling back', new Error(errorMessage));
        await rollbackOperations(results, backups);
        return {
            success: false,
            results,
            error: errorMessage
        };
    }
}
async function executeFileOperation(operation, context) {
    const fullPath = resolve(context.rootPath, operation.path);
    try {
        if (operation.delete) {
            return await deleteFile(fullPath, context);
        }
        else if (operation.move_to) {
            return await moveFile(fullPath, resolve(context.rootPath, operation.move_to), context);
        }
        else if (operation.updated_full_content !== undefined) {
            const exists = existsSync(fullPath);
            return await writeFile(fullPath, operation.updated_full_content || '', context, exists ? 'update' : 'create');
        }
        else {
            throw new Error('No valid operation specified');
        }
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return {
            success: false,
            path: operation.path,
            operation: 'update',
            error: errorMessage
        };
    }
}
async function writeFile(filePath, content, context, operation) {
    let backup;
    try {
        if (context.createBackups && existsSync(filePath)) {
            backup = await createBackup(filePath, context);
        }
        const dir = dirname(filePath);
        if (!existsSync(dir)) {
            mkdirSync(dir, { recursive: true });
        }
        if (!context.dryRun) {
            writeFileSync(filePath, content, 'utf-8');
        }
        logInfo(`File ${operation}d`, { path: filePath, backup });
        return {
            success: true,
            path: filePath,
            operation,
            backup
        };
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return {
            success: false,
            path: filePath,
            operation,
            error: errorMessage,
            backup
        };
    }
}
async function deleteFile(filePath, context) {
    let backup;
    try {
        if (!existsSync(filePath)) {
            return {
                success: true,
                path: filePath,
                operation: 'delete'
            };
        }
        if (context.createBackups) {
            backup = await createBackup(filePath, context);
        }
        if (!context.dryRun) {
            unlinkSync(filePath);
        }
        logInfo('File deleted', { path: filePath, backup });
        return {
            success: true,
            path: filePath,
            operation: 'delete',
            backup
        };
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return {
            success: false,
            path: filePath,
            operation: 'delete',
            error: errorMessage,
            backup
        };
    }
}
async function moveFile(fromPath, toPath, context) {
    let backup;
    try {
        if (!existsSync(fromPath)) {
            throw new Error('Source file does not exist');
        }
        if (context.createBackups && existsSync(toPath)) {
            backup = await createBackup(toPath, context);
        }
        const dir = dirname(toPath);
        if (!existsSync(dir)) {
            mkdirSync(dir, { recursive: true });
        }
        if (!context.dryRun) {
            renameSync(fromPath, toPath);
        }
        logInfo('File moved', { from: fromPath, to: toPath, backup });
        return {
            success: true,
            path: fromPath,
            operation: 'move',
            backup
        };
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return {
            success: false,
            path: fromPath,
            operation: 'move',
            error: errorMessage,
            backup
        };
    }
}
async function createBackup(filePath, context) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = context.backupDir || join(context.rootPath, '.kritrima-backups');
    const backupName = `${timestamp}-${filePath.replace(/[/\\]/g, '_')}`;
    const backupPath = join(backupDir, backupName);
    if (!existsSync(backupDir)) {
        mkdirSync(backupDir, { recursive: true });
    }
    const content = readFileSync(filePath);
    writeFileSync(backupPath, content);
    return backupPath;
}
async function rollbackOperations(results, backups) {
    try {
        for (const { original, backup } of backups) {
            if (existsSync(backup)) {
                const content = readFileSync(backup);
                writeFileSync(original, content);
                unlinkSync(backup);
            }
        }
        for (const result of results) {
            if (result.success && result.operation === 'create' && existsSync(result.path)) {
                unlinkSync(result.path);
            }
        }
        logInfo('Operations rolled back successfully');
    }
    catch (error) {
        logError('Failed to rollback operations', error instanceof Error ? error : new Error(String(error)));
    }
}
