import { statSync, readdirSync } from 'fs';
import { join, relative, extname } from 'path';
import { logInfo, logError } from '../logger/log.js';
export function computeSizeMap(root, files) {
    const fileSizeMap = {};
    const dirSizeMap = {};
    for (const file of files) {
        const relativePath = relative(root, file.path);
        fileSizeMap[relativePath] = file.size;
    }
    const directories = new Set();
    for (const file of files) {
        const relativePath = relative(root, file.path);
        const pathParts = relativePath.split('/');
        for (let i = 1; i <= pathParts.length; i++) {
            const dirPath = pathParts.slice(0, i).join('/');
            if (dirPath) {
                directories.add(dirPath);
            }
        }
    }
    for (const dir of directories) {
        let totalSize = 0;
        for (const [filePath, size] of Object.entries(fileSizeMap)) {
            if (filePath.startsWith(dir + '/') || filePath === dir) {
                totalSize += size;
            }
        }
        dirSizeMap[dir] = totalSize;
    }
    return [fileSizeMap, dirSizeMap];
}
export function analyzeDirectory(rootPath) {
    const stats = {
        totalFiles: 0,
        totalSize: 0,
        textFiles: 0,
        binaryFiles: 0,
        largestFile: { path: '', size: 0 },
        filesByExtension: {}
    };
    try {
        walkDirectory(rootPath, (filePath, stat) => {
            stats.totalFiles++;
            stats.totalSize += stat.size;
            if (stat.size > stats.largestFile.size) {
                stats.largestFile = { path: relative(rootPath, filePath), size: stat.size };
            }
            const ext = extname(filePath).toLowerCase();
            stats.filesByExtension[ext] = (stats.filesByExtension[ext] || 0) + 1;
            if (isTextFile(filePath)) {
                stats.textFiles++;
            }
            else {
                stats.binaryFiles++;
            }
        });
    }
    catch (error) {
        logError('Failed to analyze directory', error instanceof Error ? error : new Error(String(error)));
    }
    return stats;
}
export function optimizeForContextLimit(files, options) {
    const selectedFiles = [];
    const excludedFiles = [];
    let totalSize = 0;
    const prioritizedFiles = prioritizeFiles(files, options);
    for (const file of prioritizedFiles) {
        if (file.size > options.maxFileSize) {
            excludedFiles.push({
                path: file.path,
                reason: `File too large (${file.size} > ${options.maxFileSize})`,
                size: file.size
            });
            continue;
        }
        const estimatedSize = totalSize + file.size;
        if (estimatedSize > options.maxTotalSize) {
            excludedFiles.push({
                path: file.path,
                reason: `Would exceed total size limit (${estimatedSize} > ${options.maxTotalSize})`,
                size: file.size
            });
            continue;
        }
        if (shouldExcludeFile(file.path, options.excludePatterns)) {
            excludedFiles.push({
                path: file.path,
                reason: 'Matches exclude pattern',
                size: file.size
            });
            continue;
        }
        if (options.includeExtensions.length > 0) {
            const ext = extname(file.path).toLowerCase();
            if (!options.includeExtensions.includes(ext)) {
                excludedFiles.push({
                    path: file.path,
                    reason: 'Extension not in include list',
                    size: file.size
                });
                continue;
            }
        }
        selectedFiles.push(file);
        totalSize += file.size;
    }
    const compressionEstimate = Math.round(totalSize * options.compressionRatio);
    logInfo('Context optimization completed', {
        totalFiles: files.length,
        selectedFiles: selectedFiles.length,
        excludedFiles: excludedFiles.length,
        totalSize,
        compressionEstimate
    });
    return {
        selectedFiles,
        excludedFiles,
        totalSize,
        compressionEstimate
    };
}
function prioritizeFiles(files, options) {
    return files.sort((a, b) => {
        const aPriority = getPriorityScore(a.path, options.priorityDirectories);
        const bPriority = getPriorityScore(b.path, options.priorityDirectories);
        if (aPriority !== bPriority) {
            return bPriority - aPriority;
        }
        return a.size - b.size;
    });
}
function getPriorityScore(filePath, priorityDirectories) {
    for (let i = 0; i < priorityDirectories.length; i++) {
        if (filePath.startsWith(priorityDirectories[i])) {
            return priorityDirectories.length - i;
        }
    }
    return 0;
}
function shouldExcludeFile(filePath, excludePatterns) {
    for (const pattern of excludePatterns) {
        if (filePath.includes(pattern)) {
            return true;
        }
    }
    return false;
}
function isTextFile(filePath) {
    const textExtensions = [
        '.txt', '.md', '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.c', '.cpp',
        '.h', '.hpp', '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
        '.html', '.css', '.scss', '.sass', '.less', '.xml', '.json', '.yaml', '.yml',
        '.toml', '.ini', '.cfg', '.conf', '.sh', '.bash', '.zsh', '.fish', '.ps1',
        '.sql', '.r', '.m', '.pl', '.lua', '.vim', '.dockerfile', '.makefile'
    ];
    const ext = extname(filePath).toLowerCase();
    return textExtensions.includes(ext);
}
function walkDirectory(dirPath, callback, maxDepth = 10, currentDepth = 0) {
    if (currentDepth >= maxDepth)
        return;
    try {
        const entries = readdirSync(dirPath);
        for (const entry of entries) {
            const fullPath = join(dirPath, entry);
            const stat = statSync(fullPath);
            if (stat.isFile()) {
                callback(fullPath, stat);
            }
            else if (stat.isDirectory() && !entry.startsWith('.')) {
                walkDirectory(fullPath, callback, maxDepth, currentDepth + 1);
            }
        }
    }
    catch (error) {
    }
}
export function getDefaultContextLimitOptions() {
    return {
        maxTotalSize: 1024 * 1024,
        maxFileSize: 100 * 1024,
        excludePatterns: [
            'node_modules',
            '.git',
            'dist',
            'build',
            '.next',
            '.nuxt',
            'coverage',
            '.nyc_output',
            'logs',
            '*.log',
            '*.tmp',
            '*.cache'
        ],
        includeExtensions: [],
        priorityDirectories: [
            'src',
            'lib',
            'app',
            'components',
            'utils',
            'services'
        ],
        compressionRatio: 0.7
    };
}
