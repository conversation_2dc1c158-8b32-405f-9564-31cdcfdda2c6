import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput } from 'ink';
import { getSavedSessions, loadSession, deleteSession } from '../../utils/storage/save-rollout.js';
export function SessionsOverlay({ onLoadSession, onClose, visible }) {
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [sessions, setSessions] = useState([]);
    const [viewMode, setViewMode] = useState('list');
    const [previewData, setPreviewData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    useEffect(() => {
        if (visible) {
            loadSessions();
        }
    }, [visible]);
    const loadSessions = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const sessionList = await getSavedSessions();
            setSessions(sessionList.sort((a, b) => b.lastActivity - a.lastActivity));
            setSelectedIndex(0);
        }
        catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load sessions');
            setSessions([]);
        }
        finally {
            setLoading(false);
        }
    }, []);
    const loadPreview = useCallback(async (sessionId) => {
        setLoading(true);
        setError(null);
        try {
            const sessionData = await loadSession(sessionId);
            setPreviewData(sessionData);
            setViewMode('preview');
        }
        catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load session preview');
        }
        finally {
            setLoading(false);
        }
    }, []);
    const handleDeleteSession = useCallback(async (sessionId) => {
        setLoading(true);
        setError(null);
        try {
            await deleteSession(sessionId);
            await loadSessions();
            setViewMode('list');
        }
        catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to delete session');
        }
        finally {
            setLoading(false);
        }
    }, [loadSessions]);
    useInput((input, key) => {
        if (!visible)
            return;
        if (key.escape) {
            if (viewMode !== 'list') {
                setViewMode('list');
                setPreviewData(null);
                setError(null);
            }
            else {
                onClose();
            }
            return;
        }
        if (viewMode === 'list') {
            if (key.upArrow) {
                setSelectedIndex(Math.max(0, selectedIndex - 1));
                return;
            }
            if (key.downArrow) {
                setSelectedIndex(Math.min(sessions.length - 1, selectedIndex + 1));
                return;
            }
            if (key.pageUp) {
                setSelectedIndex(Math.max(0, selectedIndex - 10));
                return;
            }
            if (key.pageDown) {
                setSelectedIndex(Math.min(sessions.length - 1, selectedIndex + 10));
                return;
            }
            if (key.return) {
                const selectedSession = sessions[selectedIndex];
                if (selectedSession) {
                    loadPreview(selectedSession.id);
                }
                return;
            }
            if (input === 'l' || input === 'L') {
                const selectedSession = sessions[selectedIndex];
                if (selectedSession) {
                    loadPreview(selectedSession.id);
                }
                return;
            }
            if (input === 'd' || input === 'D') {
                if (sessions[selectedIndex]) {
                    setViewMode('confirm-delete');
                }
                return;
            }
            if (input === 'r' || input === 'R') {
                loadSessions();
                return;
            }
        }
        else if (viewMode === 'preview') {
            if (key.return) {
                if (previewData) {
                    onLoadSession(previewData);
                    onClose();
                }
                return;
            }
        }
        else if (viewMode === 'confirm-delete') {
            if (input === 'y' || input === 'Y') {
                const selectedSession = sessions[selectedIndex];
                if (selectedSession) {
                    handleDeleteSession(selectedSession.id);
                }
                return;
            }
            if (input === 'n' || input === 'N') {
                setViewMode('list');
                return;
            }
        }
    });
    const formatTimestamp = useCallback((timestamp) => {
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffHours / 24);
        if (diffHours < 1) {
            return 'Just now';
        }
        else if (diffHours < 24) {
            return `${diffHours}h ago`;
        }
        else if (diffDays < 7) {
            return `${diffDays}d ago`;
        }
        else {
            return date.toLocaleDateString();
        }
    }, []);
    const formatDuration = useCallback((start, end) => {
        const durationMs = end - start;
        const minutes = Math.floor(durationMs / (1000 * 60));
        const hours = Math.floor(minutes / 60);
        if (hours > 0) {
            return `${hours}h ${minutes % 60}m`;
        }
        else {
            return `${minutes}m`;
        }
    }, []);
    if (!visible) {
        return null;
    }
    return (_jsxs(Box, { position: "absolute", borderStyle: "double", borderColor: "cyan", flexDirection: "column", children: [_jsxs(Box, { paddingX: 2, paddingY: 1, borderStyle: "single", borderColor: "gray", children: [_jsx(Text, { color: "cyan", bold: true, children: "Session Management" }), _jsx(Box, { children: _jsxs(Text, { color: "gray", children: [viewMode === 'list' && `${sessions.length} sessions available`, viewMode === 'preview' && 'Session Preview', viewMode === 'confirm-delete' && 'Confirm Deletion'] }) })] }), _jsx(Box, { flexGrow: 1, paddingX: 2, paddingY: 1, children: loading ? (_jsx(Text, { color: "yellow", children: "Loading..." })) : error ? (_jsx(Text, { color: "red", children: error })) : viewMode === 'list' ? (_jsx(Box, { flexDirection: "column", children: sessions.length === 0 ? (_jsx(Text, { color: "gray", children: "No saved sessions found." })) : (sessions.map((session, index) => {
                        const isSelected = index === selectedIndex;
                        const duration = formatDuration(session.timestamp, session.lastActivity);
                        return (_jsxs(Box, { children: [_jsx(Box, { width: 3, children: _jsx(Text, { color: isSelected ? "black" : "gray", backgroundColor: isSelected ? "cyan" : undefined, children: isSelected ? '►' : ' ' }) }), _jsx(Box, { width: 12, children: _jsx(Text, { color: isSelected ? "black" : "gray", backgroundColor: isSelected ? "cyan" : undefined, children: formatTimestamp(session.lastActivity) }) }), _jsx(Box, { width: 15, children: _jsxs(Text, { color: isSelected ? "black" : "blue", backgroundColor: isSelected ? "cyan" : undefined, children: [session.provider, "/", session.model] }) }), _jsx(Box, { width: 8, children: _jsxs(Text, { color: isSelected ? "black" : "gray", backgroundColor: isSelected ? "cyan" : undefined, children: [session.itemCount, " msgs"] }) }), _jsx(Box, { width: 8, children: _jsx(Text, { color: isSelected ? "black" : "gray", backgroundColor: isSelected ? "cyan" : undefined, children: duration }) }), _jsx(Box, { flexGrow: 1, children: _jsxs(Text, { color: isSelected ? "black" : "white", backgroundColor: isSelected ? "cyan" : undefined, bold: isSelected, children: [session.id.substring(0, 8), "..."] }) })] }, session.id));
                    })) })) : viewMode === 'preview' ? (_jsx(Box, { flexDirection: "column", children: previewData && (_jsxs(_Fragment, { children: [_jsx(Text, { color: "blue", bold: true, children: "Session Details:" }), _jsxs(Text, { children: ["ID: ", previewData.metadata.id] }), _jsxs(Text, { children: ["Provider: ", previewData.metadata.provider] }), _jsxs(Text, { children: ["Model: ", previewData.metadata.model] }), _jsxs(Text, { children: ["Messages: ", previewData.metadata.itemCount] }), _jsxs(Text, { children: ["Created: ", new Date(previewData.metadata.timestamp).toLocaleString()] }), _jsxs(Text, { children: ["Last Activity: ", new Date(previewData.metadata.lastActivity).toLocaleString()] }), _jsx(Text, { color: "blue", bold: true, children: "Recent Messages:" }), previewData.items.slice(-3).map((item, index) => (_jsxs(Box, { children: [_jsxs(Text, { color: "gray", children: [item.role === 'user' ? '👤' : '🤖', " ", item.type, ":"] }), _jsx(Text, { children: item.type === 'message'
                                            ? item.content.map(c => c.type === 'input_text' ? c.text : '[Image]').join(' ')
                                            : item.type === 'output'
                                                ? item.content
                                                : `${item.name || 'Tool'}(${item.arguments || item.result || ''})` })] }, index)))] })) })) : viewMode === 'confirm-delete' ? (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: "red", bold: true, children: "Delete Session?" }), _jsx(Text, { children: "Are you sure you want to delete this session?" }), _jsxs(Text, { color: "gray", children: ["Session: ", sessions[selectedIndex]?.id.substring(0, 16), "..."] }), _jsx(Text, { color: "yellow", children: "This action cannot be undone." })] })) : null }), _jsx(Box, { paddingX: 2, paddingY: 1, borderStyle: "single", borderColor: "gray", children: _jsxs(Text, { color: "gray", children: [viewMode === 'list' && 'Enter/L: Preview • D: Delete • R: Refresh • Esc: Close', viewMode === 'preview' && 'Enter: Load Session • Esc: Back', viewMode === 'confirm-delete' && 'Y: Confirm • N: Cancel'] }) })] }));
}
