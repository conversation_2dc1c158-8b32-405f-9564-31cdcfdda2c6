import { writeFileSync, existsSync, mkdirSync, readdirSync, readFileSync, unlinkSync } from 'fs';
import { join, dirname } from 'path';
import { homedir } from 'os';
const SESSIONS_DIR = join(homedir(), '.kritrima-ai', 'sessions');
const MAX_SESSIONS = 100;
export function saveRollout(sessionId, items, config) {
    saveRolloutAsync(sessionId, items, config).catch(() => {
    });
}
export async function saveRolloutAsync(sessionId, items, config) {
    try {
        if (!existsSync(SESSIONS_DIR)) {
            mkdirSync(SESSIONS_DIR, { recursive: true });
        }
        const metadata = {
            id: sessionId,
            timestamp: Date.now(),
            model: config?.model || 'unknown',
            provider: config?.provider || 'unknown',
            itemCount: items.length,
            lastActivity: Date.now()
        };
        const sessionData = {
            metadata,
            items,
            config: config || {}
        };
        const timestamp = new Date().toISOString().split('T')[0];
        const filename = `rollout-${timestamp}-${sessionId}.json`;
        const filepath = join(SESSIONS_DIR, filename);
        writeFileSync(filepath, JSON.stringify(sessionData, null, 2));
        await cleanupOldSessions();
    }
    catch (error) {
        console.warn('Warning: Could not save session rollout:', error);
    }
}
export function loadSession(sessionId) {
    try {
        if (!existsSync(SESSIONS_DIR)) {
            return null;
        }
        const files = readdirSync(SESSIONS_DIR);
        const sessionFile = files.find(file => file.includes(sessionId));
        if (!sessionFile) {
            return null;
        }
        const filepath = join(SESSIONS_DIR, sessionFile);
        const content = readFileSync(filepath, 'utf-8');
        const sessionData = JSON.parse(content);
        return sessionData;
    }
    catch (error) {
        console.warn(`Warning: Could not load session ${sessionId}:`, error);
        return null;
    }
}
export async function getSavedSessions() {
    return listSessions();
}
export function listSessions() {
    try {
        if (!existsSync(SESSIONS_DIR)) {
            return [];
        }
        const files = readdirSync(SESSIONS_DIR);
        const sessions = [];
        for (const file of files) {
            if (!file.startsWith('rollout-') || !file.endsWith('.json')) {
                continue;
            }
            try {
                const filepath = join(SESSIONS_DIR, file);
                const content = readFileSync(filepath, 'utf-8');
                const sessionData = JSON.parse(content);
                sessions.push(sessionData.metadata);
            }
            catch (error) {
                continue;
            }
        }
        sessions.sort((a, b) => b.timestamp - a.timestamp);
        return sessions;
    }
    catch (error) {
        console.warn('Warning: Could not list sessions:', error);
        return [];
    }
}
export function deleteSession(sessionId) {
    try {
        if (!existsSync(SESSIONS_DIR)) {
            return false;
        }
        const files = readdirSync(SESSIONS_DIR);
        const sessionFile = files.find(file => file.includes(sessionId));
        if (!sessionFile) {
            return false;
        }
        const filepath = join(SESSIONS_DIR, sessionFile);
        unlinkSync(filepath);
        return true;
    }
    catch (error) {
        console.warn(`Warning: Could not delete session ${sessionId}:`, error);
        return false;
    }
}
export function getSessionStats() {
    try {
        const sessions = listSessions();
        if (sessions.length === 0) {
            return { totalSessions: 0, totalSize: 0 };
        }
        let totalSize = 0;
        if (existsSync(SESSIONS_DIR)) {
            const files = readdirSync(SESSIONS_DIR);
            for (const file of files) {
                if (file.startsWith('rollout-') && file.endsWith('.json')) {
                    try {
                        const filepath = join(SESSIONS_DIR, file);
                        const content = readFileSync(filepath, 'utf-8');
                        totalSize += content.length;
                    }
                    catch (error) {
                    }
                }
            }
        }
        const timestamps = sessions.map(s => s.timestamp);
        const oldestTimestamp = Math.min(...timestamps);
        const newestTimestamp = Math.max(...timestamps);
        return {
            totalSessions: sessions.length,
            totalSize,
            oldestSession: new Date(oldestTimestamp),
            newestSession: new Date(newestTimestamp)
        };
    }
    catch (error) {
        console.warn('Warning: Could not get session stats:', error);
        return { totalSessions: 0, totalSize: 0 };
    }
}
async function cleanupOldSessions() {
    try {
        const sessions = listSessions();
        if (sessions.length <= MAX_SESSIONS) {
            return;
        }
        const sortedSessions = [...sessions].sort((a, b) => a.timestamp - b.timestamp);
        const sessionsToDelete = sortedSessions.slice(0, sessions.length - MAX_SESSIONS);
        for (const session of sessionsToDelete) {
            deleteSession(session.id);
        }
    }
    catch (error) {
        console.warn('Warning: Could not cleanup old sessions:', error);
    }
}
export function exportSession(sessionId, outputPath) {
    try {
        const sessionData = loadSession(sessionId);
        if (!sessionData) {
            return false;
        }
        const outputDir = dirname(outputPath);
        if (!existsSync(outputDir)) {
            mkdirSync(outputDir, { recursive: true });
        }
        writeFileSync(outputPath, JSON.stringify(sessionData, null, 2));
        return true;
    }
    catch (error) {
        console.warn(`Warning: Could not export session ${sessionId}:`, error);
        return false;
    }
}
export async function importSession(inputPath) {
    try {
        if (!existsSync(inputPath)) {
            return null;
        }
        const content = readFileSync(inputPath, 'utf-8');
        const sessionData = JSON.parse(content);
        const newSessionId = generateSessionId();
        sessionData.metadata.id = newSessionId;
        sessionData.metadata.timestamp = Date.now();
        await saveRolloutAsync(newSessionId, sessionData.items, sessionData.config);
        return newSessionId;
    }
    catch (error) {
        console.warn(`Warning: Could not import session from ${inputPath}:`, error);
        return null;
    }
}
function generateSessionId() {
    return Math.random().toString(36).substring(2, 15) +
        Math.random().toString(36).substring(2, 15);
}
export function formatFileSize(bytes) {
    if (bytes === 0)
        return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
