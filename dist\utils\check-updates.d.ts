interface UpdateInfo {
    hasUpdate: boolean;
    currentVersion: string;
    latestVersion?: string;
    updateCommand?: string;
    releaseNotes?: string;
}
interface LastUpdateCheck {
    timestamp: number;
    lastCheckedVersion: string;
    latestVersion?: string;
}
export declare function checkForUpdates(): Promise<UpdateInfo | null>;
export declare function forceCheckForUpdates(): Promise<UpdateInfo>;
export declare function getLastUpdateCheck(): LastUpdateCheck | null;
export declare function clearUpdateCheckCache(): void;
export declare function disableUpdateChecks(): void;
export {};
//# sourceMappingURL=check-updates.d.ts.map