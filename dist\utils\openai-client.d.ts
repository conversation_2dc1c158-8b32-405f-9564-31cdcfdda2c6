import { OpenAI } from 'openai';
interface ClientOptions {
    provider?: string;
    apiKey?: string;
    baseURL?: string;
    timeout?: number;
    organization?: string;
    project?: string;
}
export declare function createOpenAIClient(options?: ClientOptions): OpenAI;
export declare function testClientConnection(provider?: string): Promise<{
    success: boolean;
    error?: string;
}>;
export declare function createClientWithRetry(options?: ClientOptions, maxRetries?: number): Promise<OpenAI>;
export declare function createMultiProviderClients(providers: string[]): Record<string, OpenAI>;
export declare function getClientConfig(provider?: string): {
    provider: string;
    baseURL: string;
    hasApiKey: boolean;
    timeout: number;
};
export declare function validateClientConfig(provider?: string): {
    valid: boolean;
    errors: string[];
};
export {};
//# sourceMappingURL=openai-client.d.ts.map