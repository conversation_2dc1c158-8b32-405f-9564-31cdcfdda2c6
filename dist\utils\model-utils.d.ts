import type { ModelInfo } from '../types/index.js';
export declare function fetchModels(provider: string): Promise<string[]>;
export declare function validateModel(model: string, provider: string): Promise<boolean>;
export declare function getModelInfo(model: string, provider: string): ModelInfo;
export declare function estimateTokens(text: string): number;
export declare function checkContextLimit(content: string, model: string, provider: string, buffer?: number): {
    fits: boolean;
    tokens: number;
    limit: number;
};
export declare function getRecommendedModels(provider: string): string[];
export declare function clearModelCache(): void;
export declare function getCachedModels(provider: string): string[] | null;
//# sourceMappingURL=model-utils.d.ts.map